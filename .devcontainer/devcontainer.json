{"name": "Equtum Batch Development", "dockerComposeFile": "../docker/docker-compose.dev.yml", "service": "app", "workspaceFolder": "/workspaces/equtum-batch", "customizations": {"vscode": {"extensions": ["ms-vscode.vscode-typescript-next", "ms-vscode.vscode-json", "biomejs.biome", "prisma.prisma", "ms-vscode.vscode-docker", "amazonwebservices.aws-toolkit-vscode", "ms-azuretools.vscode-docker", "GitHub.copilot", "GitHub.copilot-chat", "ms-vscode.remote-containers"], "settings": {"editor.formatOnSave": true, "editor.defaultFormatter": "biomejs.biome", "editor.codeActionsOnSave": {"quickfix.biome": true, "source.organizeImports.biome": true}, "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "files.associations": {"*.prisma": "prisma"}, "editor.rulers": [100], "editor.tabSize": 2, "editor.insertSpaces": true, "terminal.integrated.defaultProfile.linux": "bash", "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}}}}, "portsAttributes": {"3307": {"label": "MySQL", "onAutoForward": "notify"}}, "postCreateCommand": "npm install && npm run prisma:generate", "remoteUser": "root", "features": {"ghcr.io/devcontainers/features/git:1": {}, "ghcr.io/devcontainers/features/github-cli:1": {}, "ghcr.io/devcontainers/features/terraform:1": {"version": "latest"}}, "forwardPorts": [3307]}