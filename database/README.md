# equtum-database

equtum-database is a repository managing the database.

## Create migration files

Install golang-migrate.
ref: https://github.com/golang-migrate/migrate/tree/master/cmd/migrate#installation

```sh
brew install golang-migrate
```

Create migration files.

```sh
migrate create -ext sql -dir db/migrations -seq [name]
```

Edit `[version]_[name].up.sql` and `[version]_[name].down.sql`.

For more information, check the golang-migrate documents.

https://github.com/golang-migrate/migrate

## Migration

### Examples

```
migrate -database "mysql://docker:passwd@tcp(db.equtum-api-devcontainer.orb.local:3306)/equtum" -path db/migrations up
migrate -database "mysql://docker:passwd@tcp(db.equtum-analytics.orb.local:3306)/equtum" -path db/migrations up
migrate -database "mysql://root@tcp(db.equtum-crawler.orb.local:3306)/equtum" -path db/migrations up
```

Seeding data form dump file.

```
mysql -ppasswd -u docker --host db.equtum-api-devcontainer.orb.local --port 3306 equtum < dump/local_db.sql
mysql -ppasswd -u docker --host db.equtum-analytics.orb.local --port 3306 equtum < dump/local_db.sql
```

## Format migration files

https://github.com/sql-formatter-org/sql-formatter

```
npx sql-formatter --fix db/migrations/xxxxx.sql --config .sql-formatter.json
```

or on VSCode,
⌘ + Shift + P -> Run Task -> format-sql
