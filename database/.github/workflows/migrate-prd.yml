name: migrate-prd

on:
  push:
    tags:
      - '*'

concurrency:
  group: ${{ github.ref }}
  cancel-in-progress: true

jobs:
  migrate-prd:
    runs-on: ubuntu-latest
    environment:
      name: prd
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - id: auth
        name: Authenticate
        uses: google-github-actions/auth@v1
        with:
          create_credentials_file: true
          workload_identity_provider: ${{ secrets.GCP_WORKLOAD_IDENTITY_PROVIDER }}
          service_account: ${{ secrets.GCP_SERVICE_ACCOUNT }}

      - name: Setup Cloud SQL Auth Proxy
        env:
          CREDENTIALS_FILE_PATH: ${{ steps.auth.outputs.credentials_file_path }}
        run: |
          wget https://dl.google.com/cloudsql/cloud_sql_proxy.linux.amd64 -O cloud_sql_proxy
          chmod +x cloud_sql_proxy
          ./cloud_sql_proxy -credential_file=${CREDENTIALS_FILE_PATH} -instances=equtum-production:asia-northeast1:equtum=tcp:3306 &

      - name: Setup golang-migrate
        run: |
          curl -L https://github.com/golang-migrate/migrate/releases/download/v4.15.2/migrate.linux-amd64.tar.gz | tar xvz

      - name: Run migration up
        env:
          MYSQL_USER: ${{ secrets.MYSQL_USER }}
          MYSQL_PASSWORD: ${{ secrets.MYSQL_PASSWORD }}
        run: ./migrate -database "mysql://${MYSQL_USER}:${MYSQL_PASSWORD}@tcp(127.0.0.1:3306)/equtum" -path db/migrations up

  delete_draft_release:
    runs-on: ubuntu-latest
    needs: [migrate-prd]
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v4
      - name: Get Latest release
        id: get_latest_release
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          lastPublishedRelease==$(gh release list --exclude-drafts --json=createdAt,isDraft --jq="[.[].createdAt | now - fromdate][0]")
          echo "threshold=$(echo ${lastPublishedRelease%.*})" >> $GITHUB_OUTPUT
      - name: Delete drafts
        uses: hugo19941994/delete-draft-releases@v1.0.1
        with:
          threshold: ${{ steps.get_latest_release.outputs.threshold }}s
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
