name: migrate

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
    secrets:
      GCP_WORKLOAD_IDENTITY_PROVIDER:
        required: true
      GCP_SERVICE_ACCOUNT:
        required: true
      MYSQL_USER:
        required: true
      MYSQL_PASSWORD:
        required: true

jobs:
  migrate:
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - id: auth
        name: Authenticate
        uses: google-github-actions/auth@v2
        with:
          create_credentials_file: true
          workload_identity_provider: ${{ secrets.GCP_WORKLOAD_IDENTITY_PROVIDER }}
          service_account: ${{ secrets.GCP_SERVICE_ACCOUNT }}

      - name: Setup Cloud SQL Auth Proxy
        env:
          CREDENTIALS_FILE_PATH: ${{ steps.auth.outputs.credentials_file_path }}
        run: |
          wget https://dl.google.com/cloudsql/cloud_sql_proxy.linux.amd64 -O cloud_sql_proxy
          chmod +x cloud_sql_proxy
          ./cloud_sql_proxy -credential_file=${CREDENTIALS_FILE_PATH} -instances=${{ vars.CLOUD_SQL_INSTANCE }}=tcp:3306 &

      - name: Setup golang-migrate
        run: |
          curl -L https://github.com/golang-migrate/migrate/releases/download/v4.15.2/migrate.linux-amd64.tar.gz | tar xvz

      - name: Run migration up
        env:
          MYSQL_USER: ${{ secrets.MYSQL_USER }}
          MYSQL_PASSWORD: ${{ secrets.MYSQL_PASSWORD }}
        run: ./migrate -database "mysql://${MYSQL_USER}:${MYSQL_PASSWORD}@tcp(127.0.0.1:3306)/equtum" -path db/migrations up
