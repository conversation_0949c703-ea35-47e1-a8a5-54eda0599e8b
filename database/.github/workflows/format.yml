name: Format SQL files in PR

on:
  pull_request:
    paths:
      - "db/migrations/*.sql"

jobs:
  format_sql:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2
        with:
          fetch-depth: 0

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: 16

      - name: Install sql-formatter
        run: npm install -g sql-formatter

      - name: Format SQL files
        run: |
          for file in $(git diff --name-only --diff-filter=d ${{ github.event.pull_request.base.sha }}..${{ github.event.pull_request.head.sha }} | grep -E 'db/migrations/.*\.sql$'); do
            npx sql-formatter --fix $file --config .sql-formatter.json
            git add $file
          done

      - name: Commit version
        uses: stefanzweifel/git-auto-commit-action@v4.2.0
        with:
          commit_message: "[auto commit] format SQL files"
