name: Dry-Run

on:
  pull_request:
    branches:
      - main
      - develop

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  dry-run:
    runs-on: ubuntu-latest
    services:
      db:
        image: mysql:8.0.28
        ports:
          - 3306:3306
        env:
          MYSQL_ROOT_PASSWORD: passwd
          MYSQL_DATABASE: equtum
        options: >-
          --health-cmd "mysqladmin ping -uroot -ppasswd"
          --health-interval 10s
          --health-timeout 10s
          --health-retries 5
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup
        run: | 
          curl -L https://github.com/golang-migrate/migrate/releases/download/v4.15.2/migrate.linux-amd64.tar.gz | tar xvz

      - name: Dry-run migration up
        run: ./migrate -database "mysql://root:passwd@tcp(127.0.0.1:3306)/equtum" -path db/migrations up

      - name: Dry-run seeding
        run: mysql -ppasswd -u root --host 127.0.0.1 --port 3306 equtum < dump/local_db.sql

      - name: Dry-run migration down
        run: ./migrate -database "mysql://root:passwd@tcp(127.0.0.1:3306)/equtum" -path db/migrations down 1
