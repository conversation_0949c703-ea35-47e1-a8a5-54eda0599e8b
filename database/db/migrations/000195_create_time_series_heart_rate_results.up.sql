CREATE TABLE `time_series_heart_beat_results` (
  `time_series_heart_beat_result_id` int NOT NULL AUTO_INCREMENT,
  `training_id` bigint UNSIGNED NOT NULL,
  `time` int NOT NULL,
  `heart_rate` int DEFAULT NULL,
  `sympathetic_nerve` float DEFAULT NULL,
  `parasympathetic_nerve` float DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`time_series_heart_beat_result_id`),
  UNIQUE KEY `training_id` (`training_id`, `time`),
  CONSTRAINT `time_series_heart_beat_results_training_id_fk` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;
