ALTER TABLE `staffs`
MODIFY COLUMN stable_uuid BINARY(16) NOT NULL,
DROP FOREIGN KEY `staffs_user_uuid_fk`,
DROP FOREIGN KEY `staffs_organization_uuid_fk`,
DROP COLUMN `user_uuid`,
DROP COLUMN `organization_uuid`;


ALTER TABLE staffs
CHANGE COLUMN staff_uuid training_staff_uuid BINARY(16) NOT NULL,
ALGORITHM = INPLACE,
LOCK = SHARED;


RENAME TABLE `staffs` TO `training_staffs`;


ALTER TABLE horse_daily_records_race_recaps
DROP FOREIGN KEY horse_daily_records_race_recaps_staff_uuid_fk,
<PERSON><PERSON><PERSON> COLUMN staff_uuid training_staff_uuid BINARY(16) DEFAULT NULL,
ADD CONSTRAINT horse_daily_records_race_recaps_training_staff_uuid_fk FOREIGN KEY (training_staff_uuid) REFERENCES training_staffs (training_staff_uuid);


ALTER TABLE `training_staffs`
ADD CONSTRAINT `stable_uuid_fk_5` <PERSON>OR<PERSON><PERSON><PERSON> KEY (`stable_uuid`) REFERENCES `stables` (`stable_uuid`);
