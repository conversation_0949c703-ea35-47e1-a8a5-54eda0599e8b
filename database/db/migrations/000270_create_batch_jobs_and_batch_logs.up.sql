-- Create batch_jobs table
CREATE TABLE `batch_jobs` (
  `batch_job_id` binary(16) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `status` enum('PENDING','RUNNING','COMPLETED','FAILED','CANCELLED') NOT NULL DEFAULT 'PENDING',
  `started_at` datetime DEFAULT NULL,
  `completed_at` datetime DEFAULT NULL,
  `error_message` text,
  `metadata` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`batch_job_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create batch_logs table
CREATE TABLE `batch_logs` (
  `batch_log_id` binary(16) NOT NULL,
  `batch_job_id` binary(16) DEFAULT NULL,
  `level` enum('DEBUG','INFO','WARN','ERROR') NOT NULL DEFAULT 'INFO',
  `message` text NOT NULL,
  `metadata` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`batch_log_id`),
  CONSTRAINT `batch_logs_batch_job_id_fk` FOREIGN KEY (`batch_job_id`) REFERENCES `batch_jobs` (`batch_job_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;