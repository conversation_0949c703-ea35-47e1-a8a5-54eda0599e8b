CREATE TABLE `user_lang_settings` (
  `user_lang_setting_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `training_id` bigint UNSIGNED NOT NULL,
  `user_uuid` binary(16) NOT NULL,
  `lang` varchar(10) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_lang_setting_id`),
  CONSTRAINT `user_lang_settings_user_uuid_fk` FOREIGN KEY (`user_uuid`) REFERENCES `users` (`user_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;
