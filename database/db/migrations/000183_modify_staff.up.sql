RENAME TABLE `training_staffs` TO `staffs`;


ALTER TABLE staffs
CHANGE COLUMN training_staff_uuid staff_uuid BINARY(16) NOT NULL,
ALGORITHM = INPLACE,
LOCK = SHARED;


ALTER TABLE `staffs`
MODIFY COLUMN stable_uuid BINARY(16) DEFAULT NULL,
ADD COLUMN `user_uuid` BINARY(16) DEFAULT NULL AFTER `name`,
ADD COLUMN `organization_uuid` BINARY(16) DEFAULT NULL AFTER `user_uuid`,
ADD CONSTRAINT `staffs_user_uuid_fk` FOREIGN KEY (`user_uuid`) REFERENCES `users` (`user_uuid`),
ADD CONSTRAINT `staffs_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`),
DROP FOREIGN KEY `stable_uuid_fk_5`;


UPDATE staffs s
JOIN stables st ON s.stable_uuid = st.stable_uuid
SET
  s.organization_uuid = st.organization_uuid;


ALTER TABLE horse_daily_records_race_recaps
DROP FOREIGN KEY horse_daily_records_race_recaps_training_staff_uuid_fk,
CHANGE COLUMN training_staff_uuid staff_uuid BINARY(16) DEFAULT NULL,
ADD CONSTRAINT horse_daily_records_race_recaps_staff_uuid_fk FOREIGN KEY (staff_uuid) REFERENCES staffs (staff_uuid);
