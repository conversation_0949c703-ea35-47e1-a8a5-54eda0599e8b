CREATE TABLE `horse_daily_records_horse_medical_treatment_affected_area_photos` (
  `horse_medical_treatment_affected_area_photo_id` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `horse_medical_treatment_affected_area_photo_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `horse_medical_treatment_record_id` binary(16) NOT NULL,
  `photo_path` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_medical_treatment_affected_area_photo_id`),
  UNIQUE KEY `idx_horse_medical_treatment_affected_area_photo_internal_id` (
    `horse_medical_treatment_affected_area_photo_internal_id`
  ),
  KEY `horse_medical_treatment_affected_area_photos_record_id_fk` (`horse_medical_treatment_record_id`),
  CONSTRAINT `horse_medical_treatment_affected_area_photos_record_id_fk` FOREIGN KEY (`horse_medical_treatment_record_id`) REFERENCES `horse_daily_records_horse_medical_treatments` (`horse_medical_treatment_record_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `horse_daily_records_horse_body_affected_area_photos` (
  `horse_body_affected_area_photo_id` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `horse_body_affected_area_photo_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `horse_daily_record_id` binary(16) NOT NULL,
  `daypart` varchar(50) NOT NULL,
  `photo_path` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_body_affected_area_photo_id`),
  UNIQUE KEY `idx_horse_body_affected_area_photo_internal_id` (`horse_body_affected_area_photo_internal_id`),
  KEY `horse_body_affected_area_photos_horse_daily_record_id_fk` (`horse_daily_record_id`),
  CONSTRAINT `horse_body_affected_area_photos_horse_daily_record_id_fk` FOREIGN KEY (`horse_daily_record_id`) REFERENCES `horse_daily_records` (`horse_daily_record_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;
