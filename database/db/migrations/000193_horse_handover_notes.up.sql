CREATE TABLE `horse_handover_notes` (
  `horse_handover_note_id` binary(16) NOT NULL,
  `horse_handover_note_internal_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `organization_uuid` binary(16) NOT NULL,
  `horse_id` bigint UNSIGNED NOT NULL,
  `handover_note` text DEFAULT NULL,
  `next_race_equipment_note` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_handover_note_id`),
  UNIQUE KEY `idx_horse_handover_notes_horse_handover_note_internal_id` (`horse_handover_note_internal_id`),
  KEY `horse_handover_notes_horse_id_fk` (`horse_id`),
  CONSTRAINT `horse_handover_notes_horse_id_fk` FOREIGN KEY (`horse_id`) REFERENCES `horses` (`horse_id`),
  <PERSON><PERSON><PERSON> `horse_handover_notes_organization_uuid_fk` (`organization_uuid`),
  CONSTRAINT `horse_handover_notes_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;
