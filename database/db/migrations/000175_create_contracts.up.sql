CREATE TABLE contracts (
  contract_id bigint UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  organization_uuid binary(16) NOT NULL,
  has_hm_contract tinyint(1) NOT NULL DEFAULT 0,
  has_orm_contract tinyint(1) NOT NULL DEFAULT 0,
  has_rm_contract tinyint(1) NOT NULL DEFAULT 0,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY `contracts_organization_uuid_fk` (`organization_uuid`),
  CONSTRAINT `contracts_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`)
);
