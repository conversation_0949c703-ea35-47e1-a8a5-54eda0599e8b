CREATE TABLE `report_section_monthly_summaries` (
  `report_section_monthly_summary_id` binary(16) NOT NULL,
  `report_section_monthly_summary_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `report_section_id` binary(16) NOT NULL,
  `start_year` int NOT NULL,
  `start_month` int NOT NULL,
  `start_day` int NOT NULL,
  `end_year` int NOT NULL,
  `end_month` int NOT NULL,
  `end_day` int NOT NULL,
  `horse_body_weight_history` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_section_monthly_summary_id`),
  UNIQUE KEY `idx_report_section_monthly_summary_internal_id` (`report_section_monthly_summary_internal_id`),
  KEY `report_section_monthly_summaries_report_section_id_fk` (`report_section_id`),
  CONSTRAINT `report_section_monthly_summaries_report_section_id_fk` FOREIGN KEY (`report_section_id`) REFERENCES `report_sections` (`report_section_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `report_section_monthly_summary_race_records` (
  `report_section_monthly_summary_race_record_id` binary(16) NOT NULL,
  `report_section_monthly_summary_race_record_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `report_section_monthly_summary_id` binary(16) NOT NULL,
  `body` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_section_monthly_summary_race_record_id`),
  UNIQUE KEY `idx_report_section_monthly_summary_race_record_internal_id` (
    `report_section_monthly_summary_race_record_internal_id`
  ),
  KEY `rsms_race_record_report_section_monthly_summary_id_fk` (`report_section_monthly_summary_id`),
  CONSTRAINT `rsms_race_record_report_section_monthly_summary_id_fk` FOREIGN KEY (`report_section_monthly_summary_id`) REFERENCES `report_section_monthly_summaries` (`report_section_monthly_summary_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `report_section_monthly_timelines` (
  `report_section_monthly_timeline_id` binary(16) NOT NULL,
  `report_section_monthly_timeline_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `report_section_id` binary(16) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_section_monthly_timeline_id`),
  UNIQUE KEY `idx_report_section_monthly_timeline_internal_id` (`report_section_monthly_timeline_internal_id`),
  KEY `report_section_monthly_timelines_report_section_id_fk` (`report_section_id`),
  CONSTRAINT `report_section_monthly_timelines_report_section_id_fk` FOREIGN KEY (`report_section_id`) REFERENCES `report_sections` (`report_section_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `report_section_monthly_timeline_records` (
  `report_section_monthly_timeline_record_id` binary(16) NOT NULL,
  `report_section_monthly_timeline_record_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `report_section_monthly_timeline_id` binary(16) NOT NULL,
  `index` varchar(255) NOT NULL,
  `year` int NOT NULL,
  `month` int NOT NULL,
  `date` int NOT NULL,
  `body` text,
  `training_menu` text,
  `furlong_time` varchar(255),
  `assignee` varchar(255),
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_section_monthly_timeline_record_id`),
  UNIQUE KEY `idx_report_section_monthly_timeline_record_internal_id` (
    `report_section_monthly_timeline_record_internal_id`
  ),
  KEY `report_section_monthly_timeline_records_timeline_id_fk` (`report_section_monthly_timeline_id`),
  KEY `idx_rsmt_records_report_section_monthly_timeline_id_index` (`report_section_monthly_timeline_id`, `index`),
  CONSTRAINT `report_section_monthly_timeline_records_timeline_id_fk` FOREIGN KEY (`report_section_monthly_timeline_id`) REFERENCES `report_section_monthly_timelines` (`report_section_monthly_timeline_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `report_section_medical_treatments` (
  `report_section_medical_treatment_id` binary(16) NOT NULL,
  `report_section_medical_treatment_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `report_section_id` binary(16) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_section_medical_treatment_id`),
  UNIQUE KEY `idx_report_section_medical_treatment_internal_id` (`report_section_medical_treatment_internal_id`),
  KEY `rsmt_report_section_id_fk` (`report_section_id`),
  CONSTRAINT `rsmt_report_section_id_fk` FOREIGN KEY (`report_section_id`) REFERENCES `report_sections` (`report_section_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `report_section_medical_treatment_records` (
  `report_section_medical_treatment_record_id` binary(16) NOT NULL,
  `report_section_medical_treatment_record_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `report_section_medical_treatment_id` binary(16) NOT NULL,
  `year` int NOT NULL,
  `month` int NOT NULL,
  `date` int NOT NULL,
  `body` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_section_medical_treatment_record_id`),
  UNIQUE KEY `idx_report_section_medical_treatment_record_internal_id` (
    `report_section_medical_treatment_record_internal_id`
  ),
  KEY `rsmt_records_report_section_medical_treatment_id_fk` (`report_section_medical_treatment_id`),
  CONSTRAINT `rsmt_records_report_section_medical_treatment_id_fk` FOREIGN KEY (`report_section_medical_treatment_id`) REFERENCES `report_section_medical_treatments` (`report_section_medical_treatment_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `report_section_medical_treatment_affected_area_photos` (
  `report_section_medical_treatment_affected_area_photo_id` binary(16) NOT NULL,
  `report_section_medical_treatment_affected_area_photo_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `report_section_medical_treatment_record_id` binary(16) NOT NULL,
  `photo_path` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (
    `report_section_medical_treatment_affected_area_photo_id`
  ),
  UNIQUE KEY `idx_rsmt_affected_area_photo_internal_id` (
    `report_section_medical_treatment_affected_area_photo_internal_id`
  ),
  KEY `rsmt_area_photos_report_section_medical_treatment_record_id_fk` (`report_section_medical_treatment_record_id`),
  CONSTRAINT `rsmt_area_photos_report_section_medical_treatment_record_id_fk` FOREIGN KEY (`report_section_medical_treatment_record_id`) REFERENCES `report_section_medical_treatment_records` (`report_section_medical_treatment_record_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


ALTER TABLE `reports`
ADD COLUMN `template_id` varchar(255) NOT NULL AFTER `title`;
