CREATE TABLE `trainers_user_settings` (
  `trainers_user_settings_id` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `user_uuid` binary(16) NOT NULL,
  `report_unread_filter_truncate_enabled` tinyint(1) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`trainers_user_settings_id`),
  CONSTRAINT `trainers_user_settings_user_uuid_fk` FOREIGN KEY (`user_uuid`) REFERENCES `users` (`user_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;
