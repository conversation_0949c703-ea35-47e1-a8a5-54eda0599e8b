CREATE TABLE `horse_course_pitch_averages` (
  `horse_course_pitch_average_uuid` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `master_horse_id` varchar(50) NOT NULL,
  `course_id` varchar(50) NOT NULL,
  `speed` int UNSIGNED NOT NULL,
  `relative_average_pitch` float DEFAULT NULL,
  `absolute_average_pitch` float DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_course_pitch_average_uuid`),
  UNIQUE KEY `unique_horse_course_speed` (`master_horse_id`, `course_id`, `speed`),
  KEY `horse_course_pitch_averages_master_horse_id_fk` (`master_horse_id`),
  KEY `horse_course_pitch_averages_course_id_idx` (`course_id`),
  CONSTRAINT `horse_course_pitch_averages_master_horse_id_fk` FOREIGN KEY (`master_horse_id`) REFERENCES `master_horses` (`master_horse_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `training_course_pitch_averages` (
  `training_course_pitch_average_uuid` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `course_id` varchar(50) NOT NULL,
  `speed` int UNSIGNED NOT NULL,
  `average_pitch` float DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_course_pitch_average_uuid`),
  UNIQUE KEY `unique_course_speed` (`course_id`, `speed`),
  KEY `training_course_pitch_averages_course_id_idx` (`course_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `training_course_horse_ability_scores` (
  `training_course_horse_ability_score_uuid` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `master_horse_id` varchar(50) NOT NULL,
  `course_id` varchar(50) NOT NULL,
  `pitch_score` float DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_course_horse_ability_score_uuid`),
  UNIQUE KEY `unique_horse_course` (`master_horse_id`, `course_id`),
  KEY `training_course_horse_ability_scores_master_horse_id_fk` (`master_horse_id`),
  KEY `training_course_horse_ability_scores_course_id_idx` (`course_id`),
  CONSTRAINT `training_course_horse_ability_scores_master_horse_id_fk` FOREIGN KEY (`master_horse_id`) REFERENCES `master_horses` (`master_horse_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;
