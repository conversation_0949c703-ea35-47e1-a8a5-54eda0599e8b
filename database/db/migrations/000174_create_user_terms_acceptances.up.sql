CREATE TABLE user_terms_acceptances (
  `user_terms_acceptance_id` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `user_id` bigint UNSIGNED NOT NULL,
  `accepted_at` DATETIME NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_terms_acceptance_id`),
  CONSTRAINT `user_terms_acceptances_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
);
