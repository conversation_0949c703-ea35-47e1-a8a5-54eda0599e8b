CREATE TABLE horse_note_user_devices (
  `device_id` VARCHAR(50) NOT NULL,
  `os_name` VA<PERSON>HAR(50) NULL,
  `os_version` VARCHAR(50),
  `model_name` VA<PERSON>HA<PERSON>(50),
  `app_runtime_version` VA<PERSON>HAR(50) NOT NULL,
  `app_update_created_at` DATETIME NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`device_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;
