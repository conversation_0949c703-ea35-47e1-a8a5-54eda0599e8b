CREATE TABLE training_menus (
  `training_menu_uuid` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `training_menu_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `stable_uuid` binary(16) NOT NULL,
  `training_menu_name` TEXT NOT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_menu_internal_id`),
  UNIQUE KEY `idx_training_menu_internal_id` (`training_menu_internal_id`),
  CONSTRAINT `training_menus_stable_uuid_fk` FOREIGN KEY (`stable_uuid`) REFERENCES stables (`stable_uuid`)
)
