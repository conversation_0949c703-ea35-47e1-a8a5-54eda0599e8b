CREATE TABLE horse_daily_records_race_results (
  `race_result_id` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `race_result_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `horse_daily_record_id` binary(16) NOT NULL,
  `race_place_id` int DEFAULT NULL,
  `race_number` int DEFAULT NULL,
  `race_name` VARCHAR(255) DEFAULT NULL,
  `distance` INT DEFAULT NULL,
  `going` VARCHAR(50) DEFAULT NULL,
  `track_type` VARCHAR(100) DEFAULT NULL,
  `jockey_name` VARCHAR(100) DEFAULT NULL,
  `weight` INT DEFAULT NULL,
  `before_race_weight_diff` INT DEFAULT NULL,
  `rank` INT DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`race_result_id`),
  UNIQUE KEY `idx_race_result_internal_id` (`race_result_internal_id`),
  KEY `horse_daily_records_race_results_horse_daily_record_id_fk` (`horse_daily_record_id`),
  KEY `horse_daily_records_race_results_race_place_id_fk` (`race_place_id`),
  CONSTRAINT `horse_daily_records_race_results_horse_daily_record_id_fk` FOREIGN KEY (`horse_daily_record_id`) REFERENCES `horse_daily_records` (`horse_daily_record_id`),
  CONSTRAINT `horse_daily_records_race_results_race_place_id_fk` FOREIGN KEY (`race_place_id`) REFERENCES `race_places` (`race_place_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE horse_daily_records_race_recaps (
  `race_recap_id` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `race_recap_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `horse_daily_record_id` binary(16) NOT NULL,
  `attendance` VARCHAR(100) DEFAULT NULL COMMENT '臨場',
  `staff` VARCHAR(255) DEFAULT NULL,
  `equipment` VARCHAR(255) DEFAULT NULL,
  `comment_in_transport` TEXT DEFAULT NULL,
  `comment_in_stable` TEXT DEFAULT NULL,
  `comment_in_paddock` TEXT DEFAULT NULL,
  `comment_in_warm_up` TEXT DEFAULT NULL,
  `comment_in_gate` TEXT DEFAULT NULL,
  `race_strategy` TEXT DEFAULT NULL,
  `comment_after_race` TEXT DEFAULT NULL,
  `comment_from_jockey` TEXT DEFAULT NULL,
  `comment_from_trainer` TEXT DEFAULT NULL,
  `comment_to_next_race` TEXT DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`race_recap_id`),
  UNIQUE KEY `idx_race_recap_internal_id` (`race_recap_internal_id`),
  KEY `race_recap_horse_daily_record_id_fk` (`horse_daily_record_id`),
  CONSTRAINT `race_recap_horse_daily_record_id_fk` FOREIGN KEY (`horse_daily_record_id`) REFERENCES `horse_daily_records` (`horse_daily_record_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;
