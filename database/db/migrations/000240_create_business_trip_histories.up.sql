CREATE TABLE `business_trip_histories` (
  `business_trip_history_id` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `business_trip_history_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `organization_uuid` binary(16) NOT NULL,
  `staff_name` varchar(255) DEFAULT NULL,
  `destination_name` varchar(255) DEFAULT NULL,
  `start_year` int DEFAULT NULL,
  `start_month` int DEFAULT NULL,
  `start_day` int DEFAULT NULL,
  `end_year` int DEFAULT NULL,
  `end_month` int DEFAULT NULL,
  `end_day` int DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`business_trip_history_id`),
  UNIQUE KEY `business_trip_history_internal_id` (`business_trip_history_internal_id`),
  <PERSON><PERSON><PERSON> `business_trip_histories_organization_uuid_fk` (`organization_uuid`),
  CONSTRAINT `business_trip_histories_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `business_trip_history_horses` (
  `business_trip_history_horse_id` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `business_trip_history_id` binary(16) NOT NULL,
  `horse_id` bigint UNSIGNED NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`business_trip_history_horse_id`),
  UNIQUE KEY `business_trip_history_horses_unique` (`business_trip_history_id`, `horse_id`),
  KEY `business_trip_history_horses_horse_id_fk` (`horse_id`),
  CONSTRAINT `business_trip_history_horses_business_trip_history_id_fk` FOREIGN KEY (`business_trip_history_id`) REFERENCES `business_trip_histories` (`business_trip_history_id`),
  CONSTRAINT `business_trip_history_horses_horse_id_fk` FOREIGN KEY (`horse_id`) REFERENCES `horses` (`horse_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;
