CREATE TABLE stable_status (
  id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  stable_uuid BINARY(16) NOT NULL,
  stall_num INT NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY idx_stable_uuid (stable_uuid),
  CONSTRAINT stable_status_stable_uuid_fk FOREIGN KEY (stable_uuid) REFERENCES stables (stable_uuid)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
