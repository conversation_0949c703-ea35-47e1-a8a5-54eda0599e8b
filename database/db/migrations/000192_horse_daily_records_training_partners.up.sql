CREATE TABLE `horse_daily_records_training_partners` (
  `training_partner_id` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `training_partner_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `training_record_uuid` binary(16) NOT NULL,
  `horse_id` bigint UNSIGNED DEFAULT NULL,
  `rank` int DEFAULT NULL COMMENT '着順',
  `horse_name` varchar(255) NOT NULL,
  `track_position` varchar(50) DEFAULT NULL COMMENT '内中外',
  `starting_order` int DEFAULT NULL COMMENT '番手',
  `detail` text DEFAULT NULL COMMENT '内容',
  `intensity` varchar(50) DEFAULT NULL COMMENT '強度',
  `margin` varchar(50) DEFAULT NULL COMMENT '着差',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_partner_id`),
  UNIQUE KEY `training_partner_internal_id` (`training_partner_internal_id`),
  KEY `horse_daily_records_training_partners_training_record_uuid_fk` (`training_record_uuid`),
  KEY `horse_daily_records_training_partners_horse_id_fk` (`horse_id`),
  CONSTRAINT `horse_daily_records_training_partners_horse_id_fk` FOREIGN KEY (`horse_id`) REFERENCES `horses` (`horse_id`),
  CONSTRAINT `horse_daily_records_training_partners_training_record_uuid_fk` FOREIGN KEY (`training_record_uuid`) REFERENCES `horse_daily_records_trainings` (`training_record_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;
