CREATE TABLE ai_generate_logs (
  `ai_generate_log_id` VARCHAR(50) NOT NULL PRIMARY KEY,
  `ai_generate_log_internal_id` BIGINT UNSIGNED NOT NULL UNIQUE AUTO_INCREMENT,
  `model` VARCHAR(100) DEFAULT NULL,
  `request_prompt` TEXT DEFAULT NULL,
  `response` TEXT DEFAULT NULL,
  `prompt_tokens` INT UNSIGNED DEFAULT NULL,
  `completion_tokens` INT UNSIGNED DEFAULT NULL,
  `total_tokens` INT UNSIGNED DEFAULT NULL,
  `finish_reason` VARCHAR(100) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);


-- report_generate_requestsテーブルにai_generate_log_idを追加する
ALTER TABLE report_generate_requests
ADD COLUMN `ai_generate_log_id` VARCHAR(50) DEFAULT NULL,
ADD CONSTRAINT `report_generate_requests_ai_generate_log_id_fk` FOREIGN KEY (`ai_generate_log_id`) REFERENCES `ai_generate_logs` (`ai_generate_log_id`);
