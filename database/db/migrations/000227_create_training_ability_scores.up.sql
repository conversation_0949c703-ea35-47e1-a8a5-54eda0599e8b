CREATE TABLE `training_ability_scores` (
  `training_ability_score_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `training_id` bigint UNSIGNED NOT NULL,
  `sympathetic_nerve_last_60sec_sum` float DEFAULT NULL,
  `parasympathetic_nerve_max_diff` float DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_ability_score_id`),
  KEY `training_ability_scores_training_id_fk` (`training_id`),
  CONSTRAINT `training_ability_scores_training_id_fk` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;
