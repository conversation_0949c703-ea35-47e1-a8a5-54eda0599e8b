DROP TABLE IF EXISTS report_generate_requests;


CREATE TABLE report_generate_requests (
  `report_generate_request_id` BIGINT UNSIGNED NOT NULL PRIMARY KEY AUTO_INCREMENT,
  `horse_id` BIGINT UNSIGNED NOT NULL,
  `report_id` VARCHAR(50) DEFAULT NULL,
  `request_memo` TEXT DEFAULT NULL,
  `generated_content` TEXT DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  CONSTRAINT `report_generate_requests_horse_id_fk` FOREIGN KEY (`horse_id`) REFERENCES `horses` (`horse_id`),
  CONSTRAINT `report_generate_requests_report_id_fk` FOREIGN KEY (`report_id`) REFERENCES `reports` (`report_id`)
);
