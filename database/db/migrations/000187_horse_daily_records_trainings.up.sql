CREATE TABLE `horse_daily_records_trainings` (
  `training_record_uuid` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `training_record_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `horse_daily_record_id` binary(16) NOT NULL,
  `is_gait_abnormal` tinyint(1) DEFAULT NULL,
  `gait_abnormal_description` text DEFAULT NULL,
  `training_type` varchar(255) DEFAULT NULL,
  `rider_uuid` binary(16) DEFAULT NULL,
  `training_menu_uuid` binary(16) DEFAULT NULL,
  `facility_id` varchar(50) DEFAULT NULL,
  `course_id` varchar(50) DEFAULT NULL,
  `course_going` varchar(50) DEFAULT NULL,
  `furlong_time` varchar(50) DEFAULT NULL,
  `training_comment` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_record_uuid`),
  UNIQUE KEY `idx_training_record_internal_id` (`training_record_internal_id`),
  KEY `horse_daily_records_trainings_horse_daily_record_id_fk` (`horse_daily_record_id`),
  KEY `horse_daily_records_trainings_training_menu_uuid_fk` (`training_menu_uuid`),
  KEY `horse_daily_records_trainings_facility_id_fk` (`facility_id`),
  KEY `horse_daily_records_trainings_course_id_fk` (`course_id`),
  KEY `horse_daily_records_trainings_rider_uuid_fk` (`rider_uuid`),
  CONSTRAINT `horse_daily_records_trainings_horse_daily_record_id_fk` FOREIGN KEY (`horse_daily_record_id`) REFERENCES `horse_daily_records` (`horse_daily_record_id`),
  CONSTRAINT `horse_daily_records_trainings_training_menu_uuid_fk` FOREIGN KEY (`training_menu_uuid`) REFERENCES `training_menus` (`training_menu_uuid`),
  CONSTRAINT `horse_daily_records_trainings_facility_id_fk` FOREIGN KEY (`facility_id`) REFERENCES `facilities` (`facility_id`),
  CONSTRAINT `horse_daily_records_trainings_course_id_fk` FOREIGN KEY (`course_id`) REFERENCES `training_courses` (`course_id`),
  CONSTRAINT `horse_daily_records_trainings_rider_uuid_fk` FOREIGN KEY (`rider_uuid`) REFERENCES `staffs` (`staff_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;
