-- -------------------------------------------------------------
-- TablePlus 6.1.4(570)
--
-- https://tableplus.com/
--
-- Database: equtum
-- Generation Time: 2024-09-24 15:27:41.4120
-- -------------------------------------------------------------
/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;


/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;


/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;


/*!40101 SET NAMES utf8mb4 */;


/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;


/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;


/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;


/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


CREATE TABLE `accessible_courses` (
  `accessible_course_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `stable_uuid` binary(16) NOT NULL,
  `course_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`accessible_course_id`),
  UNIQUE KEY `idx_stable_uuid_course_id` (`stable_uuid`, `course_id`),
  CONSTRAINT `accessible_courses_stable_uuid_fk` FOREIGN KEY (`stable_uuid`) REFERENCES `stables` (`stable_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `accessible_facilities` (
  `stable_uuid` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `facility_id` bigint UNSIGNED NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`stable_uuid`, `facility_id`),
  KEY `facility_id_fk_1` (`facility_id`),
  CONSTRAINT `facility_id_fk_1` FOREIGN KEY (`facility_id`) REFERENCES `training_facilities` (`facility_id`),
  CONSTRAINT `stable_uuid_fk_1` FOREIGN KEY (`stable_uuid`) REFERENCES `stables` (`stable_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `aggregated_stats_furlong_line_based` (
  `aggregated_stats_furlong_line_based_uuid` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `training_period_uuid` binary(16) NOT NULL,
  `furlong` int NOT NULL,
  `furlong_time` float DEFAULT NULL,
  `passed_time` int NOT NULL,
  `distance` int NOT NULL,
  `average_speed` float DEFAULT NULL,
  `average_pitch` float DEFAULT NULL,
  `average_stride` float DEFAULT NULL,
  `average_heart_rate` int DEFAULT NULL,
  `max_heart_rate` int DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`aggregated_stats_furlong_line_based_uuid`),
  KEY `training_id_fk_12` (`training_period_uuid`),
  CONSTRAINT `training_id_fk_12` FOREIGN KEY (`training_period_uuid`) REFERENCES `training_periods` (`training_period_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `associations` (
  `association_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `association_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `association_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`association_id`),
  UNIQUE KEY `association_internal_id` (`association_internal_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `device_groups` (
  `organization_uuid` binary(16) NOT NULL,
  `stable_uuid` binary(16) DEFAULT NULL,
  `device_group_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  `hr_device_id` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `has_front_leg_sensor` tinyint(1) DEFAULT '1',
  `has_hind_leg_sensor` tinyint(1) DEFAULT '1',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`device_group_id`),
  UNIQUE KEY `device_group_id` (`device_group_id`),
  UNIQUE KEY `uq_name_stable_uuid` (`name`, `stable_uuid`),
  KEY `stable_uuid_fk` (`stable_uuid`),
  KEY `device_groups_organization_uuid_fk` (`organization_uuid`),
  CONSTRAINT `device_groups_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`),
  CONSTRAINT `stable_uuid_fk` FOREIGN KEY (`stable_uuid`) REFERENCES `stables` (`stable_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `distance_series_results` (
  `distance_series_result_uuid` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `training_id` bigint UNSIGNED NOT NULL,
  `distance` int NOT NULL,
  `gait` enum('stable', 'walk', 'trot', 'canter', 'gallop') DEFAULT NULL,
  `lead_leg` enum('right', 'left') DEFAULT NULL,
  `speed` float DEFAULT NULL,
  `pitch` float DEFAULT NULL,
  `stride` float DEFAULT NULL,
  `heart_rate` int DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`distance_series_result_uuid`),
  UNIQUE KEY `training_id` (`training_id`, `distance`),
  CONSTRAINT `training_id_fk_9` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `email_logs` (
  `email_log_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `email` varchar(255) NOT NULL,
  `recipient_firebase_uid` varchar(50) DEFAULT NULL,
  `sender_firebase_uid` varchar(50) DEFAULT NULL,
  `email_template_key` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`email_log_internal_id`),
  KEY `idx_email` (`email`),
  KEY `idx_recipient_firebase_uid` (`recipient_firebase_uid`),
  KEY `idx_sender_firebase_uid` (`sender_firebase_uid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `facilities` (
  `facility_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `facility_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `association_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `facility_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`facility_id`),
  UNIQUE KEY `facility_internal_id` (`facility_internal_id`),
  KEY `facilities_association_id_fk` (`association_id`),
  CONSTRAINT `facilities_association_id_fk` FOREIGN KEY (`association_id`) REFERENCES `associations` (`association_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `firmware_versions` (
  `major` int UNSIGNED NOT NULL,
  `minor` int UNSIGNED NOT NULL,
  `patch` int UNSIGNED NOT NULL,
  `data_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`major`, `minor`, `patch`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `furlong_lines` (
  `furlong_line_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `facility_id` bigint UNSIGNED NOT NULL,
  `course_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `furlong` int UNSIGNED NOT NULL,
  `direction` enum('right', 'left', 'straight') NOT NULL,
  `line` geometry NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`furlong_line_internal_id`),
  UNIQUE KEY `idx_facility_id_furlong_direction` (`facility_id`, `furlong`, `direction`),
  CONSTRAINT `facility_id_fk_3` FOREIGN KEY (`facility_id`) REFERENCES `training_facilities` (`facility_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `gait_analysis_results` (
  `gait_analysis_result_uuid` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `training_id` bigint UNSIGNED NOT NULL,
  `start_at` TIMESTAMP NOT NULL,
  `end_at` TIMESTAMP NOT NULL,
  `gait` enum('stable', 'walk', 'trot', 'canter', 'gallop') DEFAULT NULL,
  `impact_left_front` float DEFAULT NULL,
  `impact_right_front` float DEFAULT NULL,
  `impact_left_back` float DEFAULT NULL,
  `impact_right_back` float DEFAULT NULL,
  `swing_time_ratio_left_front` float DEFAULT NULL,
  `swing_time_ratio_right_front` float DEFAULT NULL,
  `swing_time_ratio_left_back` float DEFAULT NULL,
  `swing_time_ratio_right_back` float DEFAULT NULL,
  `foot_on_angle_left_front` float DEFAULT NULL,
  `foot_on_angle_right_front` float DEFAULT NULL,
  `foot_on_angle_left_back` float DEFAULT NULL,
  `foot_on_angle_right_back` float DEFAULT NULL,
  `foot_off_angle_left_front` float DEFAULT NULL,
  `foot_off_angle_right_front` float DEFAULT NULL,
  `foot_off_angle_left_back` float DEFAULT NULL,
  `foot_off_angle_right_back` float DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`gait_analysis_result_uuid`),
  KEY `training_id_fk_6` (`training_id`),
  CONSTRAINT `training_id_fk_6` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `horse_daily_records` (
  `horse_daily_record_id` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `horse_daily_record_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `organization_uuid` binary(16) NOT NULL,
  `horse_id` bigint UNSIGNED NOT NULL,
  `year` int NOT NULL,
  `month` int NOT NULL,
  `day` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_daily_record_id`),
  UNIQUE KEY `horse_daily_record_internal_id` (`horse_daily_record_internal_id`),
  UNIQUE KEY `horse_daily_records_horse_id_year_month_day_unique` (`horse_id`, `year`, `month`, `day`),
  KEY `idx_organization_uuid` (`organization_uuid`),
  CONSTRAINT `horse_daily_records_horse_id_fk` FOREIGN KEY (`horse_id`) REFERENCES `horses` (`horse_id`),
  CONSTRAINT `horse_daily_records_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `horse_daily_records_horse_bodies` (
  `horse_body_record_id` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `horse_body_record_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `horse_daily_record_id` binary(16) NOT NULL,
  `body_weight` int DEFAULT NULL,
  `am_body_temperature` decimal(3, 1) DEFAULT NULL,
  `pm_body_temperature` decimal(3, 1) DEFAULT NULL,
  `am_horse_body_comment` text,
  `pm_horse_body_comment` text,
  `am_horse_body_care` text,
  `pm_horse_body_care` text,
  `free_comment` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_body_record_id`),
  UNIQUE KEY `horse_body_record_internal_id` (`horse_body_record_internal_id`),
  UNIQUE KEY `horse_body_records_horse_daily_record_id_fk` (`horse_daily_record_id`),
  CONSTRAINT `horse_body_records_horse_daily_record_id_fk` FOREIGN KEY (`horse_daily_record_id`) REFERENCES `horse_daily_records` (`horse_daily_record_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `horse_daily_records_horse_body_photos` (
  `horse_body_photo_id` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `horse_body_photo_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `horse_daily_record_id` binary(16) NOT NULL,
  `photo_path` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_body_photo_id`),
  UNIQUE KEY `horse_body_photo_internal_id` (`horse_body_photo_internal_id`),
  KEY `horse_body_photos_horse_daily_record_id_fk` (`horse_daily_record_id`),
  CONSTRAINT `horse_body_photos_horse_daily_record_id_fk` FOREIGN KEY (`horse_daily_record_id`) REFERENCES `horse_daily_records` (`horse_daily_record_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `horse_daily_records_horse_medical_treatment_invoice_photos` (
  `horse_medical_treatment_invoice_photo_id` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `horse_medical_treatment_invoice_photo_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `horse_medical_treatment_record_id` binary(16) NOT NULL,
  `photo_path` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_medical_treatment_invoice_photo_id`),
  UNIQUE KEY `idx_horse_medical_treatment_invoice_photo_internal_id` (
    `horse_medical_treatment_invoice_photo_internal_id`
  ),
  KEY `horse_medical_treatment_invoice_photos_record_id_fk` (`horse_medical_treatment_record_id`),
  CONSTRAINT `horse_medical_treatment_invoice_photos_record_id_fk` FOREIGN KEY (`horse_medical_treatment_record_id`) REFERENCES `horse_daily_records_horse_medical_treatments` (`horse_medical_treatment_record_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `horse_daily_records_horse_medical_treatments` (
  `horse_medical_treatment_record_id` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `horse_medical_treatment_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `horse_daily_record_id` binary(16) NOT NULL,
  `medical_treatment_reason` text,
  `medical_treatment_inspection` text,
  `medical_treatment_result` text,
  `medical_treatment_detail` text,
  `organization_veterinarian_id` binary(16) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_medical_treatment_record_id`),
  UNIQUE KEY `idx_horse_medical_treatment_internal_id` (`horse_medical_treatment_internal_id`),
  KEY `horse_medical_treatments_horse_daily_record_id_fk` (`horse_daily_record_id`),
  KEY `fk_horse_medical_treatments_organization_veterinarian_id` (`organization_veterinarian_id`),
  CONSTRAINT `fk_horse_medical_treatments_organization_veterinarian_id` FOREIGN KEY (`organization_veterinarian_id`) REFERENCES `organization_veterinarians` (`organization_veterinarian_id`),
  CONSTRAINT `horse_medical_treatments_horse_daily_record_id_fk` FOREIGN KEY (`horse_daily_record_id`) REFERENCES `horse_daily_records` (`horse_daily_record_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `horse_daily_records_horse_shoeing_invoice_photos` (
  `horse_shoeing_invoice_photo_id` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `horse_shoeing_invoice_photo_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `horse_shoeing_record_id` binary(16) NOT NULL,
  `photo_path` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_shoeing_invoice_photo_id`),
  UNIQUE KEY `idx_horse_shoeing_invoice_photo_internal_id` (`horse_shoeing_invoice_photo_internal_id`),
  KEY `horse_shoeing_invoice_photos_record_id_fk` (`horse_shoeing_record_id`),
  CONSTRAINT `horse_shoeing_invoice_photos_record_id_fk` FOREIGN KEY (`horse_shoeing_record_id`) REFERENCES `horse_daily_records_horse_shoeings` (`horse_shoeing_record_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `horse_daily_records_horse_shoeings` (
  `horse_shoeing_record_id` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `horse_shoeing_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `horse_daily_record_id` binary(16) NOT NULL,
  `horse_shoeing_treatment_type` varchar(255) DEFAULT NULL,
  `organization_farrier_id` binary(16) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_shoeing_record_id`),
  UNIQUE KEY `idx_horse_shoeing_internal_id` (`horse_shoeing_internal_id`),
  KEY `horse_shoeings_horse_daily_record_id_fk` (`horse_daily_record_id`),
  KEY `fk_horse_shoeings_organization_farrier_id` (`organization_farrier_id`),
  CONSTRAINT `fk_horse_shoeings_organization_farrier_id` FOREIGN KEY (`organization_farrier_id`) REFERENCES `organization_farriers` (`organization_farrier_id`),
  CONSTRAINT `horse_shoeings_horse_daily_record_id_fk` FOREIGN KEY (`horse_daily_record_id`) REFERENCES `horse_daily_records` (`horse_daily_record_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `horse_stable_history` (
  `horse_stable_history_uuid` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `horse_id` bigint UNSIGNED NOT NULL,
  `stable_uuid` binary(16) NOT NULL,
  `in_stable` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_stable_history_uuid`),
  KEY `horse_id_fk_horse_stable_history` (`horse_id`),
  KEY `stable_uuid_fk_horse_stable_history` (`stable_uuid`),
  CONSTRAINT `horse_id_fk_horse_stable_history` FOREIGN KEY (`horse_id`) REFERENCES `horses` (`horse_id`),
  CONSTRAINT `stable_uuid_fk_horse_stable_history` FOREIGN KEY (`stable_uuid`) REFERENCES `stables` (`stable_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `horse_weight_history` (
  `horse_weight_uuid` binary(16) NOT NULL,
  `horse_id` bigint UNSIGNED NOT NULL,
  `weight` float NOT NULL,
  `date` DATE NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_weight_uuid`),
  UNIQUE KEY `date_index_horse_weights` (`horse_id`, `date`),
  CONSTRAINT `horse_id_fk_horse_weights` FOREIGN KEY (`horse_id`) REFERENCES `horses` (`horse_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `horses` (
  `horse_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `organization_uuid` binary(16) DEFAULT NULL,
  `stable_uuid` binary(16) NOT NULL,
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `gender` enum('male', 'female', 'gelding') DEFAULT NULL,
  `birth_year` int UNSIGNED DEFAULT NULL,
  `birth_day` DATE DEFAULT NULL,
  `father_id` bigint UNSIGNED DEFAULT NULL,
  `mother_id` bigint UNSIGNED DEFAULT NULL,
  `rfid` bigint DEFAULT NULL,
  `profile_pic_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `master_horse_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `deleted_at` TIMESTAMP NULL DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_id`),
  UNIQUE KEY `horse_id` (`horse_id`),
  KEY `father_id_fk` (`father_id`),
  KEY `mother_id_fk` (`mother_id`),
  KEY `stable_uuid_fk_3` (`stable_uuid`),
  KEY `horses_organization_uuid_fk` (`organization_uuid`),
  KEY `horses_master_horse_id_fk` (`master_horse_id`),
  KEY `idx_horses_name` (`name`),
  CONSTRAINT `father_id_fk` FOREIGN KEY (`father_id`) REFERENCES `horses` (`horse_id`),
  CONSTRAINT `horses_master_horse_id_fk` FOREIGN KEY (`master_horse_id`) REFERENCES `master_horses` (`master_horse_id`),
  CONSTRAINT `horses_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`),
  CONSTRAINT `mother_id_fk` FOREIGN KEY (`mother_id`) REFERENCES `horses` (`horse_id`),
  CONSTRAINT `stable_uuid_fk_3` FOREIGN KEY (`stable_uuid`) REFERENCES `stables` (`stable_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `invalid_results` (
  `training_id` bigint UNSIGNED NOT NULL,
  `period_id` bigint UNSIGNED NOT NULL,
  `code` enum(
    'speed_out_of_range',
    'pitch_out_of_range',
    'stride_out_of_range'
  ) NOT NULL,
  `level` enum('critical', 'error', 'warning', 'info', 'debug') NOT NULL,
  `count` int UNSIGNED NOT NULL,
  PRIMARY KEY (`training_id`, `period_id`, `code`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `invitations` (
  `invitation_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `invitation_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `organization_owner_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `accepted_at` datetime DEFAULT NULL,
  `method` enum('email', 'link') NOT NULL,
  `invite_email` varchar(255) DEFAULT NULL,
  `email_dispatch_count` int DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`invitation_id`),
  UNIQUE KEY `invitation_internal_id` (`invitation_internal_id`),
  UNIQUE KEY `organization_owner_id` (`organization_owner_id`, `token`),
  CONSTRAINT `invitations_organization_owner_id_fk` FOREIGN KEY (`organization_owner_id`) REFERENCES `organization_owners` (`organization_owner_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `jockeys` (
  `jockey_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`jockey_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `master_horse_prizes` (
  `master_horse_prize_id` int NOT NULL AUTO_INCREMENT,
  `master_horse_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `flat_race_prize` int NOT NULL DEFAULT '0',
  `jump_race_prize` int NOT NULL DEFAULT '0',
  `jra_source_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`master_horse_prize_id`),
  UNIQUE KEY `master_horse_prizes_master_horse_id_unique` (`master_horse_id`),
  CONSTRAINT `master_horse_prizes_master_horse_id_fk` FOREIGN KEY (`master_horse_id`) REFERENCES `master_horses` (`master_horse_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `master_horses` (
  `master_horse_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `horse_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_ja_0900_as_cs NOT NULL,
  `horse_name_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `mother_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `gender` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `birth_year` int NOT NULL,
  `stable_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`master_horse_id`),
  KEY `idx_horse_name` (`horse_name`),
  KEY `idx_horse_name_en` (`horse_name_en`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `minimum_versions` (
  `device_type` enum('mobile', 'tablet', 'horse_device') NOT NULL,
  `version` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`device_type`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `organization_farriers` (
  `organization_farrier_id` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `organization_farrier_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `organization_uuid` binary(16) NOT NULL,
  `farrier_name` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`organization_farrier_id`),
  UNIQUE KEY `idx_organization_farrier_internal_id` (`organization_farrier_internal_id`),
  KEY `organization_farriers_organization_uuid_fk` (`organization_uuid`),
  CONSTRAINT `organization_farriers_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `organization_owner_horse_relations` (
  `organization_owner_horse_relation_id` varchar(50) NOT NULL,
  `organization_owner_horse_relation_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `organization_uuid` binary(16) DEFAULT NULL,
  `organization_owner_id` varchar(50) NOT NULL,
  `horse_id` bigint UNSIGNED NOT NULL,
  `owner_horse_id` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`organization_owner_horse_relation_id`),
  UNIQUE KEY `organization_owner_horse_relation_internal_id` (`organization_owner_horse_relation_internal_id`),
  KEY `organization_owner_horse_relations_organization_uuid_fk` (`organization_uuid`),
  KEY `organization_owner_horse_relations_organization_owner_id_fk` (`organization_owner_id`),
  KEY `organization_owner_horse_relations_horse_id_fk` (`horse_id`),
  KEY `organization_owner_horse_relations_owner_horse_id_fk` (`owner_horse_id`),
  CONSTRAINT `organization_owner_horse_relations_horse_id_fk` FOREIGN KEY (`horse_id`) REFERENCES `horses` (`horse_id`),
  CONSTRAINT `organization_owner_horse_relations_organization_owner_id_fk` FOREIGN KEY (`organization_owner_id`) REFERENCES `organization_owners` (`organization_owner_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `organization_owners` (
  `organization_owner_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `organization_owner_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `organization_uuid` binary(16) NOT NULL,
  `owner_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `organization_owner_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`organization_owner_id`),
  UNIQUE KEY `organization_owner_internal_id` (`organization_owner_internal_id`),
  UNIQUE KEY `unique_organization_uuid_owner_id` (`organization_uuid`, `owner_id`),
  KEY `organization_owners_owner_id_fk` (`owner_id`),
  CONSTRAINT `organization_owners_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`),
  CONSTRAINT `organization_owners_owner_id_fk` FOREIGN KEY (`owner_id`) REFERENCES `owners` (`owner_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `organization_veterinarians` (
  `organization_veterinarian_id` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `organization_veterinarian_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `organization_uuid` binary(16) NOT NULL,
  `veterinarian_name` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`organization_veterinarian_id`),
  UNIQUE KEY `idx_organization_veterinarian_internal_id` (`organization_veterinarian_internal_id`),
  KEY `organization_veterinarians_organization_uuid_fk` (`organization_uuid`),
  CONSTRAINT `organization_veterinarians_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `organizations` (
  `organization_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `organization_uuid` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `association_id` varchar(50) DEFAULT NULL,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `home_facility_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`organization_uuid`),
  UNIQUE KEY `organization_id` (`organization_id`),
  UNIQUE KEY `name` (`name`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `owner_horses` (
  `owner_horse_id` varchar(50) NOT NULL,
  `owner_horse_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `owner_id` varchar(50) NOT NULL,
  `master_horse_id` varchar(50) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`owner_horse_id`),
  UNIQUE KEY `owner_horse_internal_id` (`owner_horse_internal_id`),
  KEY `owner_horses_owner_id_fk` (`owner_id`),
  KEY `owner_horses_master_horse_id_fk` (`master_horse_id`),
  CONSTRAINT `owner_horses_master_horse_id_fk` FOREIGN KEY (`master_horse_id`) REFERENCES `master_horses` (`master_horse_id`),
  CONSTRAINT `owner_horses_owner_id_fk` FOREIGN KEY (`owner_id`) REFERENCES `owners` (`owner_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `owners` (
  `owner_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `owner_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `owner_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `firebase_uid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `verified_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`owner_id`),
  UNIQUE KEY `owner_internal_id` (`owner_internal_id`),
  UNIQUE KEY `firebase_uid` (`firebase_uid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `pending_send_reports` (
  `pending_send_report_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `report_id` varchar(50) NOT NULL,
  `horse_id` bigint UNSIGNED NOT NULL,
  `organization_owner_id` varchar(50) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `sent_at` datetime DEFAULT NULL,
  PRIMARY KEY (`pending_send_report_internal_id`),
  KEY `pending_send_reports_report_id_fk` (`report_id`),
  KEY `pending_send_reports_horse_id_fk` (`horse_id`),
  KEY `pending_send_reports_organization_owner_id_fk` (`organization_owner_id`),
  CONSTRAINT `pending_send_reports_horse_id_fk` FOREIGN KEY (`horse_id`) REFERENCES `horses` (`horse_id`),
  CONSTRAINT `pending_send_reports_organization_owner_id_fk` FOREIGN KEY (`organization_owner_id`) REFERENCES `organization_owners` (`organization_owner_id`),
  CONSTRAINT `pending_send_reports_report_id_fk` FOREIGN KEY (`report_id`) REFERENCES `reports` (`report_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `race_horses` (
  `master_horse_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `race_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `jockey_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `starting_gate_number` int DEFAULT NULL,
  `starting_number` int DEFAULT NULL,
  `weight` int DEFAULT NULL,
  `impost` int DEFAULT NULL,
  `rank` int DEFAULT NULL,
  `is_canceled` tinyint(1) NOT NULL DEFAULT '0',
  `is_withdrawn` tinyint(1) NOT NULL DEFAULT '0',
  `length_behind_string` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `length_behind` decimal(5, 2) DEFAULT NULL,
  `total_time` decimal(5, 2) DEFAULT NULL,
  `three_furlong_time` decimal(5, 2) DEFAULT NULL,
  `corner_position` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`master_horse_id`, `race_id`),
  KEY `race_horses_race_id_fk` (`race_id`),
  KEY `race_horses_jockey_id_fk` (`jockey_id`),
  CONSTRAINT `race_horses_jockey_id_fk` FOREIGN KEY (`jockey_id`) REFERENCES `jockeys` (`jockey_id`),
  CONSTRAINT `race_horses_master_horse_id_fk` FOREIGN KEY (`master_horse_id`) REFERENCES `master_horses` (`master_horse_id`),
  CONSTRAINT `race_horses_race_id_fk` FOREIGN KEY (`race_id`) REFERENCES `race_results` (`race_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `race_places` (
  `race_place_id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`race_place_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `race_results` (
  `race_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `race_place_id` int NOT NULL,
  `iteration_number` int DEFAULT NULL,
  `iteration_day_number` int DEFAULT NULL,
  `race_number` int NOT NULL,
  `race_year` int NOT NULL,
  `race_month` int NOT NULL,
  `race_day` int NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `age_limit` varchar(20) DEFAULT NULL,
  `class` varchar(20) DEFAULT NULL,
  `distance` int NOT NULL,
  `track_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `going` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `total_horse_number` int DEFAULT NULL,
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`race_id`),
  KEY `race_results_race_place_id_fk` (`race_place_id`),
  CONSTRAINT `race_results_race_place_id_fk` FOREIGN KEY (`race_place_id`) REFERENCES `race_places` (`race_place_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `report_section_horse_conditions` (
  `report_section_horse_condition_id` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `report_section_id` binary(16) NOT NULL,
  `horse_weight` int DEFAULT NULL,
  `horse_weight_measured_date` varchar(50) DEFAULT NULL,
  `is_gait_abnormal` tinyint(1) DEFAULT NULL,
  `gait_comment` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_section_horse_condition_id`),
  KEY `report_section_horse_conditions_report_section_id_fk` (`report_section_id`),
  CONSTRAINT `report_section_horse_conditions_report_section_id_fk` FOREIGN KEY (`report_section_id`) REFERENCES `report_sections` (`report_section_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `report_section_images` (
  `report_section_image_id` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `report_section_id` binary(16) NOT NULL,
  `image_path` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_section_image_id`),
  KEY `report_section_images_report_section_id_fk` (`report_section_id`),
  CONSTRAINT `report_section_images_report_section_id_fk` FOREIGN KEY (`report_section_id`) REFERENCES `report_sections` (`report_section_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `report_section_plain_texts` (
  `report_section_plain_text_id` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `report_section_id` binary(16) NOT NULL,
  `body` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_section_plain_text_id`),
  KEY `report_section_plain_texts_report_section_id_fk` (`report_section_id`),
  CONSTRAINT `report_section_plain_texts_report_section_id_fk` FOREIGN KEY (`report_section_id`) REFERENCES `report_sections` (`report_section_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `report_section_workout_conditions` (
  `report_section_workout_condition_id` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `report_section_id` binary(16) NOT NULL,
  `workout_training_date` varchar(50) DEFAULT NULL,
  `rider_name` varchar(50) DEFAULT NULL,
  `running_style` varchar(50) DEFAULT NULL,
  `facility_id` varchar(50) DEFAULT NULL,
  `course_id` varchar(50) DEFAULT NULL,
  `workout_furlong_time` varchar(50) DEFAULT NULL,
  `partner_number` int DEFAULT NULL,
  `partner_1_name` varchar(50) DEFAULT NULL,
  `partner_2_name` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_section_workout_condition_id`),
  KEY `report_section_workout_conditions_report_section_id_fk` (`report_section_id`),
  KEY `report_section_workout_conditions_facility_id_fk` (`facility_id`),
  KEY `report_section_workout_conditions_course_id_fk` (`course_id`),
  CONSTRAINT `report_section_workout_conditions_course_id_fk` FOREIGN KEY (`course_id`) REFERENCES `training_courses` (`course_id`),
  CONSTRAINT `report_section_workout_conditions_facility_id_fk` FOREIGN KEY (`facility_id`) REFERENCES `facilities` (`facility_id`),
  CONSTRAINT `report_section_workout_conditions_report_section_id_fk` FOREIGN KEY (`report_section_id`) REFERENCES `report_sections` (`report_section_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `report_sections` (
  `report_section_id` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `report_section_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `report_id` varchar(50) NOT NULL,
  `type` varchar(50) NOT NULL,
  `template_inner_id` varchar(50) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_section_id`),
  UNIQUE KEY `report_section_internal_id` (`report_section_internal_id`),
  UNIQUE KEY `report_id_template_inner_id` (`report_id`, `template_inner_id`),
  CONSTRAINT `report_sections_report_id_fk` FOREIGN KEY (`report_id`) REFERENCES `reports` (`report_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `reports` (
  `report_id` varchar(50) NOT NULL,
  `report_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `organization_uuid` binary(16) NOT NULL,
  `horse_id` bigint UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `is_draft` tinyint(1) NOT NULL DEFAULT '1',
  `first_sent_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_id`),
  UNIQUE KEY `report_internal_id` (`report_internal_id`),
  KEY `reports_organization_uuid_fk` (`organization_uuid`),
  KEY `reports_horse_id_fk` (`horse_id`),
  CONSTRAINT `reports_horse_id_fk` FOREIGN KEY (`horse_id`) REFERENCES `horses` (`horse_id`),
  CONSTRAINT `reports_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `sent_reports` (
  `sent_report_id` varchar(50) NOT NULL,
  `sent_report_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `report_id` varchar(50) NOT NULL,
  `organization_owner_id` varchar(50) NOT NULL,
  `owner_horse_id` varchar(50) NOT NULL,
  `sent_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `first_read_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`sent_report_id`),
  UNIQUE KEY `sent_report_internal_id` (`sent_report_internal_id`),
  KEY `sent_reports_report_id_fk` (`report_id`),
  KEY `sent_reports_organization_owner_id_fk` (`organization_owner_id`),
  KEY `sent_reports_owner_horse_id_fk` (`owner_horse_id`),
  CONSTRAINT `sent_reports_organization_owner_id_fk` FOREIGN KEY (`organization_owner_id`) REFERENCES `organization_owners` (`organization_owner_id`),
  CONSTRAINT `sent_reports_owner_horse_id_fk` FOREIGN KEY (`owner_horse_id`) REFERENCES `owner_horses` (`owner_horse_id`),
  CONSTRAINT `sent_reports_report_id_fk` FOREIGN KEY (`report_id`) REFERENCES `reports` (`report_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `stables` (
  `stable_uuid` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `organization_uuid` binary(16) NOT NULL,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`stable_uuid`),
  KEY `stables_organization_uuid_fk` (`organization_uuid`),
  CONSTRAINT `stables_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `time_series_locations` (
  `time_series_location_id` int NOT NULL AUTO_INCREMENT,
  `time_series_location_uuid` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `training_id` bigint UNSIGNED NOT NULL,
  `time` int NOT NULL,
  `distance` int NOT NULL,
  `latitude` double(8, 6) NOT NULL,
  `longitude` double(9, 6) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`time_series_location_id`),
  UNIQUE KEY `training_id` (`training_id`, `time`),
  CONSTRAINT `training_id_fk_11` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `time_series_results` (
  `time_series_result_id` int NOT NULL AUTO_INCREMENT,
  `time_series_result_uuid` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `training_id` bigint UNSIGNED NOT NULL,
  `time` int NOT NULL,
  `gait` enum('stable', 'walk', 'trot', 'canter', 'gallop') DEFAULT NULL,
  `lead_leg` enum('right', 'left') DEFAULT NULL,
  `speed` float DEFAULT NULL,
  `pitch` float DEFAULT NULL,
  `stride` float DEFAULT NULL,
  `heart_rate` int DEFAULT NULL,
  `acceleration` float NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`time_series_result_id`),
  UNIQUE KEY `training_id` (`training_id`, `time`),
  CONSTRAINT `training_id_fk_10` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `track_condition_history` (
  `track_condition_history_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `stable_uuid` binary(16) NOT NULL,
  `facility_id` bigint UNSIGNED NOT NULL,
  `track_condition` enum('firm', 'good', 'yielding', 'soft') DEFAULT NULL,
  `date` DATE NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `hour` int NOT NULL,
  PRIMARY KEY (`track_condition_history_id`),
  UNIQUE KEY `training_date_index_track_condition_history` (`stable_uuid`, `facility_id`, `date` DESC),
  KEY `facility_id_fk_track_condition_history` (`facility_id`),
  CONSTRAINT `facility_id_fk_track_condition_history` FOREIGN KEY (`facility_id`) REFERENCES `training_facilities` (`facility_id`),
  CONSTRAINT `stable_uuid_id_fk_track_condition_history` FOREIGN KEY (`stable_uuid`) REFERENCES `stables` (`stable_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `training` (
  `training_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `horse_id` bigint UNSIGNED NOT NULL,
  `trainer_uuid` binary(16) DEFAULT NULL,
  `trainer_id` bigint UNSIGNED DEFAULT NULL,
  `stable_uuid` binary(16) NOT NULL,
  `start_at` datetime DEFAULT NULL,
  `end_at` datetime DEFAULT NULL,
  `analysis_started_at` datetime DEFAULT NULL,
  `analysis_ended_at` datetime DEFAULT NULL,
  `location_data_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `location_data_ext` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `gyro_and_acc_data_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `gyro_and_acc_data_ext` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_id`),
  UNIQUE KEY `training_id` (`training_id`),
  KEY `stable_uuid_fk_4` (`stable_uuid`),
  KEY `trainer_uuid_fk` (`trainer_uuid`),
  KEY `idx_horse_id_start_at` (`horse_id`, `start_at`),
  CONSTRAINT `horse_id_fk` FOREIGN KEY (`horse_id`) REFERENCES `horses` (`horse_id`),
  CONSTRAINT `stable_uuid_fk_4` FOREIGN KEY (`stable_uuid`) REFERENCES `stables` (`stable_uuid`),
  CONSTRAINT `trainer_uuid_fk` FOREIGN KEY (`trainer_uuid`) REFERENCES `users` (`user_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `training_comments` (
  `training_comment_uuid` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `training_id` bigint UNSIGNED NOT NULL,
  `user_uuid` binary(16) NOT NULL,
  `comment` text NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_comment_uuid`),
  KEY `training_comments_training_id_fk` (`training_id`),
  KEY `training_comments_user_uuid_fk` (`user_uuid`),
  CONSTRAINT `training_comments_training_id_fk` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`),
  CONSTRAINT `training_comments_user_uuid_fk` FOREIGN KEY (`user_uuid`) REFERENCES `users` (`user_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `training_conditions` (
  `training_condition_uuid` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `training_id` bigint UNSIGNED NOT NULL,
  `going` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `rider_uuid` binary(16) DEFAULT NULL,
  `handler_uuid` binary(16) DEFAULT NULL,
  `is_workout` tinyint(1) NOT NULL DEFAULT '0',
  `app_ver` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_condition_uuid`),
  KEY `training_conditions_training_id_fk` (`training_id`),
  KEY `training_conditions_rider_uuid_fk` (`handler_uuid`),
  KEY `training_conditions_handler_uuid_fk` (`rider_uuid`),
  CONSTRAINT `training_conditions_handler_uuid_fk` FOREIGN KEY (`rider_uuid`) REFERENCES `training_staffs` (`training_staff_uuid`),
  CONSTRAINT `training_conditions_rider_uuid_fk` FOREIGN KEY (`handler_uuid`) REFERENCES `training_staffs` (`training_staff_uuid`),
  CONSTRAINT `training_conditions_training_id_fk` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `training_course_masters` (
  `training_course_master_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `training_course_master_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `course_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `facility_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `facility_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `temp_training_facility_master_uuid` binary(16) DEFAULT NULL,
  PRIMARY KEY (`training_course_master_id`),
  UNIQUE KEY `training_course_master_internal_id` (`training_course_master_internal_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `training_courses` (
  `course_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `course_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `training_course_master_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `course_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `geometry` geometry DEFAULT NULL,
  `distance` float DEFAULT NULL,
  `last_n_furlong` tinyint(1) DEFAULT '2' COMMENT '基準として利用する上がりNハロン',
  `opened_at` datetime DEFAULT NULL,
  `closed_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `temp_facility_id` bigint UNSIGNED DEFAULT NULL,
  PRIMARY KEY (`course_id`),
  UNIQUE KEY `course_internal_id` (`course_internal_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `training_data_upload_log` (
  `training_data_upload_log_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `training_id` bigint UNSIGNED NOT NULL,
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `logged_at` TIMESTAMP NOT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_data_upload_log_id`),
  KEY `training_data_upload_log_training_id_fk` (`training_id`),
  CONSTRAINT `training_data_upload_log_training_id_fk` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `training_facilities` (
  `facility_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `training_facility_master_uuid` binary(16) DEFAULT NULL,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `facility_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `geometry` geometry NOT NULL,
  `distance` float DEFAULT NULL,
  `last_n_furlong` tinyint(1) DEFAULT '2' COMMENT '基準として利用する上がりNハロン',
  `has_track_condition` tinyint(1) NOT NULL DEFAULT '1',
  `opened_at` datetime DEFAULT NULL,
  `closed_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`facility_id`),
  UNIQUE KEY `facility_id` (`facility_id`),
  KEY `training_facilities_training_facility_master_uuid_fk` (`training_facility_master_uuid`),
  CONSTRAINT `training_facilities_training_facility_master_uuid_fk` FOREIGN KEY (`training_facility_master_uuid`) REFERENCES `training_facility_masters` (`training_facility_master_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `training_facility_masters` (
  `training_facility_master_uuid` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `facility_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_facility_master_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `training_indicator_labels` (
  `training_indicator_label_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `training_indicator_id` bigint UNSIGNED NOT NULL,
  `label` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `time` int NOT NULL,
  `distance` int NOT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_indicator_label_id`),
  KEY `training_indicator_labels_training_indicator_id_fk` (`training_indicator_id`),
  CONSTRAINT `training_indicator_labels_training_indicator_id_fk` FOREIGN KEY (`training_indicator_id`) REFERENCES `training_indicators` (`training_indicator_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `training_indicators` (
  `training_indicator_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `training_id` bigint UNSIGNED NOT NULL,
  `period_group_id` int NOT NULL,
  `facility_id` bigint UNSIGNED DEFAULT NULL,
  `course_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `max_heart_rate` int DEFAULT NULL,
  `max_heart_rate_in_all` int DEFAULT NULL,
  `max_heart_rate_in_lap` int DEFAULT NULL,
  `thr100` int DEFAULT NULL,
  `v200` float DEFAULT NULL,
  `one_minute_heart_rate` int DEFAULT NULL,
  `three_minutes_min_heart_rate` int DEFAULT NULL,
  `heart_rate_gap` int DEFAULT NULL,
  `thirty_seconds_after_goal_heart_rate` int DEFAULT NULL,
  `three_minutes_after_goal_min_heart_rate` int DEFAULT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_indicator_id`),
  KEY `training_indicators_training_id_fk` (`training_id`),
  CONSTRAINT `training_indicators_training_id_fk` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `training_modes` (
  `training_mode_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `training_id` bigint UNSIGNED NOT NULL,
  `with_heart_rate` tinyint(1) NOT NULL DEFAULT '0',
  `with_front_leg` tinyint(1) NOT NULL DEFAULT '0',
  `with_hind_leg` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_mode_id`),
  UNIQUE KEY `training_id` (`training_id`),
  CONSTRAINT `training_modes_training_id_fk` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `training_period_max_speed_stats` (
  `training_period_max_speed_stats_uuid` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `training_period_uuid` binary(16) NOT NULL,
  `training_id` bigint UNSIGNED NOT NULL,
  `max_speed` float DEFAULT NULL,
  `max_speed_time` float DEFAULT NULL,
  `max_speed_stride` float DEFAULT NULL,
  `max_speed_pitch` float DEFAULT NULL,
  `left_max_speed` float DEFAULT NULL,
  `left_max_speed_time` float DEFAULT NULL,
  `left_max_speed_stride` float DEFAULT NULL,
  `left_max_speed_pitch` float DEFAULT NULL,
  `right_max_speed` float DEFAULT NULL,
  `right_max_speed_time` float DEFAULT NULL,
  `right_max_speed_stride` float DEFAULT NULL,
  `right_max_speed_pitch` float DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_period_max_speed_stats_uuid`),
  KEY `training_period_max_speed_stats_training_period_uuid_fk` (`training_period_uuid`),
  KEY `training_period_max_speed_stats_training_id_fk` (`training_id`),
  CONSTRAINT `training_period_max_speed_stats_training_id_fk` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`),
  CONSTRAINT `training_period_max_speed_stats_training_period_uuid_fk` FOREIGN KEY (`training_period_uuid`) REFERENCES `training_periods` (`training_period_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `training_period_max_stats` (
  `training_period_max_stats_uuid` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `training_period_uuid` binary(16) NOT NULL,
  `training_id` bigint UNSIGNED NOT NULL,
  `max_speed` float DEFAULT NULL,
  `max_speed_time` float DEFAULT NULL,
  `max_stride` float DEFAULT NULL,
  `max_stride_time` float DEFAULT NULL,
  `max_pitch` float DEFAULT NULL,
  `max_pitch_time` float DEFAULT NULL,
  `left_max_speed` float DEFAULT NULL,
  `left_max_speed_time` float DEFAULT NULL,
  `left_max_stride` float DEFAULT NULL,
  `left_max_stride_time` float DEFAULT NULL,
  `left_max_pitch` float DEFAULT NULL,
  `left_max_pitch_time` float DEFAULT NULL,
  `right_max_speed` float DEFAULT NULL,
  `right_max_speed_time` float DEFAULT NULL,
  `right_max_stride` float DEFAULT NULL,
  `right_max_stride_time` float DEFAULT NULL,
  `right_max_pitch` float DEFAULT NULL,
  `right_max_pitch_time` float DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_period_max_stats_uuid`),
  KEY `training_period_max_stats_training_period_uuid_fk` (`training_period_uuid`),
  KEY `training_period_max_stats_training_id_fk` (`training_id`),
  CONSTRAINT `training_period_max_stats_training_id_fk` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`),
  CONSTRAINT `training_period_max_stats_training_period_uuid_fk` FOREIGN KEY (`training_period_uuid`) REFERENCES `training_periods` (`training_period_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `training_periods` (
  `training_period_uuid` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `training_id` bigint UNSIGNED NOT NULL,
  `period_group_id` int UNSIGNED NOT NULL,
  `period_type` enum('all', 'all_in_facility', 'one_lap') NOT NULL,
  `facility_id` bigint UNSIGNED DEFAULT NULL,
  `course_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `direction` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `start_time` float UNSIGNED NOT NULL,
  `end_time` float UNSIGNED NOT NULL,
  `start_distance` float UNSIGNED NOT NULL,
  `end_distance` float UNSIGNED NOT NULL,
  `lap_count` int UNSIGNED DEFAULT NULL,
  `is_main` tinyint(1) DEFAULT '0',
  `left_leg_ratio` float DEFAULT NULL,
  `total_distance` float DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_period_uuid`),
  UNIQUE KEY `training_periods_training_period_group_lap_unique` (`training_id`, `period_group_id`, `lap_count`),
  KEY `facility_id_fk_7` (`facility_id`),
  CONSTRAINT `facility_id_fk_7` FOREIGN KEY (`facility_id`) REFERENCES `training_facilities` (`facility_id`),
  CONSTRAINT `training_id_fk_8` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `training_results` (
  `training_id` bigint UNSIGNED NOT NULL,
  `period_id` bigint UNSIGNED NOT NULL,
  `period_group_id` int UNSIGNED NOT NULL,
  `period_type` enum('all', 'all_in_facility', 'one_lap') NOT NULL,
  `lap_count` int UNSIGNED DEFAULT NULL,
  `facility_id` bigint UNSIGNED DEFAULT NULL,
  `course_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `start_at` TIMESTAMP NULL DEFAULT NULL,
  `end_at` TIMESTAMP NULL DEFAULT NULL,
  `max_speed` float DEFAULT NULL,
  `max_pitch` float DEFAULT NULL,
  `max_stride` float DEFAULT NULL,
  `average_speed` float DEFAULT NULL,
  `average_pitch` float DEFAULT NULL,
  `average_stride` float DEFAULT NULL,
  `total_distance` float DEFAULT NULL,
  `left_leg_ratio` float DEFAULT NULL,
  `head_motion_forward_average` float DEFAULT NULL,
  `head_motion_vertical_average` float DEFAULT NULL,
  `head_motion_side_average` float DEFAULT NULL,
  `head_motion_direction_average` float DEFAULT NULL,
  `left_max_speed` float DEFAULT NULL,
  `left_max_pitch` float DEFAULT NULL,
  `left_max_stride` float DEFAULT NULL,
  `left_average_speed` float DEFAULT NULL,
  `left_average_pitch` float DEFAULT NULL,
  `left_average_stride` float DEFAULT NULL,
  `left_head_motion_forward_average` float DEFAULT NULL,
  `left_head_motion_vertical_average` float DEFAULT NULL,
  `left_head_motion_side_average` float DEFAULT NULL,
  `left_head_motion_direction_average` float DEFAULT NULL,
  `right_max_speed` float DEFAULT NULL,
  `right_max_pitch` float DEFAULT NULL,
  `right_max_stride` float DEFAULT NULL,
  `right_average_speed` float DEFAULT NULL,
  `right_average_pitch` float DEFAULT NULL,
  `right_average_stride` float DEFAULT NULL,
  `right_head_motion_forward_average` float DEFAULT NULL,
  `right_head_motion_vertical_average` float DEFAULT NULL,
  `right_head_motion_side_average` float DEFAULT NULL,
  `right_head_motion_direction_average` float DEFAULT NULL,
  PRIMARY KEY (`training_id`, `period_id`),
  KEY `facility_id_fk_2_idx` (`facility_id`),
  CONSTRAINT `facility_id_fk_2` FOREIGN KEY (`facility_id`) REFERENCES `training_facilities` (`facility_id`),
  CONSTRAINT `training_id_fk_3` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `training_results_distance_series` (
  `training_id` bigint UNSIGNED NOT NULL,
  `period_id` bigint UNSIGNED NOT NULL,
  `distance_to_end` int NOT NULL,
  `period_type` enum('all', 'all_in_facility', 'one_lap') NOT NULL,
  `gait` enum('stable', 'walk', 'trot', 'canter', 'gallop') DEFAULT NULL,
  `lead_leg` enum('right', 'left') DEFAULT NULL,
  `speed` float DEFAULT NULL,
  `pitch` float DEFAULT NULL,
  `stride` float DEFAULT NULL,
  `head_motion_forward` float DEFAULT NULL,
  `head_motion_vertical` float DEFAULT NULL,
  `head_motion_side` float DEFAULT NULL,
  `head_motion_direction` float DEFAULT NULL,
  `time_to_end` float DEFAULT NULL,
  `heart_rate` int DEFAULT NULL,
  PRIMARY KEY (
    `training_id`,
    `period_id`,
    `distance_to_end`,
    `period_type`
  ),
  CONSTRAINT `training_id_fk_4` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `training_results_location_series` (
  `training_id` bigint UNSIGNED NOT NULL,
  `period_id` bigint UNSIGNED NOT NULL,
  `timestamp` timestamp(3) NOT NULL,
  `distance_to_end` int NOT NULL,
  `latitude` double(8, 6) NOT NULL,
  `longitude` double(9, 6) NOT NULL,
  PRIMARY KEY (`training_id`, `period_id`, `timestamp`),
  CONSTRAINT `training_id_fk_5` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `training_staffs` (
  `training_staff_uuid` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `stable_uuid` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `deleted_at` TIMESTAMP NULL DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_staff_uuid`),
  KEY `stable_uuid_fk_5` (`stable_uuid`),
  CONSTRAINT `stable_uuid_fk_5` FOREIGN KEY (`stable_uuid`) REFERENCES `stables` (`stable_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `upcoming_race_conditions` (
  `upcoming_race_condition_id` int NOT NULL AUTO_INCREMENT,
  `upcoming_race_id` int NOT NULL,
  `condition_raw_string` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `min_age` int DEFAULT NULL,
  `max_age` int DEFAULT NULL,
  `class_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `limit_prize` int DEFAULT NULL,
  `stallion_enterable` tinyint(1) DEFAULT NULL,
  `mare_enterable` tinyint(1) DEFAULT NULL,
  `gelding_enterable` tinyint(1) DEFAULT NULL,
  `international_enterable` tinyint(1) DEFAULT NULL,
  `nar_enterable` tinyint(1) DEFAULT NULL,
  `has_other_conditions` tinyint(1) DEFAULT NULL,
  `impost_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`upcoming_race_condition_id`),
  UNIQUE KEY `upcoming_race_id` (`upcoming_race_id`),
  CONSTRAINT `upcoming_race_conditions_ibfk_1` FOREIGN KEY (`upcoming_race_id`) REFERENCES `upcoming_races` (`upcoming_race_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `upcoming_races` (
  `upcoming_race_id` int NOT NULL AUTO_INCREMENT,
  `race_place_id` int NOT NULL,
  `iteration_number` int NOT NULL,
  `iteration_day_number` int NOT NULL,
  `race_number` int NOT NULL,
  `race_year` int NOT NULL,
  `race_month` int NOT NULL,
  `race_day` int NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `distance` int NOT NULL,
  `track_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`upcoming_race_id`),
  UNIQUE KEY `unique_upcoming_race_constraint` (
    `race_year`,
    `race_place_id`,
    `iteration_number`,
    `iteration_day_number`,
    `race_number`
  ),
  KEY `upcoming_races_race_place_id_fk` (`race_place_id`),
  CONSTRAINT `upcoming_races_race_place_id_fk` FOREIGN KEY (`race_place_id`) REFERENCES `race_places` (`race_place_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `user_roles` (
  `role_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `role_uuid` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `user_uuid` binary(16) DEFAULT NULL,
  `role` enum('admin', 'staff', 'jockey') DEFAULT NULL,
  `organization_uuid` binary(16) DEFAULT NULL,
  `stable_uuid` binary(16) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`role_uuid`),
  UNIQUE KEY `role_id` (`role_id`),
  KEY `stable_id_fk_5` (`stable_uuid`),
  KEY `user_uuid_fk` (`user_uuid`),
  KEY `user_roles_organization_fK` (`organization_uuid`),
  CONSTRAINT `stable_id_fk_5` FOREIGN KEY (`stable_uuid`) REFERENCES `stables` (`stable_uuid`),
  CONSTRAINT `user_roles_organization_fK` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`),
  CONSTRAINT `user_uuid_fk` FOREIGN KEY (`user_uuid`) REFERENCES `users` (`user_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `users` (
  `user_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_uuid` binary(16) NOT NULL DEFAULT(uuid_to_bin(uuid())),
  `firebase_uid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `first_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `middle_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `last_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `deleted_at` TIMESTAMP NULL DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_uuid`),
  UNIQUE KEY `user_id` (`user_id`),
  UNIQUE KEY `firebase_uid` (`firebase_uid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;


/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;


/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;


/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;


/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;


/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;


/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
