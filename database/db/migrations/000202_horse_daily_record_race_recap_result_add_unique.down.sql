ALTER TABLE `horse_daily_records_race_results`
DROP FOREIGN KEY `horse_daily_records_race_results_horse_daily_record_id_fk`,
DROP KEY `horse_daily_records_race_results_horse_daily_record_id_fk`;


ALTER TABLE `horse_daily_records_race_results`
ADD INDEX `horse_daily_records_race_results_horse_daily_record_id_fk` (`horse_daily_record_id`),
ADD CONSTRAINT `horse_daily_records_race_results_horse_daily_record_id_fk` FOREIGN KEY (`horse_daily_record_id`) REFERENCES `horse_daily_records` (`horse_daily_record_id`);


ALTER TABLE `horse_daily_records_race_recaps`
DROP FOREIGN KEY `horse_daily_records_race_recaps_horse_daily_record_id_fk`,
DROP KEY `horse_daily_records_race_recaps_horse_daily_record_id_fk`;


ALTER TABLE `horse_daily_records_race_recaps`
ADD INDEX `race_recap_horse_daily_record_id_fk` (`horse_daily_record_id`),
ADD CONSTRAINT `race_recap_horse_daily_record_id_fk` FOREIGN KEY (`horse_daily_record_id`) REFERENCES `horse_daily_records` (`horse_daily_record_id`);
