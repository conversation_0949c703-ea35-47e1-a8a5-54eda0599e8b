CREATE TABLE `owner_register_onetime_code` (
  `owner_register_onetime_code_internal_id` bigint(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(50) NOT NULL,
  `owner_id` VARCHAR(50) NOT NULL,
  `expired_at` datetime NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`owner_register_onetime_code_internal_id`),
  KEY `idx_owner_id_expired_at` (`owner_id`, `expired_at`),
  KEY `owner_register_onetime_code_owner_id_fk` (`owner_id`),
  CONSTRAINT `owner_register_onetime_code_owner_id_fk` FOREIGN KEY (`owner_id`) REFERENCES `owners` (`owner_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;
