CREATE TABLE `share_reports` (
  `share_report_id` VARCHAR(50) NOT NULL PRIMARY KEY,
  `share_report_internal_id` BIGINT UNSIGNED NOT NULL UNIQUE AUTO_INCREMENT,
  `report_id` VARCHAR(50) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  CONSTRAINT `share_reports_report_id_fk` FOREIGN KEY (`report_id`) REFERENCES `reports` (`report_id`)
);
