CREATE TABLE `farm_areas` (
  `farm_area_id` binary(16) NOT NULL,
  `farm_area_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `order` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`farm_area_id`),
  UNIQUE KEY `farm_area_internal_id` (`farm_area_internal_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `master_farms` (
  `master_farm_id` binary(16) NOT NULL,
  `master_farm_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `farm_area_id` binary(16) NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`master_farm_id`),
  UNIQUE KEY `master_farm_internal_id` (`master_farm_internal_id`),
  KEY `idx_farm_area_id` (`farm_area_id`),
  CONSTRAINT `master_farms_farm_area_id_fk` FOREIGN KEY (`farm_area_id`) REFERENCES `farm_areas` (`farm_area_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `stable_tm_outside_farm` (
  `outside_farm_id` binary(16) NOT NULL,
  `outside_farm_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `organization_uuid` binary(16) NOT NULL,
  `farm_area_id` binary(16) NOT NULL,
  `name` varchar(255) NOT NULL,
  `master_farm_id` binary(16) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`outside_farm_id`),
  UNIQUE KEY `outside_farm_internal_id` (`outside_farm_internal_id`),
  KEY `idx_organization_uuid` (`organization_uuid`),
  KEY `idx_farm_area_id` (`farm_area_id`),
  KEY `idx_master_farm_id` (`master_farm_id`),
  CONSTRAINT `stable_tm_outside_farm_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`),
  CONSTRAINT `stable_tm_outside_farm_farm_area_id_fk` FOREIGN KEY (`farm_area_id`) REFERENCES `farm_areas` (`farm_area_id`),
  CONSTRAINT `stable_tm_outside_farm_master_farm_id_fk` FOREIGN KEY (`master_farm_id`) REFERENCES `master_farms` (`master_farm_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `stable_tm_section` (
  `section_id` binary(16) NOT NULL,
  `section_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `stable_uuid` binary(16) NOT NULL,
  `start_year` int NOT NULL,
  `start_month` int NOT NULL,
  `start_day` int NOT NULL,
  `end_year` int NOT NULL,
  `end_month` int NOT NULL,
  `end_day` int NOT NULL,
  `status` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`section_id`),
  UNIQUE KEY `section_internal_id` (`section_internal_id`),
  KEY `idx_stable_uuid` (`stable_uuid`),
  CONSTRAINT `stable_tm_section_stable_uuid_fk` FOREIGN KEY (`stable_uuid`) REFERENCES `stables` (`stable_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `stable_tm_transport_queue_tickets` (
  `transport_queue_ticket_id` binary(16) NOT NULL,
  `transport_queue_ticket_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `section_id` binary(16) NOT NULL,
  `ticket_key` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`transport_queue_ticket_id`),
  UNIQUE KEY `transport_queue_ticket_internal_id` (`transport_queue_ticket_internal_id`),
  KEY `idx_section_id` (`section_id`),
  CONSTRAINT `transport_queue_tickets_section_id_fk` FOREIGN KEY (`section_id`) REFERENCES `stable_tm_section` (`section_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `stable_tm_fixed_slots` (
  `fixed_slot_id` binary(16) NOT NULL,
  `fixed_slot_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `section_id` binary(16) NOT NULL,
  `number_of_section` int NOT NULL,
  `slot_num` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`fixed_slot_id`),
  UNIQUE KEY `fixed_slot_internal_id` (`fixed_slot_internal_id`),
  KEY `idx_section_id` (`section_id`),
  CONSTRAINT `fixed_slots_section_id_fk` FOREIGN KEY (`section_id`) REFERENCES `stable_tm_section` (`section_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `stable_tm_transport_daily_records` (
  `transport_daily_record_id` binary(16) NOT NULL,
  `transport_daily_record_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `section_id` binary(16) NOT NULL,
  `stable_uuid` binary(16) NOT NULL,
  `year` int NOT NULL,
  `month` int NOT NULL,
  `day` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`transport_daily_record_id`),
  UNIQUE KEY `transport_daily_record_internal_id` (`transport_daily_record_internal_id`),
  KEY `idx_section_id` (`section_id`),
  KEY `idx_stable_uuid` (`stable_uuid`),
  KEY `idx_year_month_day` (`year`, `month`, `day`),
  CONSTRAINT `transport_daily_records_section_id_fk` FOREIGN KEY (`section_id`) REFERENCES `stable_tm_section` (`section_id`),
  CONSTRAINT `transport_daily_records_stable_uuid_fk` FOREIGN KEY (`stable_uuid`) REFERENCES `stables` (`stable_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `stable_tm_transport_records` (
  `transport_record_id` binary(16) NOT NULL,
  `transport_record_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `transport_daily_record_id` binary(16) NOT NULL,
  `type` varchar(255) NOT NULL COMMENT 'in / out / expedition_outbound / expedition_inbound / farm_transfer',
  `horse_id` bigint UNSIGNED NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`transport_record_id`),
  UNIQUE KEY `transport_record_internal_id` (`transport_record_internal_id`),
  KEY `idx_transport_daily_record_id` (`transport_daily_record_id`),
  KEY `idx_horse_id` (`horse_id`),
  CONSTRAINT `transport_records_transport_daily_record_id_fk` FOREIGN KEY (`transport_daily_record_id`) REFERENCES `stable_tm_transport_daily_records` (`transport_daily_record_id`),
  CONSTRAINT `transport_records_horse_id_fk` FOREIGN KEY (`horse_id`) REFERENCES `horses` (`horse_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `stable_tm_transport_in_statuses` (
  `transport_in_status_id` binary(16) NOT NULL,
  `transport_in_status_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `transport_record_id` binary(16) NOT NULL,
  `staff_uuid` binary(16) DEFAULT NULL,
  `is_horse_van_arranged` tinyint(1) DEFAULT '0',
  `is_quarantine_applied` tinyint(1) DEFAULT '0',
  `next_race` text,
  `comment` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`transport_in_status_id`),
  UNIQUE KEY `idx_transport_record_id` (`transport_record_id`),
  UNIQUE KEY `idx_transport_in_status_internal_id` (`transport_in_status_internal_id`),
  KEY `idx_staff_uuid` (`staff_uuid`),
  CONSTRAINT `transport_in_statuses_transport_record_id_fk` FOREIGN KEY (`transport_record_id`) REFERENCES `stable_tm_transport_records` (`transport_record_id`),
  CONSTRAINT `transport_in_statuses_staff_uuid_fk` FOREIGN KEY (`staff_uuid`) REFERENCES `staffs` (`staff_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `stable_tm_transport_out_statuses` (
  `transport_out_status_id` binary(16) NOT NULL,
  `transport_out_status_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `transport_record_id` binary(16) NOT NULL,
  `staff_uuid` binary(16) DEFAULT NULL,
  `is_stable_out_procedure_completed` tinyint(1) DEFAULT '0' COMMENT '退厩手続きが完了したかどうか',
  `is_horse_van_arranged` tinyint(1) DEFAULT '0' COMMENT '馬運車手配したかどうか',
  `farm_id` binary(16) DEFAULT NULL,
  `comment` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`transport_out_status_id`),
  UNIQUE KEY `idx_transport_record_id` (`transport_record_id`),
  UNIQUE KEY `idx_transport_out_status_internal_id` (`transport_out_status_internal_id`),
  KEY `idx_staff_uuid` (`staff_uuid`),
  KEY `idx_farm_id` (`farm_id`),
  CONSTRAINT `transport_out_statuses_transport_record_id_fk` FOREIGN KEY (`transport_record_id`) REFERENCES `stable_tm_transport_records` (`transport_record_id`),
  CONSTRAINT `transport_out_statuses_staff_uuid_fk` FOREIGN KEY (`staff_uuid`) REFERENCES `staffs` (`staff_uuid`),
  CONSTRAINT `transport_out_statuses_farm_id_fk` FOREIGN KEY (`farm_id`) REFERENCES `stable_tm_outside_farm` (`outside_farm_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE `stable_tm_transport_handover_notes` (
  `transport_out_handover_note_id` binary(16) NOT NULL,
  `transport_out_handover_note_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `transport_out_status_id` binary(16) NOT NULL,
  `body` text,
  `latest_horse_shoeing_date` date DEFAULT NULL,
  `latest_horse_shoeing_farrier` varchar(255) DEFAULT NULL,
  `latest_horse_shoeing_body` varchar(255) DEFAULT NULL,
  `latest_horse_body_weight_date` date DEFAULT NULL,
  `latest_horse_body_weight` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`transport_out_handover_note_id`),
  UNIQUE KEY `transport_out_handover_note_internal_id` (`transport_out_handover_note_internal_id`),
  KEY `idx_transport_out_status_id` (`transport_out_status_id`),
  CONSTRAINT `transport_handover_notes_transport_out_status_id_fk` FOREIGN KEY (`transport_out_status_id`) REFERENCES `stable_tm_transport_out_statuses` (`transport_out_status_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;
