CREATE TABLE `gyro_and_acc_error_indicators` (
  `gyro_and_acc_error_indicator_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `training_id` bigint UNSIGNED NOT NULL,
  `lead_leg_deficient_count` int UNSIGNED NOT NULL DEFAULT '0',
  `lead_leg_excess_count` int UNSIGNED NOT NULL DEFAULT '0',
  `is_anomaly` tinyint(1) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`gyro_and_acc_error_indicator_id`),
  KEY `gyro_and_acc_error_indicators_training_id_fk` (`training_id`),
  CONSTRAINT `gyro_and_acc_error_indicators_training_id_fk` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;
