CREATE TABLE `feature_flags` (
  `feature_flag_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `organization_uuid` binary(16) NOT NULL,
  `monthly_report` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`feature_flag_id`),
  UNIQUE KEY `feature_flags_organization_uuid_unique` (`organization_uuid`),
  KEY `feature_flags_organization_uuid_fk` (`organization_uuid`),
  CONSTRAINT `feature_flags_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;
