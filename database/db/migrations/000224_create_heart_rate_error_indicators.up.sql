DROP TABLE IF EXISTS `training_analysis_results`;


CREATE TABLE `heart_rate_error_indicators` (
  `heart_rate_error_indicator_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `training_id` bigint UNSIGNED NOT NULL,
  `time_count_all` int UNSIGNED NOT NULL DEFAULT 0,
  `time_count_on_peak` int UNSIGNED NOT NULL DEFAULT 0,
  `time_count_after_peak` int UNSIGNED NOT NULL DEFAULT 0,
  `heart_rate_max_up_all` int UNSIGNED NOT NULL DEFAULT 0,
  `heart_rate_max_down_all` int UNSIGNED NOT NULL DEFAULT 0,
  `heart_rate_max_up_on_peak` int UNSIGNED NOT NULL DEFAULT 0,
  `heart_rate_max_down_on_peak` int UNSIGNED NOT NULL DEFAULT 0,
  `heart_rate_max_up_after_peak` int UNSIGNED NOT NULL DEFAULT 0,
  `heart_rate_max_down_after_peak` int UNSIGNED NOT NULL DEFAULT 0,
  `heart_rate_deficient_count_all` int UNSIGNED NOT NULL DEFAULT 0,
  `heart_rate_deficient_count_on_peak` int UNSIGNED NOT NULL DEFAULT 0,
  `heart_rate_deficient_count_after_peak` int UNSIGNED NOT NULL DEFAULT 0,
  `heart_rate_max_value_all` int UNSIGNED NOT NULL DEFAULT 0,
  `heart_rate_max_value_on_peak` int UNSIGNED NOT NULL DEFAULT 0,
  `heart_rate_max_value_after_peak` int UNSIGNED NOT NULL DEFAULT 0,
  `max_speed_all` float DEFAULT NULL,
  `max_speed_on_peak` float DEFAULT NULL,
  `max_speed_after_peak` float DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`heart_rate_error_indicator_id`),
  KEY `heart_rate_error_indicators_training_id_fk` (`training_id`),
  CONSTRAINT `heart_rate_error_indicators_training_id_fk` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;
