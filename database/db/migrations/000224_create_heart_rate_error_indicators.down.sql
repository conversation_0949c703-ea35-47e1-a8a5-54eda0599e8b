DROP TABLE IF EXISTS `heart_rate_error_indicators`;


CREATE TABLE `training_analysis_results` (
  `training_analysis_result_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `training_id` bigint UNSIGNED NOT NULL,
  `correct_heart_rate_data` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_analysis_result_id`),
  UNIQUE KEY `training_id` (`training_id`),
  CONSTRAINT `training_analysis_results_training_id_fk` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;
