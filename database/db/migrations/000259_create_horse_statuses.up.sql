CREATE TABLE `horse_statuses` (
  `horse_status_id` binary(16) NOT NULL,
  `horse_status_internal_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `horse_id` bigint UNSIGNED NOT NULL,
  `in_stable` tinyint(1) NOT NULL DEFAULT '0',
  `outside_farm_id` binary(16) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_status_id`),
  UNIQUE KEY `horse_status_internal_id` (`horse_status_internal_id`),
  KEY `idx_horse_id` (`horse_id`),
  KEY `idx_outside_farm_id` (`outside_farm_id`),
  CONSTRAINT `horse_statuses_horse_id_fk` FOREIGN KEY (`horse_id`) REFERENCES `horses` (`horse_id`),
  CONSTRAINT `horse_statuses_outside_farm_id_fk` FOREIGN KEY (`outside_farm_id`) REFERENCES `stable_tm_outside_farm` (`outside_farm_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;
