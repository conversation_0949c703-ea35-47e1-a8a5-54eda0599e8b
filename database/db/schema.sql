CREATE TABLE `accessible_courses` (
  `accessible_course_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `stable_uuid` binary(16) NOT NULL,
  `course_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`accessible_course_id`),
  UNIQUE KEY `idx_stable_uuid_course_id` (`stable_uuid`,`course_id`),
  CONSTRAINT `accessible_courses_stable_uuid_fk` FOREIGN KEY (`stable_uuid`) REFERENCES `stables` (`stable_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `accessible_facilities` (
  `stable_uuid` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `facility_id` bigint unsigned NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`stable_uuid`,`facility_id`),
  KEY `facility_id_fk_1` (`facility_id`),
  CONSTRAINT `facility_id_fk_1` FOREIGN KEY (`facility_id`) REFERENCES `training_facilities` (`facility_id`),
  CONSTRAINT `stable_uuid_fk_1` FOREIGN KEY (`stable_uuid`) REFERENCES `stables` (`stable_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `aggregated_stats_furlong_line_based` (
  `aggregated_stats_furlong_line_based_uuid` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `training_period_uuid` binary(16) NOT NULL,
  `furlong` int NOT NULL,
  `furlong_time` float DEFAULT NULL,
  `passed_time` int NOT NULL,
  `distance` int NOT NULL,
  `average_speed` float DEFAULT NULL,
  `average_pitch` float DEFAULT NULL,
  `average_stride` float DEFAULT NULL,
  `average_heart_rate` int DEFAULT NULL,
  `max_heart_rate` int DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`aggregated_stats_furlong_line_based_uuid`),
  KEY `training_id_fk_12` (`training_period_uuid`),
  CONSTRAINT `training_id_fk_12` FOREIGN KEY (`training_period_uuid`) REFERENCES `training_periods` (`training_period_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `ai_generate_logs` (
  `ai_generate_log_id` varchar(50) NOT NULL,
  `ai_generate_log_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `model` varchar(100) DEFAULT NULL,
  `request_prompt` text,
  `response` text,
  `prompt_tokens` int unsigned DEFAULT NULL,
  `completion_tokens` int unsigned DEFAULT NULL,
  `total_tokens` int unsigned DEFAULT NULL,
  `finish_reason` varchar(100) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ai_generate_log_id`),
  UNIQUE KEY `ai_generate_log_internal_id` (`ai_generate_log_internal_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `associations` (
  `association_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `association_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `association_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `association_type` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`association_id`),
  UNIQUE KEY `association_internal_id` (`association_internal_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `business_trip_histories` (
  `business_trip_history_id` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `business_trip_history_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_uuid` binary(16) NOT NULL,
  `staff_name` varchar(255) DEFAULT NULL,
  `destination_name` varchar(255) DEFAULT NULL,
  `start_year` int DEFAULT NULL,
  `start_month` int DEFAULT NULL,
  `start_day` int DEFAULT NULL,
  `end_year` int DEFAULT NULL,
  `end_month` int DEFAULT NULL,
  `end_day` int DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`business_trip_history_id`),
  UNIQUE KEY `business_trip_history_internal_id` (`business_trip_history_internal_id`),
  KEY `business_trip_histories_organization_uuid_fk` (`organization_uuid`),
  CONSTRAINT `business_trip_histories_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `business_trip_history_horses` (
  `business_trip_history_horse_id` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `business_trip_history_id` binary(16) NOT NULL,
  `horse_id` bigint unsigned NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`business_trip_history_horse_id`),
  UNIQUE KEY `business_trip_history_horses_unique` (`business_trip_history_id`,`horse_id`),
  KEY `business_trip_history_horses_horse_id_fk` (`horse_id`),
  CONSTRAINT `business_trip_history_horses_business_trip_history_id_fk` FOREIGN KEY (`business_trip_history_id`) REFERENCES `business_trip_histories` (`business_trip_history_id`),
  CONSTRAINT `business_trip_history_horses_horse_id_fk` FOREIGN KEY (`horse_id`) REFERENCES `horses` (`horse_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `contracts` (
  `contract_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_uuid` binary(16) NOT NULL,
  `has_hn_contract` tinyint(1) NOT NULL DEFAULT '0',
  `has_orm_contract` tinyint(1) NOT NULL DEFAULT '0',
  `has_orm_ai_contract` tinyint(1) NOT NULL DEFAULT '0',
  `has_stm_contracts` tinyint(1) NOT NULL DEFAULT '0',
  `has_rm_contract` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`contract_id`),
  UNIQUE KEY `contracts_organization_uuid_unique` (`organization_uuid`),
  KEY `contracts_organization_uuid_fk` (`organization_uuid`),
  CONSTRAINT `contracts_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `device_groups` (
  `organization_uuid` binary(16) NOT NULL,
  `stable_uuid` binary(16) DEFAULT NULL,
  `device_group_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  `hr_device_id` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `has_head_sensor` tinyint(1) DEFAULT '0',
  `has_front_leg_sensor` tinyint(1) DEFAULT '1',
  `has_hind_leg_sensor` tinyint(1) DEFAULT '1',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`device_group_id`),
  UNIQUE KEY `device_group_id` (`device_group_id`),
  UNIQUE KEY `uq_name_stable_uuid` (`name`,`stable_uuid`),
  KEY `stable_uuid_fk` (`stable_uuid`),
  KEY `device_groups_organization_uuid_fk` (`organization_uuid`),
  CONSTRAINT `device_groups_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`),
  CONSTRAINT `stable_uuid_fk` FOREIGN KEY (`stable_uuid`) REFERENCES `stables` (`stable_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `distance_series_results` (
  `distance_series_result_uuid` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `training_id` bigint unsigned NOT NULL,
  `distance` int NOT NULL,
  `gait` enum('stable','walk','trot','canter','gallop') DEFAULT NULL,
  `lead_leg` enum('right','left') DEFAULT NULL,
  `speed` float DEFAULT NULL,
  `pitch` float DEFAULT NULL,
  `stride` float DEFAULT NULL,
  `heart_rate` int DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`distance_series_result_uuid`),
  UNIQUE KEY `training_id` (`training_id`,`distance`),
  CONSTRAINT `training_id_fk_9` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `email_logs` (
  `email_log_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `email` varchar(255) NOT NULL,
  `recipient_firebase_uid` varchar(50) DEFAULT NULL,
  `sender_firebase_uid` varchar(50) DEFAULT NULL,
  `email_template_key` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`email_log_internal_id`),
  KEY `idx_email` (`email`),
  KEY `idx_recipient_firebase_uid` (`recipient_firebase_uid`),
  KEY `idx_sender_firebase_uid` (`sender_firebase_uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `facilities` (
  `facility_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `facility_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `association_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `facility_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`facility_id`),
  UNIQUE KEY `facility_internal_id` (`facility_internal_id`),
  KEY `facilities_association_id_fk` (`association_id`),
  CONSTRAINT `facilities_association_id_fk` FOREIGN KEY (`association_id`) REFERENCES `associations` (`association_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `farm_areas` (
  `farm_area_id` binary(16) NOT NULL,
  `farm_area_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `order` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`farm_area_id`),
  UNIQUE KEY `farm_area_internal_id` (`farm_area_internal_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `feature_flags` (
  `feature_flag_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_uuid` binary(16) NOT NULL,
  `monthly_report` tinyint(1) NOT NULL DEFAULT '0',
  `business_trip` tinyint(1) NOT NULL DEFAULT '0',
  `language_setting` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`feature_flag_id`),
  UNIQUE KEY `feature_flags_organization_uuid_unique` (`organization_uuid`),
  KEY `feature_flags_organization_uuid_fk` (`organization_uuid`),
  CONSTRAINT `feature_flags_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `firmware_versions` (
  `major` int unsigned NOT NULL,
  `minor` int unsigned NOT NULL,
  `patch` int unsigned NOT NULL,
  `data_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`major`,`minor`,`patch`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `furlong_lines` (
  `furlong_line_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `facility_id` bigint unsigned NOT NULL,
  `course_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `furlong` int unsigned NOT NULL,
  `direction` enum('right','left','straight') NOT NULL,
  `line` geometry NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`furlong_line_internal_id`),
  UNIQUE KEY `idx_facility_id_furlong_direction` (`facility_id`,`furlong`,`direction`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `gait_analysis_results` (
  `gait_analysis_result_uuid` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `training_id` bigint unsigned NOT NULL,
  `start_at` timestamp NOT NULL,
  `end_at` timestamp NOT NULL,
  `gait` enum('stable','walk','trot','canter','gallop') DEFAULT NULL,
  `impact_left_front` float DEFAULT NULL,
  `impact_right_front` float DEFAULT NULL,
  `impact_left_back` float DEFAULT NULL,
  `impact_right_back` float DEFAULT NULL,
  `swing_time_ratio_left_front` float DEFAULT NULL,
  `swing_time_ratio_right_front` float DEFAULT NULL,
  `swing_time_ratio_left_back` float DEFAULT NULL,
  `swing_time_ratio_right_back` float DEFAULT NULL,
  `foot_on_angle_left_front` float DEFAULT NULL,
  `foot_on_angle_right_front` float DEFAULT NULL,
  `foot_on_angle_left_back` float DEFAULT NULL,
  `foot_on_angle_right_back` float DEFAULT NULL,
  `foot_off_angle_left_front` float DEFAULT NULL,
  `foot_off_angle_right_front` float DEFAULT NULL,
  `foot_off_angle_left_back` float DEFAULT NULL,
  `foot_off_angle_right_back` float DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`gait_analysis_result_uuid`),
  KEY `training_id_fk_6` (`training_id`),
  CONSTRAINT `training_id_fk_6` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `gyro_and_acc_error_indicators` (
  `gyro_and_acc_error_indicator_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `training_id` bigint unsigned NOT NULL,
  `lead_leg_deficient_count` int unsigned NOT NULL DEFAULT '0',
  `lead_leg_excess_count` int unsigned NOT NULL DEFAULT '0',
  `is_anomaly` tinyint(1) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`gyro_and_acc_error_indicator_id`),
  KEY `gyro_and_acc_error_indicators_training_id_fk` (`training_id`),
  CONSTRAINT `gyro_and_acc_error_indicators_training_id_fk` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `heart_rate_error_indicators` (
  `heart_rate_error_indicator_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `training_id` bigint unsigned NOT NULL,
  `time_count_all` int unsigned NOT NULL DEFAULT '0',
  `time_count_on_peak` int unsigned NOT NULL DEFAULT '0',
  `time_count_after_peak` int unsigned NOT NULL DEFAULT '0',
  `heart_rate_max_up_all` int unsigned NOT NULL DEFAULT '0',
  `heart_rate_max_down_all` int unsigned NOT NULL DEFAULT '0',
  `heart_rate_max_up_on_peak` int unsigned NOT NULL DEFAULT '0',
  `heart_rate_max_down_on_peak` int unsigned NOT NULL DEFAULT '0',
  `heart_rate_max_up_after_peak` int unsigned NOT NULL DEFAULT '0',
  `heart_rate_max_down_after_peak` int unsigned NOT NULL DEFAULT '0',
  `heart_rate_deficient_count_all` int unsigned NOT NULL DEFAULT '0',
  `heart_rate_deficient_count_on_peak` int unsigned NOT NULL DEFAULT '0',
  `heart_rate_deficient_count_after_peak` int unsigned NOT NULL DEFAULT '0',
  `heart_rate_max_value_all` int unsigned NOT NULL DEFAULT '0',
  `heart_rate_max_value_on_peak` int unsigned NOT NULL DEFAULT '0',
  `heart_rate_max_value_after_peak` int unsigned NOT NULL DEFAULT '0',
  `max_speed_all` float DEFAULT NULL,
  `max_speed_on_peak` float DEFAULT NULL,
  `max_speed_after_peak` float DEFAULT NULL,
  `is_anomaly` tinyint(1) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`heart_rate_error_indicator_id`),
  KEY `heart_rate_error_indicators_training_id_fk` (`training_id`),
  CONSTRAINT `heart_rate_error_indicators_training_id_fk` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `horse_course_pitch_averages` (
  `horse_course_pitch_average_uuid` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `master_horse_id` varchar(50) NOT NULL,
  `course_id` varchar(50) NOT NULL,
  `speed` int unsigned NOT NULL,
  `relative_average_pitch` float DEFAULT NULL,
  `absolute_average_pitch` float DEFAULT NULL,
  `deviation_score` float DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_course_pitch_average_uuid`),
  UNIQUE KEY `unique_horse_course_speed` (`master_horse_id`,`course_id`,`speed`),
  KEY `horse_course_pitch_averages_master_horse_id_fk` (`master_horse_id`),
  KEY `horse_course_pitch_averages_course_id_idx` (`course_id`),
  CONSTRAINT `horse_course_pitch_averages_master_horse_id_fk` FOREIGN KEY (`master_horse_id`) REFERENCES `master_horses` (`master_horse_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `horse_daily_records` (
  `horse_daily_record_id` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `horse_daily_record_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_uuid` binary(16) NOT NULL,
  `horse_id` bigint unsigned NOT NULL,
  `created_user_uuid` binary(16) DEFAULT NULL,
  `year` int NOT NULL,
  `month` int NOT NULL,
  `day` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_daily_record_id`),
  UNIQUE KEY `horse_daily_record_internal_id` (`horse_daily_record_internal_id`),
  UNIQUE KEY `horse_daily_records_horse_id_year_month_day_unique` (`horse_id`,`year`,`month`,`day`),
  KEY `idx_organization_uuid` (`organization_uuid`),
  KEY `idx_horse_daily_records_year_month_day` (`year`,`month`,`day`),
  KEY `horse_daily_records_created_user_uuid_fk` (`created_user_uuid`),
  CONSTRAINT `horse_daily_records_created_user_uuid_fk` FOREIGN KEY (`created_user_uuid`) REFERENCES `users` (`user_uuid`),
  CONSTRAINT `horse_daily_records_horse_id_fk` FOREIGN KEY (`horse_id`) REFERENCES `horses` (`horse_id`),
  CONSTRAINT `horse_daily_records_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `horse_daily_records_horse_bodies` (
  `horse_body_record_id` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `horse_body_record_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `horse_daily_record_id` binary(16) NOT NULL,
  `body_weight` int DEFAULT NULL,
  `am_body_temperature` decimal(3,1) DEFAULT NULL,
  `pm_body_temperature` decimal(3,1) DEFAULT NULL,
  `am_horse_body_comment` text,
  `pm_horse_body_comment` text,
  `am_horse_body_care` text,
  `pm_horse_body_care` text,
  `free_comment` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_body_record_id`),
  UNIQUE KEY `horse_body_record_internal_id` (`horse_body_record_internal_id`),
  UNIQUE KEY `horse_body_records_horse_daily_record_id_fk` (`horse_daily_record_id`),
  CONSTRAINT `horse_body_records_horse_daily_record_id_fk` FOREIGN KEY (`horse_daily_record_id`) REFERENCES `horse_daily_records` (`horse_daily_record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `horse_daily_records_horse_body_affected_area_photos` (
  `horse_body_affected_area_photo_id` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `horse_body_affected_area_photo_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `horse_daily_record_id` binary(16) NOT NULL,
  `daypart` varchar(50) NOT NULL,
  `photo_path` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_body_affected_area_photo_id`),
  UNIQUE KEY `idx_horse_body_affected_area_photo_internal_id` (`horse_body_affected_area_photo_internal_id`),
  KEY `horse_body_affected_area_photos_horse_daily_record_id_fk` (`horse_daily_record_id`),
  CONSTRAINT `horse_body_affected_area_photos_horse_daily_record_id_fk` FOREIGN KEY (`horse_daily_record_id`) REFERENCES `horse_daily_records` (`horse_daily_record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `horse_daily_records_horse_body_photos` (
  `horse_body_photo_id` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `horse_body_photo_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `horse_daily_record_id` binary(16) NOT NULL,
  `photo_path` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_body_photo_id`),
  UNIQUE KEY `horse_body_photo_internal_id` (`horse_body_photo_internal_id`),
  KEY `horse_body_photos_horse_daily_record_id_fk` (`horse_daily_record_id`),
  CONSTRAINT `horse_body_photos_horse_daily_record_id_fk` FOREIGN KEY (`horse_daily_record_id`) REFERENCES `horse_daily_records` (`horse_daily_record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `horse_daily_records_horse_medical_treatment_affected_area_photos` (
  `horse_medical_treatment_affected_area_photo_id` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `horse_medical_treatment_affected_area_photo_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `horse_medical_treatment_record_id` binary(16) NOT NULL,
  `photo_path` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_medical_treatment_affected_area_photo_id`),
  UNIQUE KEY `idx_horse_medical_treatment_affected_area_photo_internal_id` (`horse_medical_treatment_affected_area_photo_internal_id`),
  KEY `horse_medical_treatment_affected_area_photos_record_id_fk` (`horse_medical_treatment_record_id`),
  CONSTRAINT `horse_medical_treatment_affected_area_photos_record_id_fk` FOREIGN KEY (`horse_medical_treatment_record_id`) REFERENCES `horse_daily_records_horse_medical_treatments` (`horse_medical_treatment_record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `horse_daily_records_horse_medical_treatment_invoice_photos` (
  `horse_medical_treatment_invoice_photo_id` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `horse_medical_treatment_invoice_photo_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `horse_medical_treatment_record_id` binary(16) NOT NULL,
  `photo_path` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_medical_treatment_invoice_photo_id`),
  UNIQUE KEY `idx_horse_medical_treatment_invoice_photo_internal_id` (`horse_medical_treatment_invoice_photo_internal_id`),
  KEY `horse_medical_treatment_invoice_photos_record_id_fk` (`horse_medical_treatment_record_id`),
  CONSTRAINT `horse_medical_treatment_invoice_photos_record_id_fk` FOREIGN KEY (`horse_medical_treatment_record_id`) REFERENCES `horse_daily_records_horse_medical_treatments` (`horse_medical_treatment_record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `horse_daily_records_horse_medical_treatments` (
  `horse_medical_treatment_record_id` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `horse_medical_treatment_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `horse_daily_record_id` binary(16) NOT NULL,
  `medical_treatment_reason` text,
  `medical_treatment_inspection` text,
  `medical_treatment_result` text,
  `medical_treatment_detail` text,
  `organization_veterinarian_id` binary(16) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_medical_treatment_record_id`),
  UNIQUE KEY `idx_horse_medical_treatment_internal_id` (`horse_medical_treatment_internal_id`),
  KEY `horse_medical_treatments_horse_daily_record_id_fk` (`horse_daily_record_id`),
  KEY `fk_horse_medical_treatments_organization_veterinarian_id` (`organization_veterinarian_id`),
  CONSTRAINT `fk_horse_medical_treatments_organization_veterinarian_id` FOREIGN KEY (`organization_veterinarian_id`) REFERENCES `organization_veterinarians` (`organization_veterinarian_id`),
  CONSTRAINT `horse_medical_treatments_horse_daily_record_id_fk` FOREIGN KEY (`horse_daily_record_id`) REFERENCES `horse_daily_records` (`horse_daily_record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `horse_daily_records_horse_shoeing_invoice_photos` (
  `horse_shoeing_invoice_photo_id` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `horse_shoeing_invoice_photo_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `horse_shoeing_record_id` binary(16) NOT NULL,
  `photo_path` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_shoeing_invoice_photo_id`),
  UNIQUE KEY `idx_horse_shoeing_invoice_photo_internal_id` (`horse_shoeing_invoice_photo_internal_id`),
  KEY `horse_shoeing_invoice_photos_record_id_fk` (`horse_shoeing_record_id`),
  CONSTRAINT `horse_shoeing_invoice_photos_record_id_fk` FOREIGN KEY (`horse_shoeing_record_id`) REFERENCES `horse_daily_records_horse_shoeings` (`horse_shoeing_record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `horse_daily_records_horse_shoeings` (
  `horse_shoeing_record_id` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `horse_shoeing_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `horse_daily_record_id` binary(16) NOT NULL,
  `horse_shoeing_treatment_type` varchar(255) DEFAULT NULL,
  `organization_farrier_id` binary(16) DEFAULT NULL,
  `comment` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_shoeing_record_id`),
  UNIQUE KEY `idx_horse_shoeing_internal_id` (`horse_shoeing_internal_id`),
  KEY `horse_shoeings_horse_daily_record_id_fk` (`horse_daily_record_id`),
  KEY `fk_horse_shoeings_organization_farrier_id` (`organization_farrier_id`),
  CONSTRAINT `fk_horse_shoeings_organization_farrier_id` FOREIGN KEY (`organization_farrier_id`) REFERENCES `organization_farriers` (`organization_farrier_id`),
  CONSTRAINT `horse_shoeings_horse_daily_record_id_fk` FOREIGN KEY (`horse_daily_record_id`) REFERENCES `horse_daily_records` (`horse_daily_record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `horse_daily_records_race_recaps` (
  `race_recap_id` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `race_recap_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `horse_daily_record_id` binary(16) NOT NULL,
  `attendance` varchar(100) DEFAULT NULL COMMENT '臨場',
  `staff_uuid` binary(16) DEFAULT NULL,
  `equipment` varchar(255) DEFAULT NULL,
  `transport_comment` text,
  `stall_comment` text,
  `paddock_comment` text,
  `warm_up_comment` text,
  `gate_comment` text,
  `race_strategy_comment` text,
  `after_race_comment` text,
  `jockey_comment` text,
  `trainer_comment` text,
  `next_race_comment` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`race_recap_id`),
  UNIQUE KEY `idx_race_recap_internal_id` (`race_recap_internal_id`),
  UNIQUE KEY `horse_daily_records_race_recaps_horse_daily_record_id_fk` (`horse_daily_record_id`),
  KEY `horse_daily_records_race_recaps_staff_uuid_fk` (`staff_uuid`),
  CONSTRAINT `horse_daily_records_race_recaps_horse_daily_record_id_fk` FOREIGN KEY (`horse_daily_record_id`) REFERENCES `horse_daily_records` (`horse_daily_record_id`),
  CONSTRAINT `horse_daily_records_race_recaps_staff_uuid_fk` FOREIGN KEY (`staff_uuid`) REFERENCES `staffs` (`staff_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `horse_daily_records_race_results` (
  `race_result_id` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `race_result_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `horse_daily_record_id` binary(16) NOT NULL,
  `race_place_id` int DEFAULT NULL,
  `race_number` int DEFAULT NULL,
  `race_name` varchar(255) DEFAULT NULL,
  `distance` int DEFAULT NULL,
  `going` varchar(50) DEFAULT NULL,
  `track_type` varchar(100) DEFAULT NULL,
  `jockey_name` varchar(100) DEFAULT NULL,
  `weight` int DEFAULT NULL,
  `before_race_weight_diff` int DEFAULT NULL,
  `rank` int DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`race_result_id`),
  UNIQUE KEY `idx_race_result_internal_id` (`race_result_internal_id`),
  UNIQUE KEY `horse_daily_records_race_results_horse_daily_record_id_fk` (`horse_daily_record_id`),
  KEY `horse_daily_records_race_results_race_place_id_fk` (`race_place_id`),
  CONSTRAINT `horse_daily_records_race_results_horse_daily_record_id_fk` FOREIGN KEY (`horse_daily_record_id`) REFERENCES `horse_daily_records` (`horse_daily_record_id`),
  CONSTRAINT `horse_daily_records_race_results_race_place_id_fk` FOREIGN KEY (`race_place_id`) REFERENCES `race_places` (`race_place_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `horse_daily_records_training_partners` (
  `training_partner_id` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `training_partner_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `training_record_uuid` binary(16) NOT NULL,
  `horse_id` bigint unsigned DEFAULT NULL,
  `rank` int DEFAULT NULL COMMENT '着順',
  `horse_name` varchar(255) NOT NULL,
  `track_position` varchar(50) DEFAULT NULL COMMENT '内中外',
  `starting_order` int DEFAULT NULL COMMENT '番手',
  `detail` text COMMENT '内容',
  `intensity` varchar(50) DEFAULT NULL COMMENT '強度',
  `margin` varchar(50) DEFAULT NULL COMMENT '着差',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_partner_id`),
  UNIQUE KEY `training_partner_internal_id` (`training_partner_internal_id`),
  KEY `horse_daily_records_training_partners_training_record_uuid_fk` (`training_record_uuid`),
  KEY `horse_daily_records_training_partners_horse_id_fk` (`horse_id`),
  CONSTRAINT `horse_daily_records_training_partners_horse_id_fk` FOREIGN KEY (`horse_id`) REFERENCES `horses` (`horse_id`),
  CONSTRAINT `horse_daily_records_training_partners_training_record_uuid_fk` FOREIGN KEY (`training_record_uuid`) REFERENCES `horse_daily_records_trainings` (`training_record_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `horse_daily_records_trainings` (
  `training_record_uuid` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `training_record_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `horse_daily_record_id` binary(16) NOT NULL,
  `is_gait_abnormal` tinyint(1) DEFAULT NULL,
  `gait_abnormal_description` text,
  `training_type` varchar(255) DEFAULT NULL,
  `rider_uuid` binary(16) DEFAULT NULL,
  `training_menu_uuid` binary(16) DEFAULT NULL,
  `gate_training_type` varchar(50) DEFAULT NULL,
  `facility_id` varchar(50) DEFAULT NULL,
  `facility_name` varchar(50) DEFAULT NULL,
  `course_id` varchar(50) DEFAULT NULL,
  `course_going` varchar(50) DEFAULT NULL,
  `furlong_time` varchar(50) DEFAULT NULL,
  `furlong_time_position` varchar(50) DEFAULT NULL,
  `pool_training_type` varchar(50) DEFAULT NULL,
  `lactate_level` float DEFAULT NULL,
  `training_comment` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_record_uuid`),
  UNIQUE KEY `idx_training_record_internal_id` (`training_record_internal_id`),
  KEY `horse_daily_records_trainings_horse_daily_record_id_fk` (`horse_daily_record_id`),
  KEY `horse_daily_records_trainings_training_menu_uuid_fk` (`training_menu_uuid`),
  KEY `horse_daily_records_trainings_facility_id_fk` (`facility_id`),
  KEY `horse_daily_records_trainings_course_id_fk` (`course_id`),
  KEY `horse_daily_records_trainings_rider_uuid_fk` (`rider_uuid`),
  CONSTRAINT `horse_daily_records_trainings_course_id_fk` FOREIGN KEY (`course_id`) REFERENCES `training_courses` (`course_id`),
  CONSTRAINT `horse_daily_records_trainings_facility_id_fk` FOREIGN KEY (`facility_id`) REFERENCES `facilities` (`facility_id`),
  CONSTRAINT `horse_daily_records_trainings_horse_daily_record_id_fk` FOREIGN KEY (`horse_daily_record_id`) REFERENCES `horse_daily_records` (`horse_daily_record_id`),
  CONSTRAINT `horse_daily_records_trainings_rider_uuid_fk` FOREIGN KEY (`rider_uuid`) REFERENCES `staffs` (`staff_uuid`),
  CONSTRAINT `horse_daily_records_trainings_training_menu_uuid_fk` FOREIGN KEY (`training_menu_uuid`) REFERENCES `training_menus` (`training_menu_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `horse_handover_notes` (
  `horse_handover_note_id` binary(16) NOT NULL,
  `horse_handover_note_internal_id` bigint NOT NULL AUTO_INCREMENT,
  `horse_id` bigint unsigned NOT NULL,
  `handover_note` text,
  `next_race_equipment_note` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `fodder_note` text,
  PRIMARY KEY (`horse_handover_note_id`),
  UNIQUE KEY `idx_horse_handover_notes_horse_handover_note_internal_id` (`horse_handover_note_internal_id`),
  UNIQUE KEY `idx_horse_handover_notes_horse_id` (`horse_id`),
  KEY `horse_handover_notes_horse_id_fk` (`horse_id`),
  CONSTRAINT `horse_handover_notes_horse_id_fk` FOREIGN KEY (`horse_id`) REFERENCES `horses` (`horse_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `horse_note_user_devices` (
  `device_id` varchar(50) NOT NULL,
  `os_name` varchar(255) DEFAULT NULL,
  `os_version` varchar(255) DEFAULT NULL,
  `model_name` varchar(255) DEFAULT NULL,
  `app_runtime_version` varchar(255) DEFAULT NULL,
  `app_update_created_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `user_uuid` binary(16) DEFAULT NULL,
  PRIMARY KEY (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `horse_stable_history` (
  `horse_stable_history_uuid` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `horse_id` bigint unsigned NOT NULL,
  `stable_uuid` binary(16) NOT NULL,
  `in_stable` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_stable_history_uuid`),
  KEY `horse_id_fk_horse_stable_history` (`horse_id`),
  KEY `stable_uuid_fk_horse_stable_history` (`stable_uuid`),
  CONSTRAINT `horse_id_fk_horse_stable_history` FOREIGN KEY (`horse_id`) REFERENCES `horses` (`horse_id`),
  CONSTRAINT `stable_uuid_fk_horse_stable_history` FOREIGN KEY (`stable_uuid`) REFERENCES `stables` (`stable_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `horse_statuses` (
  `horse_status_id` binary(16) NOT NULL,
  `horse_status_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `horse_id` bigint unsigned NOT NULL,
  `in_stable` tinyint(1) NOT NULL DEFAULT '1',
  `outside_farm_id` binary(16) DEFAULT NULL,
  `latest_stable_in_date` date DEFAULT NULL,
  `latest_stable_out_date` date DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_status_id`),
  UNIQUE KEY `horse_status_internal_id` (`horse_status_internal_id`),
  UNIQUE KEY `unique_horse_id` (`horse_id`),
  KEY `idx_horse_id` (`horse_id`),
  KEY `idx_outside_farm_id` (`outside_farm_id`),
  CONSTRAINT `horse_statuses_horse_id_fk` FOREIGN KEY (`horse_id`) REFERENCES `horses` (`horse_id`),
  CONSTRAINT `horse_statuses_outside_farm_id_fk` FOREIGN KEY (`outside_farm_id`) REFERENCES `stable_tm_outside_farm` (`outside_farm_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `horse_weight_history` (
  `horse_weight_uuid` binary(16) NOT NULL,
  `horse_id` bigint unsigned NOT NULL,
  `weight` float NOT NULL,
  `date` date NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_weight_uuid`),
  UNIQUE KEY `date_index_horse_weights` (`horse_id`,`date`),
  CONSTRAINT `horse_id_fk_horse_weights` FOREIGN KEY (`horse_id`) REFERENCES `horses` (`horse_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `horses` (
  `horse_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_uuid` binary(16) DEFAULT NULL,
  `stable_uuid` binary(16) NOT NULL,
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_ja_0900_as_cs NOT NULL,
  `gender` enum('male','female','gelding') DEFAULT NULL,
  `birth_year` int unsigned DEFAULT NULL,
  `birth_day` date DEFAULT NULL,
  `father_id` bigint unsigned DEFAULT NULL,
  `mother_id` bigint unsigned DEFAULT NULL,
  `rfid` bigint DEFAULT NULL,
  `profile_pic_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `master_horse_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `manage_status` varchar(50) NOT NULL DEFAULT 'managed' COMMENT 'managed: 管理内, unmanaged: 管理外',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`horse_id`),
  UNIQUE KEY `horse_id` (`horse_id`),
  KEY `father_id_fk` (`father_id`),
  KEY `mother_id_fk` (`mother_id`),
  KEY `stable_uuid_fk_3` (`stable_uuid`),
  KEY `horses_organization_uuid_fk` (`organization_uuid`),
  KEY `horses_master_horse_id_fk` (`master_horse_id`),
  KEY `idx_horses_name` (`name`),
  KEY `idx_horses_manage_status` (`manage_status`),
  CONSTRAINT `father_id_fk` FOREIGN KEY (`father_id`) REFERENCES `horses` (`horse_id`),
  CONSTRAINT `horses_master_horse_id_fk` FOREIGN KEY (`master_horse_id`) REFERENCES `master_horses` (`master_horse_id`),
  CONSTRAINT `horses_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`),
  CONSTRAINT `mother_id_fk` FOREIGN KEY (`mother_id`) REFERENCES `horses` (`horse_id`),
  CONSTRAINT `stable_uuid_fk_3` FOREIGN KEY (`stable_uuid`) REFERENCES `stables` (`stable_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `invalid_results` (
  `training_id` bigint unsigned NOT NULL,
  `period_id` bigint unsigned NOT NULL,
  `code` enum('speed_out_of_range','pitch_out_of_range','stride_out_of_range') NOT NULL,
  `level` enum('critical','error','warning','info','debug') NOT NULL,
  `count` int unsigned NOT NULL,
  PRIMARY KEY (`training_id`,`period_id`,`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `invitations` (
  `invitation_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `invitation_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_owner_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `accepted_at` datetime DEFAULT NULL,
  `method` enum('email','link') NOT NULL,
  `invite_email` varchar(255) DEFAULT NULL,
  `email_dispatch_count` int DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`invitation_id`),
  UNIQUE KEY `invitation_internal_id` (`invitation_internal_id`),
  UNIQUE KEY `organization_owner_id` (`organization_owner_id`,`token`),
  CONSTRAINT `invitations_organization_owner_id_fk` FOREIGN KEY (`organization_owner_id`) REFERENCES `organization_owners` (`organization_owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `jockeys` (
  `jockey_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`jockey_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `master_farms` (
  `master_farm_id` binary(16) NOT NULL,
  `master_farm_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `farm_area_id` binary(16) NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`master_farm_id`),
  UNIQUE KEY `master_farm_internal_id` (`master_farm_internal_id`),
  KEY `idx_farm_area_id` (`farm_area_id`),
  CONSTRAINT `master_farms_farm_area_id_fk` FOREIGN KEY (`farm_area_id`) REFERENCES `farm_areas` (`farm_area_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `master_horse_prizes` (
  `master_horse_prize_id` int NOT NULL AUTO_INCREMENT,
  `master_horse_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `flat_race_prize` int NOT NULL DEFAULT '0',
  `jump_race_prize` int NOT NULL DEFAULT '0',
  `jra_source_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`master_horse_prize_id`),
  UNIQUE KEY `master_horse_prizes_master_horse_id_unique` (`master_horse_id`),
  CONSTRAINT `master_horse_prizes_master_horse_id_fk` FOREIGN KEY (`master_horse_id`) REFERENCES `master_horses` (`master_horse_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `master_horses` (
  `master_horse_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `horse_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_ja_0900_as_cs NOT NULL,
  `horse_name_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `mother_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `gender` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `birth_year` int NOT NULL,
  `stable_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`master_horse_id`),
  KEY `idx_horse_name` (`horse_name`),
  KEY `idx_horse_name_en` (`horse_name_en`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `minimum_versions` (
  `device_type` enum('mobile','tablet','horse_device') NOT NULL,
  `version` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`device_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `onetime_code` (
  `onetime_code_internal_id` bigint NOT NULL AUTO_INCREMENT,
  `code` varchar(50) NOT NULL,
  `session_id` varchar(50) NOT NULL,
  `expires_at` datetime NOT NULL,
  `verified_at` datetime DEFAULT NULL,
  `used_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`onetime_code_internal_id`),
  UNIQUE KEY `session_id` (`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `organization_farriers` (
  `organization_farrier_id` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `organization_farrier_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_uuid` binary(16) NOT NULL,
  `farrier_name` varchar(255) NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`organization_farrier_id`),
  UNIQUE KEY `idx_organization_farrier_internal_id` (`organization_farrier_internal_id`),
  KEY `organization_farriers_organization_uuid_fk` (`organization_uuid`),
  CONSTRAINT `organization_farriers_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `organization_owner_horse_relations` (
  `organization_owner_horse_relation_id` varchar(50) NOT NULL,
  `organization_owner_horse_relation_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_uuid` binary(16) DEFAULT NULL,
  `organization_owner_id` varchar(50) NOT NULL,
  `horse_id` bigint unsigned NOT NULL,
  `owner_horse_id` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`organization_owner_horse_relation_id`),
  UNIQUE KEY `organization_owner_horse_relation_internal_id` (`organization_owner_horse_relation_internal_id`),
  KEY `organization_owner_horse_relations_organization_uuid_fk` (`organization_uuid`),
  KEY `organization_owner_horse_relations_organization_owner_id_fk` (`organization_owner_id`),
  KEY `organization_owner_horse_relations_horse_id_fk` (`horse_id`),
  KEY `organization_owner_horse_relations_owner_horse_id_fk` (`owner_horse_id`),
  CONSTRAINT `organization_owner_horse_relations_horse_id_fk` FOREIGN KEY (`horse_id`) REFERENCES `horses` (`horse_id`),
  CONSTRAINT `organization_owner_horse_relations_organization_owner_id_fk` FOREIGN KEY (`organization_owner_id`) REFERENCES `organization_owners` (`organization_owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `organization_owners` (
  `organization_owner_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `organization_owner_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_uuid` binary(16) NOT NULL,
  `owner_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `organization_owner_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`organization_owner_id`),
  UNIQUE KEY `organization_owner_internal_id` (`organization_owner_internal_id`),
  UNIQUE KEY `unique_organization_uuid_owner_id` (`organization_uuid`,`owner_id`),
  KEY `organization_owners_owner_id_fk` (`owner_id`),
  CONSTRAINT `organization_owners_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`),
  CONSTRAINT `organization_owners_owner_id_fk` FOREIGN KEY (`owner_id`) REFERENCES `owners` (`owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `organization_veterinarians` (
  `organization_veterinarian_id` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `organization_veterinarian_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_uuid` binary(16) NOT NULL,
  `veterinarian_name` varchar(255) NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`organization_veterinarian_id`),
  UNIQUE KEY `idx_organization_veterinarian_internal_id` (`organization_veterinarian_internal_id`),
  KEY `organization_veterinarians_organization_uuid_fk` (`organization_uuid`),
  CONSTRAINT `organization_veterinarians_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `organizations` (
  `organization_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_uuid` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `association_id` varchar(50) DEFAULT NULL,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `home_facility_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`organization_uuid`),
  UNIQUE KEY `organization_id` (`organization_id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `owner_horses` (
  `owner_horse_id` varchar(50) NOT NULL,
  `owner_horse_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_ja_0900_as_cs NOT NULL,
  `owner_id` varchar(50) NOT NULL,
  `master_horse_id` varchar(50) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`owner_horse_id`),
  UNIQUE KEY `owner_horse_internal_id` (`owner_horse_internal_id`),
  UNIQUE KEY `idx_owner_id_master_horse_id` (`owner_id`,`master_horse_id`),
  KEY `owner_horses_owner_id_fk` (`owner_id`),
  KEY `owner_horses_master_horse_id_fk` (`master_horse_id`),
  CONSTRAINT `owner_horses_master_horse_id_fk` FOREIGN KEY (`master_horse_id`) REFERENCES `master_horses` (`master_horse_id`),
  CONSTRAINT `owner_horses_owner_id_fk` FOREIGN KEY (`owner_id`) REFERENCES `owners` (`owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `owners` (
  `owner_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `owner_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `owner_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `firebase_uid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `verified_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`owner_id`),
  UNIQUE KEY `owner_internal_id` (`owner_internal_id`),
  UNIQUE KEY `firebase_uid` (`firebase_uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `pending_send_reports` (
  `pending_send_report_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `report_id` varchar(50) NOT NULL,
  `horse_id` bigint unsigned NOT NULL,
  `organization_owner_id` varchar(50) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `sent_at` datetime DEFAULT NULL,
  PRIMARY KEY (`pending_send_report_internal_id`),
  KEY `pending_send_reports_report_id_fk` (`report_id`),
  KEY `pending_send_reports_horse_id_fk` (`horse_id`),
  KEY `pending_send_reports_organization_owner_id_fk` (`organization_owner_id`),
  CONSTRAINT `pending_send_reports_horse_id_fk` FOREIGN KEY (`horse_id`) REFERENCES `horses` (`horse_id`),
  CONSTRAINT `pending_send_reports_organization_owner_id_fk` FOREIGN KEY (`organization_owner_id`) REFERENCES `organization_owners` (`organization_owner_id`),
  CONSTRAINT `pending_send_reports_report_id_fk` FOREIGN KEY (`report_id`) REFERENCES `reports` (`report_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `race_horses` (
  `master_horse_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `race_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `jockey_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `starting_gate_number` int DEFAULT NULL,
  `starting_number` int DEFAULT NULL,
  `weight` int DEFAULT NULL,
  `impost` int DEFAULT NULL,
  `rank` int DEFAULT NULL,
  `is_canceled` tinyint(1) NOT NULL DEFAULT '0',
  `is_withdrawn` tinyint(1) NOT NULL DEFAULT '0',
  `length_behind_string` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `length_behind` decimal(5,2) DEFAULT NULL,
  `total_time` decimal(5,2) DEFAULT NULL,
  `three_furlong_time` decimal(5,2) DEFAULT NULL,
  `corner_position` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`master_horse_id`,`race_id`),
  KEY `race_horses_race_id_fk` (`race_id`),
  KEY `race_horses_jockey_id_fk` (`jockey_id`),
  CONSTRAINT `race_horses_jockey_id_fk` FOREIGN KEY (`jockey_id`) REFERENCES `jockeys` (`jockey_id`),
  CONSTRAINT `race_horses_master_horse_id_fk` FOREIGN KEY (`master_horse_id`) REFERENCES `master_horses` (`master_horse_id`),
  CONSTRAINT `race_horses_race_id_fk` FOREIGN KEY (`race_id`) REFERENCES `race_results` (`race_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `race_places` (
  `race_place_id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(20) DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `full_name` varchar(255) DEFAULT NULL,
  `region` varchar(255) DEFAULT NULL COMMENT 'jra: JRA, nar: 地方競馬, international: 海外競馬',
  PRIMARY KEY (`race_place_id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `race_results` (
  `race_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `race_place_id` int NOT NULL,
  `iteration_number` int DEFAULT NULL,
  `iteration_day_number` int DEFAULT NULL,
  `race_number` int NOT NULL,
  `race_year` int NOT NULL,
  `race_month` int NOT NULL,
  `race_day` int NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `age_limit` varchar(20) DEFAULT NULL,
  `class` varchar(20) DEFAULT NULL,
  `distance` int NOT NULL,
  `track_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `going` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `total_horse_number` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`race_id`),
  KEY `race_results_race_place_id_fk` (`race_place_id`),
  CONSTRAINT `race_results_race_place_id_fk` FOREIGN KEY (`race_place_id`) REFERENCES `race_places` (`race_place_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `report_generate_requests` (
  `report_generate_request_id` varchar(50) NOT NULL,
  `report_generate_request_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `horse_id` bigint unsigned NOT NULL,
  `report_id` varchar(50) DEFAULT NULL,
  `request_memo` text,
  `generated_content` text,
  `ai_generate_log_id` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_generate_request_id`),
  UNIQUE KEY `report_generate_request_internal_id` (`report_generate_request_internal_id`),
  KEY `report_generate_requests_horse_id_fk` (`horse_id`),
  KEY `report_generate_requests_report_id_fk` (`report_id`),
  CONSTRAINT `report_generate_requests_horse_id_fk` FOREIGN KEY (`horse_id`) REFERENCES `horses` (`horse_id`),
  CONSTRAINT `report_generate_requests_report_id_fk` FOREIGN KEY (`report_id`) REFERENCES `reports` (`report_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `report_section_horse_conditions` (
  `report_section_horse_condition_id` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `report_section_id` binary(16) NOT NULL,
  `horse_weight` int DEFAULT NULL,
  `horse_weight_measured_date` varchar(50) DEFAULT NULL,
  `is_gait_abnormal` tinyint(1) DEFAULT NULL,
  `gait_comment` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_section_horse_condition_id`),
  KEY `report_section_horse_conditions_report_section_id_fk` (`report_section_id`),
  CONSTRAINT `report_section_horse_conditions_report_section_id_fk` FOREIGN KEY (`report_section_id`) REFERENCES `report_sections` (`report_section_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `report_section_images` (
  `report_section_image_id` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `report_section_id` binary(16) NOT NULL,
  `image_path` varchar(255) DEFAULT NULL,
  `captured_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_section_image_id`),
  KEY `report_section_images_report_section_id_fk` (`report_section_id`),
  CONSTRAINT `report_section_images_report_section_id_fk` FOREIGN KEY (`report_section_id`) REFERENCES `report_sections` (`report_section_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `report_section_medical_treatment_affected_area_photos` (
  `report_section_medical_treatment_affected_area_photo_id` binary(16) NOT NULL,
  `report_section_medical_treatment_affected_area_photo_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `report_section_medical_treatment_record_id` binary(16) NOT NULL,
  `photo_path` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_section_medical_treatment_affected_area_photo_id`),
  UNIQUE KEY `idx_rsmt_affected_area_photo_internal_id` (`report_section_medical_treatment_affected_area_photo_internal_id`),
  KEY `rsmt_area_photos_report_section_medical_treatment_record_id_fk` (`report_section_medical_treatment_record_id`),
  CONSTRAINT `rsmt_area_photos_report_section_medical_treatment_record_id_fk` FOREIGN KEY (`report_section_medical_treatment_record_id`) REFERENCES `report_section_medical_treatment_records` (`report_section_medical_treatment_record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `report_section_medical_treatment_records` (
  `report_section_medical_treatment_record_id` binary(16) NOT NULL,
  `report_section_medical_treatment_record_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `report_section_medical_treatment_id` binary(16) NOT NULL,
  `year` int NOT NULL,
  `month` int NOT NULL,
  `day` int NOT NULL,
  `body` text,
  `veterinarian` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_section_medical_treatment_record_id`),
  UNIQUE KEY `idx_report_section_medical_treatment_record_internal_id` (`report_section_medical_treatment_record_internal_id`),
  KEY `rsmt_records_report_section_medical_treatment_id_fk` (`report_section_medical_treatment_id`),
  CONSTRAINT `rsmt_records_report_section_medical_treatment_id_fk` FOREIGN KEY (`report_section_medical_treatment_id`) REFERENCES `report_section_medical_treatments` (`report_section_medical_treatment_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `report_section_medical_treatments` (
  `report_section_medical_treatment_id` binary(16) NOT NULL,
  `report_section_medical_treatment_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `report_section_id` binary(16) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_section_medical_treatment_id`),
  UNIQUE KEY `idx_report_section_medical_treatment_internal_id` (`report_section_medical_treatment_internal_id`),
  KEY `rsmt_report_section_id_fk` (`report_section_id`),
  CONSTRAINT `rsmt_report_section_id_fk` FOREIGN KEY (`report_section_id`) REFERENCES `report_sections` (`report_section_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `report_section_monthly_summaries` (
  `report_section_monthly_summary_id` binary(16) NOT NULL,
  `report_section_monthly_summary_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `report_section_id` binary(16) NOT NULL,
  `start_year` int NOT NULL,
  `start_month` int NOT NULL,
  `start_day` int NOT NULL,
  `end_year` int NOT NULL,
  `end_month` int NOT NULL,
  `end_day` int NOT NULL,
  `horse_body_weight_history` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_section_monthly_summary_id`),
  UNIQUE KEY `idx_report_section_monthly_summary_internal_id` (`report_section_monthly_summary_internal_id`),
  KEY `report_section_monthly_summaries_report_section_id_fk` (`report_section_id`),
  CONSTRAINT `report_section_monthly_summaries_report_section_id_fk` FOREIGN KEY (`report_section_id`) REFERENCES `report_sections` (`report_section_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `report_section_monthly_summary_race_records` (
  `report_section_monthly_summary_race_record_id` binary(16) NOT NULL,
  `report_section_monthly_summary_race_record_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `report_section_monthly_summary_id` binary(16) NOT NULL,
  `year` int NOT NULL,
  `month` int NOT NULL,
  `day` int NOT NULL,
  `body` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_section_monthly_summary_race_record_id`),
  UNIQUE KEY `idx_report_section_monthly_summary_race_record_internal_id` (`report_section_monthly_summary_race_record_internal_id`),
  KEY `rsms_race_record_report_section_monthly_summary_id_fk` (`report_section_monthly_summary_id`),
  CONSTRAINT `rsms_race_record_report_section_monthly_summary_id_fk` FOREIGN KEY (`report_section_monthly_summary_id`) REFERENCES `report_section_monthly_summaries` (`report_section_monthly_summary_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `report_section_monthly_timeline_records` (
  `report_section_monthly_timeline_record_id` binary(16) NOT NULL,
  `report_section_monthly_timeline_record_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `report_section_monthly_timeline_id` binary(16) NOT NULL,
  `index` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `year` int NOT NULL,
  `month` int NOT NULL,
  `day` int NOT NULL,
  `body` text,
  `training_menu` text,
  `furlong_time` varchar(255) DEFAULT NULL,
  `assignee` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_section_monthly_timeline_record_id`),
  UNIQUE KEY `idx_report_section_monthly_timeline_record_internal_id` (`report_section_monthly_timeline_record_internal_id`),
  KEY `report_section_monthly_timeline_records_timeline_id_fk` (`report_section_monthly_timeline_id`),
  KEY `idx_rsmt_records_report_section_monthly_timeline_id_index` (`report_section_monthly_timeline_id`,`index`),
  CONSTRAINT `report_section_monthly_timeline_records_timeline_id_fk` FOREIGN KEY (`report_section_monthly_timeline_id`) REFERENCES `report_section_monthly_timelines` (`report_section_monthly_timeline_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `report_section_monthly_timelines` (
  `report_section_monthly_timeline_id` binary(16) NOT NULL,
  `report_section_monthly_timeline_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `report_section_id` binary(16) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_section_monthly_timeline_id`),
  UNIQUE KEY `idx_report_section_monthly_timeline_internal_id` (`report_section_monthly_timeline_internal_id`),
  KEY `report_section_monthly_timelines_report_section_id_fk` (`report_section_id`),
  CONSTRAINT `report_section_monthly_timelines_report_section_id_fk` FOREIGN KEY (`report_section_id`) REFERENCES `report_sections` (`report_section_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `report_section_plain_texts` (
  `report_section_plain_text_id` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `report_section_id` binary(16) NOT NULL,
  `body` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_section_plain_text_id`),
  KEY `report_section_plain_texts_report_section_id_fk` (`report_section_id`),
  CONSTRAINT `report_section_plain_texts_report_section_id_fk` FOREIGN KEY (`report_section_id`) REFERENCES `report_sections` (`report_section_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `report_section_workout_conditions` (
  `report_section_workout_condition_id` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `report_section_id` binary(16) NOT NULL,
  `workout_training_date` varchar(50) DEFAULT NULL,
  `rider_name` varchar(50) DEFAULT NULL,
  `running_style` varchar(50) DEFAULT NULL,
  `facility_id` varchar(50) DEFAULT NULL,
  `course_id` varchar(50) DEFAULT NULL,
  `workout_furlong_time` varchar(50) DEFAULT NULL,
  `workout_furlong_time_position` varchar(50) DEFAULT NULL,
  `partner_number` int DEFAULT NULL,
  `partner_1_name` varchar(50) DEFAULT NULL,
  `partner_2_name` varchar(50) DEFAULT NULL,
  `course_going` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_section_workout_condition_id`),
  KEY `report_section_workout_conditions_report_section_id_fk` (`report_section_id`),
  KEY `report_section_workout_conditions_facility_id_fk` (`facility_id`),
  KEY `report_section_workout_conditions_course_id_fk` (`course_id`),
  CONSTRAINT `report_section_workout_conditions_course_id_fk` FOREIGN KEY (`course_id`) REFERENCES `training_courses` (`course_id`),
  CONSTRAINT `report_section_workout_conditions_facility_id_fk` FOREIGN KEY (`facility_id`) REFERENCES `facilities` (`facility_id`),
  CONSTRAINT `report_section_workout_conditions_report_section_id_fk` FOREIGN KEY (`report_section_id`) REFERENCES `report_sections` (`report_section_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `report_sections` (
  `report_section_id` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `report_section_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `report_id` varchar(50) NOT NULL,
  `type` varchar(50) NOT NULL,
  `template_inner_id` varchar(50) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_section_id`),
  UNIQUE KEY `report_section_internal_id` (`report_section_internal_id`),
  UNIQUE KEY `report_id_template_inner_id` (`report_id`,`template_inner_id`),
  CONSTRAINT `report_sections_report_id_fk` FOREIGN KEY (`report_id`) REFERENCES `reports` (`report_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `reports` (
  `report_id` varchar(50) NOT NULL,
  `report_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_uuid` binary(16) NOT NULL,
  `horse_id` bigint unsigned NOT NULL,
  `title` varchar(255) NOT NULL,
  `template_id` varchar(255) NOT NULL DEFAULT 'workout_report',
  `is_draft` tinyint(1) NOT NULL DEFAULT '1',
  `printed` tinyint(1) NOT NULL DEFAULT '0',
  `first_sent_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`report_id`),
  UNIQUE KEY `report_internal_id` (`report_internal_id`),
  KEY `reports_organization_uuid_fk` (`organization_uuid`),
  KEY `reports_horse_id_fk` (`horse_id`),
  CONSTRAINT `reports_horse_id_fk` FOREIGN KEY (`horse_id`) REFERENCES `horses` (`horse_id`),
  CONSTRAINT `reports_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `schema_migrations` (
  `version` bigint NOT NULL,
  `dirty` tinyint(1) NOT NULL,
  PRIMARY KEY (`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `sent_reports` (
  `sent_report_id` varchar(50) NOT NULL,
  `sent_report_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `report_id` varchar(50) NOT NULL,
  `organization_owner_id` varchar(50) NOT NULL,
  `owner_horse_id` varchar(50) NOT NULL,
  `sent_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `first_read_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`sent_report_id`),
  UNIQUE KEY `sent_report_internal_id` (`sent_report_internal_id`),
  KEY `sent_reports_report_id_fk` (`report_id`),
  KEY `sent_reports_organization_owner_id_fk` (`organization_owner_id`),
  KEY `sent_reports_owner_horse_id_fk` (`owner_horse_id`),
  CONSTRAINT `sent_reports_organization_owner_id_fk` FOREIGN KEY (`organization_owner_id`) REFERENCES `organization_owners` (`organization_owner_id`),
  CONSTRAINT `sent_reports_owner_horse_id_fk` FOREIGN KEY (`owner_horse_id`) REFERENCES `owner_horses` (`owner_horse_id`),
  CONSTRAINT `sent_reports_report_id_fk` FOREIGN KEY (`report_id`) REFERENCES `reports` (`report_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `share_reports` (
  `share_report_id` varchar(50) NOT NULL,
  `share_report_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `report_id` varchar(50) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`share_report_id`),
  UNIQUE KEY `share_report_internal_id` (`share_report_internal_id`),
  KEY `share_reports_report_id_fk` (`report_id`),
  CONSTRAINT `share_reports_report_id_fk` FOREIGN KEY (`report_id`) REFERENCES `reports` (`report_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `stable_status` (
  `stable_status_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `stable_uuid` binary(16) NOT NULL,
  `stall_num` int NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`stable_status_id`),
  UNIQUE KEY `idx_stable_uuid` (`stable_uuid`),
  CONSTRAINT `stable_status_stable_uuid_fk` FOREIGN KEY (`stable_uuid`) REFERENCES `stables` (`stable_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `stable_tm_fixed_slots` (
  `fixed_slot_id` binary(16) NOT NULL,
  `fixed_slot_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `section_id` binary(16) NOT NULL,
  `number_of_section` int NOT NULL,
  `slot_num` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`fixed_slot_id`),
  UNIQUE KEY `fixed_slot_internal_id` (`fixed_slot_internal_id`),
  KEY `idx_section_id` (`section_id`),
  CONSTRAINT `fixed_slots_section_id_fk` FOREIGN KEY (`section_id`) REFERENCES `stable_tm_sections` (`section_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `stable_tm_outside_farm` (
  `outside_farm_id` binary(16) NOT NULL,
  `outside_farm_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_uuid` binary(16) NOT NULL,
  `farm_area_id` binary(16) NOT NULL,
  `name` varchar(255) NOT NULL,
  `master_farm_id` binary(16) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`outside_farm_id`),
  UNIQUE KEY `outside_farm_internal_id` (`outside_farm_internal_id`),
  KEY `idx_organization_uuid` (`organization_uuid`),
  KEY `idx_farm_area_id` (`farm_area_id`),
  KEY `idx_master_farm_id` (`master_farm_id`),
  CONSTRAINT `stable_tm_outside_farm_farm_area_id_fk` FOREIGN KEY (`farm_area_id`) REFERENCES `farm_areas` (`farm_area_id`),
  CONSTRAINT `stable_tm_outside_farm_master_farm_id_fk` FOREIGN KEY (`master_farm_id`) REFERENCES `master_farms` (`master_farm_id`),
  CONSTRAINT `stable_tm_outside_farm_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `stable_tm_sections` (
  `section_id` binary(16) NOT NULL,
  `section_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `stable_uuid` binary(16) NOT NULL,
  `start_date` datetime NOT NULL,
  `end_date` datetime NOT NULL,
  `status` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`section_id`),
  UNIQUE KEY `section_internal_id` (`section_internal_id`),
  KEY `idx_stable_uuid` (`stable_uuid`),
  CONSTRAINT `stable_tm_section_stable_uuid_fk` FOREIGN KEY (`stable_uuid`) REFERENCES `stables` (`stable_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `stable_tm_transport_daily_records` (
  `transport_daily_record_id` binary(16) NOT NULL,
  `transport_daily_record_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `section_id` binary(16) NOT NULL,
  `stable_uuid` binary(16) NOT NULL,
  `year` int NOT NULL,
  `month` int NOT NULL,
  `day` int NOT NULL,
  `is_confirmed` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`transport_daily_record_id`),
  UNIQUE KEY `transport_daily_record_internal_id` (`transport_daily_record_internal_id`),
  UNIQUE KEY `unique_section_id_year_month_day` (`section_id`,`year`,`month`,`day`),
  KEY `idx_section_id` (`section_id`),
  KEY `idx_stable_uuid` (`stable_uuid`),
  KEY `idx_year_month_day` (`year`,`month`,`day`),
  CONSTRAINT `transport_daily_records_section_id_fk` FOREIGN KEY (`section_id`) REFERENCES `stable_tm_sections` (`section_id`),
  CONSTRAINT `transport_daily_records_stable_uuid_fk` FOREIGN KEY (`stable_uuid`) REFERENCES `stables` (`stable_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `stable_tm_transport_in_statuses` (
  `transport_in_status_id` binary(16) NOT NULL,
  `transport_in_status_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `transport_record_id` binary(16) NOT NULL,
  `staff_uuid` binary(16) DEFAULT NULL,
  `is_horse_van_arranged` tinyint(1) DEFAULT '0',
  `is_quarantine_applied` tinyint(1) DEFAULT '0',
  `is_owner_contacted` tinyint(1) DEFAULT '0' COMMENT '馬主連絡が完了したかどうか',
  `is_farm_contacted` tinyint(1) DEFAULT '0' COMMENT '牧場連絡が完了したかどうか',
  `next_race` text,
  `comment` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`transport_in_status_id`),
  UNIQUE KEY `idx_transport_record_id` (`transport_record_id`),
  UNIQUE KEY `idx_transport_in_status_internal_id` (`transport_in_status_internal_id`),
  KEY `idx_staff_uuid` (`staff_uuid`),
  CONSTRAINT `transport_in_statuses_staff_uuid_fk` FOREIGN KEY (`staff_uuid`) REFERENCES `staffs` (`staff_uuid`),
  CONSTRAINT `transport_in_statuses_transport_record_id_fk` FOREIGN KEY (`transport_record_id`) REFERENCES `stable_tm_transport_records` (`transport_record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `stable_tm_transport_out_handover_notes` (
  `transport_out_handover_note_id` binary(16) NOT NULL,
  `transport_out_handover_note_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `transport_out_status_id` binary(16) NOT NULL,
  `body` text,
  `latest_horse_shoeing_date` date DEFAULT NULL,
  `latest_horse_shoeing_farrier` varchar(255) DEFAULT NULL,
  `latest_horse_shoeing_body` varchar(255) DEFAULT NULL,
  `latest_horse_body_weight_date` date DEFAULT NULL,
  `latest_horse_body_weight` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`transport_out_handover_note_id`),
  UNIQUE KEY `transport_out_handover_note_internal_id` (`transport_out_handover_note_internal_id`),
  KEY `idx_transport_out_status_id` (`transport_out_status_id`),
  CONSTRAINT `transport_handover_notes_transport_out_status_id_fk` FOREIGN KEY (`transport_out_status_id`) REFERENCES `stable_tm_transport_out_statuses` (`transport_out_status_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `stable_tm_transport_out_statuses` (
  `transport_out_status_id` binary(16) NOT NULL,
  `transport_out_status_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `transport_record_id` binary(16) NOT NULL,
  `staff_uuid` binary(16) DEFAULT NULL,
  `is_stable_out_procedure_completed` tinyint(1) DEFAULT '0' COMMENT '退厩手続きが完了したかどうか',
  `is_horse_van_arranged` tinyint(1) DEFAULT '0' COMMENT '馬運車手配したかどうか',
  `is_owner_contacted` tinyint(1) DEFAULT '0' COMMENT '馬主連絡が完了したかどうか',
  `is_farm_contacted` tinyint(1) DEFAULT '0' COMMENT '牧場連絡が完了したかどうか',
  `farm_id` binary(16) DEFAULT NULL,
  `comment` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`transport_out_status_id`),
  UNIQUE KEY `idx_transport_record_id` (`transport_record_id`),
  UNIQUE KEY `idx_transport_out_status_internal_id` (`transport_out_status_internal_id`),
  KEY `idx_staff_uuid` (`staff_uuid`),
  KEY `idx_farm_id` (`farm_id`),
  CONSTRAINT `transport_out_statuses_farm_id_fk` FOREIGN KEY (`farm_id`) REFERENCES `stable_tm_outside_farm` (`outside_farm_id`),
  CONSTRAINT `transport_out_statuses_staff_uuid_fk` FOREIGN KEY (`staff_uuid`) REFERENCES `staffs` (`staff_uuid`),
  CONSTRAINT `transport_out_statuses_transport_record_id_fk` FOREIGN KEY (`transport_record_id`) REFERENCES `stable_tm_transport_records` (`transport_record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `stable_tm_transport_queue_tickets` (
  `transport_queue_ticket_id` binary(16) NOT NULL,
  `transport_queue_ticket_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `section_id` binary(16) NOT NULL,
  `ticket_key` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`transport_queue_ticket_id`),
  UNIQUE KEY `transport_queue_ticket_internal_id` (`transport_queue_ticket_internal_id`),
  KEY `idx_section_id` (`section_id`),
  CONSTRAINT `transport_queue_tickets_section_id_fk` FOREIGN KEY (`section_id`) REFERENCES `stable_tm_sections` (`section_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `stable_tm_transport_records` (
  `transport_record_id` binary(16) NOT NULL,
  `transport_record_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `transport_daily_record_id` binary(16) DEFAULT NULL,
  `type` varchar(255) NOT NULL COMMENT 'in / out / expedition_outbound / expedition_inbound / farm_transfer',
  `horse_id` bigint unsigned NOT NULL,
  `index` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`transport_record_id`),
  UNIQUE KEY `transport_record_internal_id` (`transport_record_internal_id`),
  KEY `idx_transport_daily_record_id` (`transport_daily_record_id`),
  KEY `idx_horse_id` (`horse_id`),
  KEY `idx_transport_daily_record_id_index` (`transport_daily_record_id`,`index`),
  CONSTRAINT `transport_records_horse_id_fk` FOREIGN KEY (`horse_id`) REFERENCES `horses` (`horse_id`),
  CONSTRAINT `transport_records_transport_daily_record_id_fk` FOREIGN KEY (`transport_daily_record_id`) REFERENCES `stable_tm_transport_daily_records` (`transport_daily_record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `stables` (
  `stable_uuid` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `organization_uuid` binary(16) NOT NULL,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`stable_uuid`),
  KEY `stables_organization_uuid_fk` (`organization_uuid`),
  CONSTRAINT `stables_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `staffs` (
  `staff_uuid` binary(16) NOT NULL,
  `stable_uuid` binary(16) DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `user_uuid` binary(16) DEFAULT NULL,
  `organization_uuid` binary(16) DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`staff_uuid`),
  KEY `stable_uuid_fk_5` (`stable_uuid`),
  KEY `staffs_user_uuid_fk` (`user_uuid`),
  KEY `staffs_organization_uuid_fk` (`organization_uuid`),
  CONSTRAINT `staffs_organization_uuid_fk` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`),
  CONSTRAINT `staffs_user_uuid_fk` FOREIGN KEY (`user_uuid`) REFERENCES `users` (`user_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `time_series_heart_beat_results` (
  `time_series_heart_beat_result_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `training_id` bigint unsigned NOT NULL,
  `time` int NOT NULL,
  `heart_rate` int DEFAULT NULL,
  `sympathetic_nerve` float DEFAULT NULL,
  `parasympathetic_nerve` float DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`time_series_heart_beat_result_id`),
  UNIQUE KEY `training_id` (`training_id`,`time`),
  CONSTRAINT `time_series_heart_beat_results_training_id_fk` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `time_series_locations` (
  `time_series_location_id` int NOT NULL AUTO_INCREMENT,
  `time_series_location_uuid` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `training_id` bigint unsigned NOT NULL,
  `time` int NOT NULL,
  `distance` int NOT NULL,
  `latitude` double(8,6) NOT NULL,
  `longitude` double(9,6) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`time_series_location_id`),
  UNIQUE KEY `training_id` (`training_id`,`time`),
  CONSTRAINT `training_id_fk_11` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `time_series_results` (
  `time_series_result_id` int NOT NULL AUTO_INCREMENT,
  `time_series_result_uuid` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `training_id` bigint unsigned NOT NULL,
  `time` int NOT NULL,
  `gait` enum('stable','walk','trot','canter','gallop') DEFAULT NULL,
  `lead_leg` enum('right','left') DEFAULT NULL,
  `speed` float DEFAULT NULL,
  `pitch` float DEFAULT NULL,
  `stride` float DEFAULT NULL,
  `heart_rate` int DEFAULT NULL,
  `acceleration` float NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`time_series_result_id`),
  UNIQUE KEY `training_id` (`training_id`,`time`),
  CONSTRAINT `training_id_fk_10` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `track_condition_history` (
  `track_condition_history_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `stable_uuid` binary(16) NOT NULL,
  `facility_id` bigint unsigned NOT NULL,
  `track_condition` enum('firm','good','yielding','soft') DEFAULT NULL,
  `date` date NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `hour` int NOT NULL,
  PRIMARY KEY (`track_condition_history_id`),
  UNIQUE KEY `training_date_index_track_condition_history` (`stable_uuid`,`facility_id`,`date` DESC),
  CONSTRAINT `stable_uuid_id_fk_track_condition_history` FOREIGN KEY (`stable_uuid`) REFERENCES `stables` (`stable_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `trainers_user_settings` (
  `trainers_user_settings_id` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `user_uuid` binary(16) NOT NULL,
  `report_unread_filter_truncate_enabled` tinyint(1) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`trainers_user_settings_id`),
  KEY `trainers_user_settings_user_uuid_fk` (`user_uuid`),
  CONSTRAINT `trainers_user_settings_user_uuid_fk` FOREIGN KEY (`user_uuid`) REFERENCES `users` (`user_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `training` (
  `training_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `horse_id` bigint unsigned NOT NULL,
  `trainer_uuid` binary(16) DEFAULT NULL,
  `trainer_id` bigint unsigned DEFAULT NULL,
  `stable_uuid` binary(16) NOT NULL,
  `start_at` datetime DEFAULT NULL,
  `end_at` datetime DEFAULT NULL,
  `analysis_started_at` datetime DEFAULT NULL,
  `analysis_ended_at` datetime DEFAULT NULL,
  `location_data_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `location_data_ext` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `gyro_and_acc_data_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `gyro_and_acc_data_ext` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_id`),
  UNIQUE KEY `training_id` (`training_id`),
  KEY `stable_uuid_fk_4` (`stable_uuid`),
  KEY `trainer_uuid_fk` (`trainer_uuid`),
  KEY `idx_horse_id_start_at` (`horse_id`,`start_at`),
  CONSTRAINT `horse_id_fk` FOREIGN KEY (`horse_id`) REFERENCES `horses` (`horse_id`),
  CONSTRAINT `stable_uuid_fk_4` FOREIGN KEY (`stable_uuid`) REFERENCES `stables` (`stable_uuid`),
  CONSTRAINT `trainer_uuid_fk` FOREIGN KEY (`trainer_uuid`) REFERENCES `users` (`user_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `training_ability_scores` (
  `training_ability_score_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `training_id` bigint unsigned NOT NULL,
  `sympathetic_nerve_last_60sec_sum` float DEFAULT NULL,
  `parasympathetic_nerve_max_diff` float DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_ability_score_id`),
  KEY `training_ability_scores_training_id_fk` (`training_id`),
  CONSTRAINT `training_ability_scores_training_id_fk` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `training_comments` (
  `training_comment_uuid` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `training_id` bigint unsigned NOT NULL,
  `user_uuid` binary(16) NOT NULL,
  `comment` text NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_comment_uuid`),
  KEY `training_comments_training_id_fk` (`training_id`),
  KEY `training_comments_user_uuid_fk` (`user_uuid`),
  CONSTRAINT `training_comments_training_id_fk` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`),
  CONSTRAINT `training_comments_user_uuid_fk` FOREIGN KEY (`user_uuid`) REFERENCES `users` (`user_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `training_conditions` (
  `training_condition_uuid` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `training_id` bigint unsigned NOT NULL,
  `going` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `rider_uuid` binary(16) DEFAULT NULL,
  `handler_uuid` binary(16) DEFAULT NULL,
  `is_workout` tinyint(1) NOT NULL DEFAULT '0',
  `app_ver` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_condition_uuid`),
  KEY `training_conditions_training_id_fk` (`training_id`),
  KEY `training_conditions_rider_uuid_fk` (`handler_uuid`),
  KEY `training_conditions_handler_uuid_fk` (`rider_uuid`),
  CONSTRAINT `training_conditions_handler_uuid_fk` FOREIGN KEY (`rider_uuid`) REFERENCES `staffs` (`staff_uuid`),
  CONSTRAINT `training_conditions_rider_uuid_fk` FOREIGN KEY (`handler_uuid`) REFERENCES `staffs` (`staff_uuid`),
  CONSTRAINT `training_conditions_training_id_fk` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `training_course_horse_ability_scores` (
  `training_course_horse_ability_score_uuid` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `master_horse_id` varchar(50) NOT NULL,
  `course_id` varchar(50) NOT NULL,
  `pitch_score` float DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_course_horse_ability_score_uuid`),
  UNIQUE KEY `unique_horse_course` (`master_horse_id`,`course_id`),
  KEY `training_course_horse_ability_scores_master_horse_id_fk` (`master_horse_id`),
  KEY `training_course_horse_ability_scores_course_id_idx` (`course_id`),
  CONSTRAINT `training_course_horse_ability_scores_master_horse_id_fk` FOREIGN KEY (`master_horse_id`) REFERENCES `master_horses` (`master_horse_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `training_course_masters` (
  `training_course_master_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `training_course_master_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `course_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `facility_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `facility_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `temp_training_facility_master_uuid` binary(16) DEFAULT NULL,
  PRIMARY KEY (`training_course_master_id`),
  UNIQUE KEY `training_course_master_internal_id` (`training_course_master_internal_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `training_course_pitch_averages` (
  `training_course_pitch_average_uuid` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `course_id` varchar(50) NOT NULL,
  `speed` int unsigned NOT NULL,
  `average_pitch` float DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_course_pitch_average_uuid`),
  UNIQUE KEY `unique_course_speed` (`course_id`,`speed`),
  KEY `training_course_pitch_averages_course_id_idx` (`course_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `training_courses` (
  `course_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `course_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `training_course_master_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `course_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `geometry` geometry DEFAULT NULL,
  `distance` float DEFAULT NULL,
  `last_n_furlong` tinyint(1) DEFAULT '2' COMMENT '基準として利用する上がりNハロン',
  `opened_at` datetime DEFAULT NULL,
  `closed_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `temp_facility_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`course_id`),
  UNIQUE KEY `course_internal_id` (`course_internal_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `training_data_upload_log` (
  `training_data_upload_log_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `training_id` bigint unsigned NOT NULL,
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `logged_at` timestamp NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_data_upload_log_id`),
  KEY `training_data_upload_log_training_id_fk` (`training_id`),
  CONSTRAINT `training_data_upload_log_training_id_fk` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `training_facilities` (
  `facility_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `training_facility_master_uuid` binary(16) DEFAULT NULL,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `facility_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `geometry` geometry NOT NULL,
  `distance` float DEFAULT NULL,
  `last_n_furlong` tinyint(1) DEFAULT '2' COMMENT '基準として利用する上がりNハロン',
  `has_track_condition` tinyint(1) NOT NULL DEFAULT '1',
  `opened_at` datetime DEFAULT NULL,
  `closed_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`facility_id`),
  UNIQUE KEY `facility_id` (`facility_id`),
  KEY `training_facilities_training_facility_master_uuid_fk` (`training_facility_master_uuid`),
  CONSTRAINT `training_facilities_training_facility_master_uuid_fk` FOREIGN KEY (`training_facility_master_uuid`) REFERENCES `training_facility_masters` (`training_facility_master_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `training_facility_masters` (
  `training_facility_master_uuid` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `facility_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_facility_master_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `training_indicator_labels` (
  `training_indicator_label_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `training_indicator_id` bigint unsigned NOT NULL,
  `label` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `time` int NOT NULL,
  `distance` int NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_indicator_label_id`),
  KEY `training_indicator_labels_training_indicator_id_fk` (`training_indicator_id`),
  CONSTRAINT `training_indicator_labels_training_indicator_id_fk` FOREIGN KEY (`training_indicator_id`) REFERENCES `training_indicators` (`training_indicator_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `training_indicators` (
  `training_indicator_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `training_id` bigint unsigned NOT NULL,
  `period_group_id` int NOT NULL,
  `facility_id` bigint unsigned DEFAULT NULL,
  `course_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `max_heart_rate` int DEFAULT NULL,
  `max_heart_rate_in_all` int DEFAULT NULL,
  `max_heart_rate_in_lap` int DEFAULT NULL,
  `thr100` int DEFAULT NULL,
  `v200` float DEFAULT NULL,
  `one_minute_heart_rate` int DEFAULT NULL,
  `three_minutes_min_heart_rate` int DEFAULT NULL,
  `heart_rate_gap` int DEFAULT NULL,
  `thirty_seconds_after_goal_heart_rate` int DEFAULT NULL,
  `one_minute_after_goal_heart_rate` int DEFAULT NULL,
  `two_minutes_after_goal_heart_rate` int DEFAULT NULL,
  `two_minutes_after_goal_min_heart_rate` int DEFAULT NULL,
  `three_minutes_after_goal_min_heart_rate` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_indicator_id`),
  KEY `training_indicators_training_id_fk` (`training_id`),
  CONSTRAINT `training_indicators_training_id_fk` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `training_menus` (
  `training_menu_uuid` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `training_menu_internal_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `stable_uuid` binary(16) NOT NULL,
  `training_menu_name` text NOT NULL,
  `training_type` varchar(255) DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_menu_uuid`),
  UNIQUE KEY `idx_training_menu_internal_id` (`training_menu_internal_id`),
  KEY `idx_stable_uuid_training_menu_name` (`stable_uuid`,`training_menu_name`(100)),
  CONSTRAINT `training_menus_stable_uuid_fk` FOREIGN KEY (`stable_uuid`) REFERENCES `stables` (`stable_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `training_modes` (
  `training_mode_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `training_id` bigint unsigned NOT NULL,
  `with_heart_rate` tinyint(1) NOT NULL DEFAULT '0',
  `with_head` tinyint(1) NOT NULL DEFAULT '0',
  `with_front_leg` tinyint(1) NOT NULL DEFAULT '0',
  `with_hind_leg` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_mode_id`),
  UNIQUE KEY `training_id` (`training_id`),
  CONSTRAINT `training_modes_training_id_fk` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `training_period_max_speed_stats` (
  `training_period_max_speed_stats_uuid` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `training_period_uuid` binary(16) NOT NULL,
  `training_id` bigint unsigned NOT NULL,
  `max_speed` float DEFAULT NULL,
  `max_speed_time` float DEFAULT NULL,
  `max_speed_stride` float DEFAULT NULL,
  `max_speed_pitch` float DEFAULT NULL,
  `left_max_speed` float DEFAULT NULL,
  `left_max_speed_time` float DEFAULT NULL,
  `left_max_speed_stride` float DEFAULT NULL,
  `left_max_speed_pitch` float DEFAULT NULL,
  `right_max_speed` float DEFAULT NULL,
  `right_max_speed_time` float DEFAULT NULL,
  `right_max_speed_stride` float DEFAULT NULL,
  `right_max_speed_pitch` float DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_period_max_speed_stats_uuid`),
  KEY `training_period_max_speed_stats_training_period_uuid_fk` (`training_period_uuid`),
  KEY `training_period_max_speed_stats_training_id_fk` (`training_id`),
  CONSTRAINT `training_period_max_speed_stats_training_id_fk` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`),
  CONSTRAINT `training_period_max_speed_stats_training_period_uuid_fk` FOREIGN KEY (`training_period_uuid`) REFERENCES `training_periods` (`training_period_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `training_period_max_stats` (
  `training_period_max_stats_uuid` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `training_period_uuid` binary(16) NOT NULL,
  `training_id` bigint unsigned NOT NULL,
  `max_speed` float DEFAULT NULL,
  `max_speed_time` float DEFAULT NULL,
  `max_stride` float DEFAULT NULL,
  `max_stride_time` float DEFAULT NULL,
  `max_pitch` float DEFAULT NULL,
  `max_pitch_time` float DEFAULT NULL,
  `left_max_speed` float DEFAULT NULL,
  `left_max_speed_time` float DEFAULT NULL,
  `left_max_stride` float DEFAULT NULL,
  `left_max_stride_time` float DEFAULT NULL,
  `left_max_pitch` float DEFAULT NULL,
  `left_max_pitch_time` float DEFAULT NULL,
  `right_max_speed` float DEFAULT NULL,
  `right_max_speed_time` float DEFAULT NULL,
  `right_max_stride` float DEFAULT NULL,
  `right_max_stride_time` float DEFAULT NULL,
  `right_max_pitch` float DEFAULT NULL,
  `right_max_pitch_time` float DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_period_max_stats_uuid`),
  KEY `training_period_max_stats_training_period_uuid_fk` (`training_period_uuid`),
  KEY `training_period_max_stats_training_id_fk` (`training_id`),
  CONSTRAINT `training_period_max_stats_training_id_fk` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`),
  CONSTRAINT `training_period_max_stats_training_period_uuid_fk` FOREIGN KEY (`training_period_uuid`) REFERENCES `training_periods` (`training_period_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `training_periods` (
  `training_period_uuid` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `training_id` bigint unsigned NOT NULL,
  `period_group_id` int unsigned NOT NULL,
  `period_type` enum('all','all_in_facility','one_lap') NOT NULL,
  `facility_id` bigint unsigned DEFAULT NULL,
  `course_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `direction` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `start_at` datetime DEFAULT NULL,
  `start_time` float unsigned NOT NULL,
  `end_time` float unsigned NOT NULL,
  `start_distance` float unsigned NOT NULL,
  `end_distance` float unsigned NOT NULL,
  `lap_count` int unsigned DEFAULT NULL,
  `is_main` tinyint(1) DEFAULT '0',
  `left_leg_ratio` float DEFAULT NULL,
  `total_distance` float DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_period_uuid`),
  UNIQUE KEY `training_periods_training_period_group_lap_unique` (`training_id`,`period_group_id`,`lap_count`),
  CONSTRAINT `training_id_fk_8` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `training_results` (
  `training_id` bigint unsigned NOT NULL,
  `period_id` bigint unsigned NOT NULL,
  `period_group_id` int unsigned NOT NULL,
  `period_type` enum('all','all_in_facility','one_lap') NOT NULL,
  `lap_count` int unsigned DEFAULT NULL,
  `facility_id` bigint unsigned DEFAULT NULL,
  `course_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `start_at` timestamp NULL DEFAULT NULL,
  `end_at` timestamp NULL DEFAULT NULL,
  `max_speed` float DEFAULT NULL,
  `max_pitch` float DEFAULT NULL,
  `max_stride` float DEFAULT NULL,
  `average_speed` float DEFAULT NULL,
  `average_pitch` float DEFAULT NULL,
  `average_stride` float DEFAULT NULL,
  `total_distance` float DEFAULT NULL,
  `left_leg_ratio` float DEFAULT NULL,
  `head_motion_forward_average` float DEFAULT NULL,
  `head_motion_vertical_average` float DEFAULT NULL,
  `head_motion_side_average` float DEFAULT NULL,
  `head_motion_direction_average` float DEFAULT NULL,
  `left_max_speed` float DEFAULT NULL,
  `left_max_pitch` float DEFAULT NULL,
  `left_max_stride` float DEFAULT NULL,
  `left_average_speed` float DEFAULT NULL,
  `left_average_pitch` float DEFAULT NULL,
  `left_average_stride` float DEFAULT NULL,
  `left_head_motion_forward_average` float DEFAULT NULL,
  `left_head_motion_vertical_average` float DEFAULT NULL,
  `left_head_motion_side_average` float DEFAULT NULL,
  `left_head_motion_direction_average` float DEFAULT NULL,
  `right_max_speed` float DEFAULT NULL,
  `right_max_pitch` float DEFAULT NULL,
  `right_max_stride` float DEFAULT NULL,
  `right_average_speed` float DEFAULT NULL,
  `right_average_pitch` float DEFAULT NULL,
  `right_average_stride` float DEFAULT NULL,
  `right_head_motion_forward_average` float DEFAULT NULL,
  `right_head_motion_vertical_average` float DEFAULT NULL,
  `right_head_motion_side_average` float DEFAULT NULL,
  `right_head_motion_direction_average` float DEFAULT NULL,
  PRIMARY KEY (`training_id`,`period_id`),
  KEY `facility_id_fk_2_idx` (`facility_id`),
  CONSTRAINT `training_id_fk_3` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `training_results_distance_series` (
  `training_id` bigint unsigned NOT NULL,
  `period_id` bigint unsigned NOT NULL,
  `distance_to_end` int NOT NULL,
  `period_type` enum('all','all_in_facility','one_lap') NOT NULL,
  `gait` enum('stable','walk','trot','canter','gallop') DEFAULT NULL,
  `lead_leg` enum('right','left') DEFAULT NULL,
  `speed` float DEFAULT NULL,
  `pitch` float DEFAULT NULL,
  `stride` float DEFAULT NULL,
  `head_motion_forward` float DEFAULT NULL,
  `head_motion_vertical` float DEFAULT NULL,
  `head_motion_side` float DEFAULT NULL,
  `head_motion_direction` float DEFAULT NULL,
  `time_to_end` float DEFAULT NULL,
  `heart_rate` int DEFAULT NULL,
  PRIMARY KEY (`training_id`,`period_id`,`distance_to_end`,`period_type`),
  CONSTRAINT `training_id_fk_4` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `training_results_location_series` (
  `training_id` bigint unsigned NOT NULL,
  `period_id` bigint unsigned NOT NULL,
  `timestamp` timestamp(3) NOT NULL,
  `distance_to_end` int NOT NULL,
  `latitude` double(8,6) NOT NULL,
  `longitude` double(9,6) NOT NULL,
  PRIMARY KEY (`training_id`,`period_id`,`timestamp`),
  CONSTRAINT `training_id_fk_5` FOREIGN KEY (`training_id`) REFERENCES `training` (`training_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `upcoming_race_conditions` (
  `upcoming_race_condition_id` int NOT NULL AUTO_INCREMENT,
  `upcoming_race_id` int NOT NULL,
  `condition_raw_string` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `min_age` int DEFAULT NULL,
  `max_age` int DEFAULT NULL,
  `class_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `limit_prize` int DEFAULT NULL,
  `stallion_enterable` tinyint(1) DEFAULT NULL,
  `mare_enterable` tinyint(1) DEFAULT NULL,
  `gelding_enterable` tinyint(1) DEFAULT NULL,
  `international_enterable` tinyint(1) DEFAULT NULL,
  `nar_enterable` tinyint(1) DEFAULT NULL,
  `has_other_conditions` tinyint(1) DEFAULT NULL,
  `impost_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`upcoming_race_condition_id`),
  UNIQUE KEY `upcoming_race_id` (`upcoming_race_id`),
  CONSTRAINT `upcoming_race_conditions_ibfk_1` FOREIGN KEY (`upcoming_race_id`) REFERENCES `upcoming_races` (`upcoming_race_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `upcoming_races` (
  `upcoming_race_id` int NOT NULL AUTO_INCREMENT,
  `race_place_id` int NOT NULL,
  `iteration_number` int NOT NULL,
  `iteration_day_number` int NOT NULL,
  `race_number` int NOT NULL,
  `race_year` int NOT NULL,
  `race_month` int NOT NULL,
  `race_day` int NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `distance` int NOT NULL,
  `track_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`upcoming_race_id`),
  UNIQUE KEY `unique_upcoming_race_constraint` (`race_year`,`race_place_id`,`iteration_number`,`iteration_day_number`,`race_number`),
  KEY `upcoming_races_race_place_id_fk` (`race_place_id`),
  CONSTRAINT `upcoming_races_race_place_id_fk` FOREIGN KEY (`race_place_id`) REFERENCES `race_places` (`race_place_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `user_lang_settings` (
  `user_lang_setting_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_uuid` binary(16) NOT NULL,
  `lang` varchar(10) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_lang_setting_id`),
  KEY `user_lang_settings_user_uuid_fk` (`user_uuid`),
  CONSTRAINT `user_lang_settings_user_uuid_fk` FOREIGN KEY (`user_uuid`) REFERENCES `users` (`user_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `user_roles` (
  `role_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `role_uuid` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `user_uuid` binary(16) DEFAULT NULL,
  `role` enum('admin','staff','jockey') DEFAULT NULL,
  `organization_uuid` binary(16) DEFAULT NULL,
  `stable_uuid` binary(16) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`role_uuid`),
  UNIQUE KEY `role_id` (`role_id`),
  KEY `stable_id_fk_5` (`stable_uuid`),
  KEY `user_uuid_fk` (`user_uuid`),
  KEY `user_roles_organization_fK` (`organization_uuid`),
  CONSTRAINT `stable_id_fk_5` FOREIGN KEY (`stable_uuid`) REFERENCES `stables` (`stable_uuid`),
  CONSTRAINT `user_roles_organization_fK` FOREIGN KEY (`organization_uuid`) REFERENCES `organizations` (`organization_uuid`),
  CONSTRAINT `user_uuid_fk` FOREIGN KEY (`user_uuid`) REFERENCES `users` (`user_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `user_terms_acceptances` (
  `user_terms_acceptance_id` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `accepted_at` datetime NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `owner_id` varchar(50) NOT NULL,
  PRIMARY KEY (`user_terms_acceptance_id`),
  KEY `user_terms_acceptances_owner_id_fk` (`owner_id`),
  CONSTRAINT `user_terms_acceptances_owner_id_fk` FOREIGN KEY (`owner_id`) REFERENCES `owners` (`owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `users` (
  `user_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_uuid` binary(16) NOT NULL DEFAULT (uuid_to_bin(uuid())),
  `firebase_uid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `first_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `middle_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `last_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_uuid`),
  UNIQUE KEY `user_id` (`user_id`),
  UNIQUE KEY `firebase_uid` (`firebase_uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
