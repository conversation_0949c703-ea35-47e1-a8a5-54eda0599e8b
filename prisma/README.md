# Prisma スキーマファイル分割構成

このディレクトリには、Equtumシステムのデータベーススキーマが機能別に分割されたPrismaファイルが含まれています。

## ファイル構成

### メインファイル
- `schema.prisma` - Prismaの基本設定（generator、datasource）
- `prisma.config.ts` - Prisma設定ファイル

### スキーマファイル（`schemas/`ディレクトリ）

#### 1. `common.prisma` - 共通・基盤モデル
**概要**: システム全体で使用される基本的なエンティティ
- **Association** - 協会情報
- **Organization** - 組織情報
- **Stable** - 厩舎情報
- **User** - ユーザー情報
- **UserRole** - ユーザーロール
- **Staff** - スタッフ情報
- **Contract** - 契約情報
- **FarmArea** - 農場エリア
- **MasterFarm** - マスターファーム

#### 2. `horse.prisma` - 馬関連モデル
**概要**: 馬の基本情報と状態管理
- **Horse** - 馬の基本情報
- **HorseStableHistory** - 馬の厩舎履歴
- **HorseStatus** - 馬の現在状態
- **MasterHorse** - マスター馬情報
- **HorseHandoverNote** - 馬の引き継ぎノート

#### 3. `training.prisma` - トレーニング関連モデル
**概要**: トレーニング記録と分析データ
- **Training** - トレーニング基本情報
- **TrainingIndicator** - トレーニング指標
- **TrainingPeriod** - トレーニング期間
- **TrainingIndicatorLabel** - トレーニング指標ラベル
- **TimeSeriesHeartBeatResult** - 時系列心拍結果
- **GaitAnalysisResult** - 歩様分析結果
- **HorseCoursePitchAverage** - 馬のコースピッチ平均
- **TrainingMenu** - トレーニングメニュー

#### 4. `daily_record.prisma` - 日次記録モデル
**概要**: 馬の日々の記録と健康管理
- **HorseDailyRecord** - 馬の日次記録
- **HorseBodyPhoto** - 馬の体写真
- **HorseBodyRecord** - 馬の体調記録
- **HorseTrainingRecord** - 馬のトレーニング記録
- **HorseMedicalTreatmentRecord** - 馬の医療処置記録
- **HorseShoeingRecord** - 馬の装蹄記録
- **HorseRaceResultRecord** - 馬のレース結果記録
- **BusinessTripHistory** - 出張履歴

#### 5. `report.prisma` - レポート関連モデル
**概要**: レポート生成と配信管理
- **Report** - レポート基本情報
- **ReportSection** - レポートセクション
- **ReportSectionPlainText** - レポートプレーンテキスト
- **ReportSectionImage** - レポート画像
- **ReportSectionHorseCondition** - レポート馬の状態
- **ReportSectionWorkoutCondition** - レポートワークアウト状態
- **ReportSectionMonthlySummary** - レポート月次サマリー
- **ReportSectionMedicalTreatment** - レポート医療処置
- **SentReport** - 送信済みレポート
- **ShareReport** - 共有レポート
- **PendingSendReport** - 送信待ちレポート
- **ReportGenerateRequest** - レポート生成リクエスト

#### 6. `stable_management.prisma` - 厩舎管理モデル
**概要**: 厩舎の運営と管理
- **StableTmSection** - 厩舎管理セクション
- **StableTmTransportDailyRecord** - 厩舎管理輸送日次記録
- **StableTmTransportRecord** - 厩舎管理輸送記録
- **StableTmOutsideFarm** - 厩舎管理外部農場

#### 7. `owner.prisma` - オーナー関連モデル
**概要**: 馬主とオーナー管理
- **OrganizationOwner** - 組織オーナー
- **OwnerHorse** - オーナー馬
- **OrganizationOwnerHorseRelation** - 組織オーナー馬関係

#### 8. `facility.prisma` - 施設関連モデル
**概要**: トレーニング施設とコース
- **Facility** - 施設情報
- **TrainingCourse** - トレーニングコース

#### 9. `system.prisma` - システム関連モデル
**概要**: システム設定と機能管理
- **FeatureFlag** - 機能フラグ
- **UserSetting** - ユーザー設定
- **TrainersUserSettings** - トレーナーユーザー設定
- **UserLangSetting** - ユーザー言語設定
- **HorseNoteUserDevice** - 馬ノートユーザーデバイス

#### 10. `log.prisma` - ログ関連モデル
**概要**: システムログと監査
- **AiGenerateLog** - AI生成ログ
- **EmailLog** - メールログ

#### 11. `user_setting.prisma` - ユーザー設定モデル
**概要**: ユーザー固有の設定
- **UserSetting** - ユーザー設定

## スキーマ分割の利点

1. **保守性の向上**: 機能別に分割することで、特定の機能に関する変更が他の部分に影響しにくい
2. **チーム開発の効率化**: 異なるチームが異なる機能のスキーマを並行して開発可能
3. **可読性の向上**: 関連するモデルが同じファイルにまとまっているため、理解しやすい
4. **スケーラビリティ**: 新機能追加時に新しいスキーマファイルを作成しやすい

## 使用方法

1. **開発時**: 各スキーマファイルを個別に編集
2. **クライアント生成**: `prisma generate`で全スキーマからクライアントが生成される

## 注意事項

- 各スキーマファイル間の依存関係に注意
- 外部キー制約は適切に設定されている
- モデル名の重複がないよう注意
- マイグレーション実行前に全スキーマファイルの整合性を確認 
