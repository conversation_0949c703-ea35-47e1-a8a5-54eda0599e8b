enum Gender {
  male
  female
  gelding
}

model Horse {
  horseId          BigInt    @id @default(autoincrement()) @map("horse_id")
  organizationUuid Bytes?    @map("organization_uuid") @db.Binary(16)
  stableUuid       Bytes     @map("stable_uuid") @db.Binary(16)
  stable           Stable    @relation(fields: [stableUuid], references: [stableUuid])
  name             String    @map("name")
  gender           Gender?   @map("gender")
  birthYear        Int?      @map("birth_year")
  fatherId         Int?      @map("father_id")
  motherId         Int?      @map("mother_id")
  rfId             Int?      @map("rfid")
  profilePicPath   String?   @map("profile_pic_path")
  masterHorseId    String?   @map("master_horse_id")
  manageStatus     String    @default("managed") @map("manage_status")
  deletedAt        DateTime? @map("deleted_at")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")

  organization Organization? @relation(fields: [organizationUuid], references: [organizationUuid])
  masterHorse  MasterHorse?  @relation(fields: [masterHorseId], references: [masterHorseId])

  horseStableHistories            HorseStableHistory[]
  organizationOwnerHorseRelations OrganizationOwnerHorseRelation[]
  reports                         Report[]
  pendingSendReports              PendingSendReport[]
  horseDailyRecords               HorseDailyRecord[]
  horseHandoverNotes              HorseHandoverNote[]
  trainingPartners                TrainingPartner[]
  ReportGenerateRequest           ReportGenerateRequest[]
  trainings                       Training[]
  businessTripHistories           BusinessTripHistoryHorse[]
  StableTmTransportRecord         StableTmTransportRecord[]
  horseStatus                     HorseStatus?

  @@map("horses")
}

model HorseStableHistory {
  horseStableHistoryUuid Bytes    @id @map("horse_stable_history_uuid") @db.Binary(16)
  horseId                BigInt   @map("horse_id")
  stableUuid             Bytes    @map("stable_uuid") @db.Binary(16)
  inStable               Boolean  @default(true) @map("in_stable")
  createdAt              DateTime @default(now()) @map("created_at")

  horse  Horse  @relation(fields: [horseId], references: [horseId])
  stable Stable @relation(fields: [stableUuid], references: [stableUuid])

  @@map("horse_stable_history")
}

model HorseStatus {
  horseStatusId         Bytes     @id @map("horse_status_id") @db.Binary(16)
  horseStatusInternalId BigInt    @unique @default(autoincrement()) @map("horse_status_internal_id")
  horseId               BigInt    @unique @map("horse_id")
  inStable              Boolean   @default(true) @map("in_stable")
  outsideFarmId         Bytes?    @map("outside_farm_id") @db.Binary(16)
  latestStableInDate    DateTime? @map("latest_stable_in_date")
  latestStableOutDate   DateTime? @map("latest_stable_out_date")
  createdAt             DateTime  @default(now()) @map("created_at")
  updatedAt             DateTime  @updatedAt @map("updated_at")

  horse       Horse                @relation(fields: [horseId], references: [horseId])
  outsideFarm StableTmOutsideFarm? @relation(fields: [outsideFarmId], references: [outsideFarmId])

  @@map("horse_statuses")
}

model MasterHorse {
  masterHorseId String   @id @map("master_horse_id")
  horseName     String   @map("horse_name")
  horseNameEn   String?  @map("horse_name_en")
  motherName    String   @map("mother_name")
  gender        String   @map("gender")
  birthYear     Int      @map("birth_year")
  stableName    String?  @map("stable_name")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  horses                  Horse[]
  ownerHorses             OwnerHorse[]
  HorseCoursePitchAverage HorseCoursePitchAverage[]

  @@map("master_horses")
}

model HorseHandoverNote {
  horseHandoverNoteId         Bytes    @id @map("horse_handover_note_id") @db.Binary(16)
  horseHandoverNoteInternalId BigInt   @unique @default(autoincrement()) @map("horse_handover_note_internal_id")
  horseId                     BigInt   @unique @map("horse_id") @db.UnsignedBigInt
  handoverNote                String?  @map("handover_note") @db.Text
  nextRaceEquipmentNote       String?  @map("next_race_equipment_note") @db.Text
  fodderNote                  String?  @map("fodder_note") @db.Text
  createdAt                   DateTime @default(now()) @map("created_at")
  updatedAt                   DateTime @default(now()) @updatedAt @map("updated_at")

  horse Horse @relation(fields: [horseId], references: [horseId])

  @@map("horse_handover_notes")
}
