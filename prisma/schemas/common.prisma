model Association {
  associationId         String   @id @map("association_id")
  associationInternalId BigInt   @unique @default(autoincrement()) @map("association_internal_id")
  associationName       String   @map("association_name")
  associationType       String?  @map("association_type")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")

  facilities    Facility[]
  organizations Organization[]

  @@map("associations")
}

model Organization {
  organizationUuid Bytes    @id @map("organization_uuid") @db.Binary(16)
  organizationId   Int      @unique @default(autoincrement()) @map("organization_id")
  associationId    String?  @map("association_id")
  name             String   @map("name")
  home_facility_id String?  @map("home_facility_id")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  facility    Facility?    @relation(fields: [home_facility_id], references: [facilityId])
  association Association? @relation(fields: [associationId], references: [associationId])

  stables                   Stable[]
  userRoles                 UserRole[]
  horses                    Horse[]
  organizationOwners        OrganizationOwner[]
  reports                   Report[]
  organizationVeterinarians OrganizationVeterinarian[]
  organizationFarriers      OrganizationFarrier[]
  staffs                    Staff[]
  contracts                 Contract[]
  featureFlag               FeatureFlag?
  horseDailyRecords         HorseDailyRecord[]
  businessTripHistories     BusinessTripHistory[]
  StableTmOutsideFarm       StableTmOutsideFarm[]

  @@map("organizations")
}

model Stable {
  stableUuid       Bytes    @id @map("stable_uuid") @db.Binary(16)
  organizationUuid Bytes    @map("organization_uuid") @db.Binary(16)
  name             String   @map("name")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  organization Organization @relation(fields: [organizationUuid], references: [organizationUuid])

  stableStatus StableStatus?

  userRoles                    UserRole[]
  horses                       Horse[]
  horseStableHistories         HorseStableHistory[]
  staffs                       Staff[]
  trainingMenus                TrainingMenu[]
  trainings                    Training[]
  stableTmSections             StableTmSection[]
  stableTmTransportDailyRecord StableTmTransportDailyRecord[]

  @@map("stables")
}

model StableStatus {
  stableStatusId BigInt   @id @default(autoincrement()) @map("stable_status_id")
  stableUuid     Bytes    @unique @map("stable_uuid") @db.Binary(16)
  stallNum       Int      @map("stall_num")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  stable Stable? @relation(fields: [stableUuid], references: [stableUuid])

  @@map("stable_status")
}

model User {
  userUuid    Bytes     @id @map("user_uuid") @db.Binary(16)
  userId      Int       @unique @default(autoincrement()) @map("user_id")
  firebaseUid String    @unique @map("firebase_uid")
  firstName   String    @map("first_name")
  middleName  String?   @map("middle_name")
  lastName    String    @map("last_name")
  deletedAt   DateTime? @map("deleted_at")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  userRoles            UserRole[]
  staffs               Staff[]
  HorseNoteUserDevice  HorseNoteUserDevice[]
  TrainersUserSettings TrainersUserSettings[]
  UserLangSettings     UserLangSetting[]
  horseDailyRecords    HorseDailyRecord[]

  @@map("users")
}

enum Role {
  admin
  staff
  jockey
}

model UserRole {
  roleUuid         Bytes    @id @map("role_uuid") @db.Binary(16)
  roleId           Int      @unique @default(autoincrement()) @map("role_id")
  userUuid         Bytes?   @map("user_uuid") @db.Binary(16)
  role             Role     @map("role")
  organizationUuid Bytes?   @map("organization_uuid") @db.Binary(16)
  stableUuid       Bytes?   @map("stable_uuid") @db.Binary(16)
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  organization Organization? @relation(fields: [organizationUuid], references: [organizationUuid])
  stable       Stable?       @relation(fields: [stableUuid], references: [stableUuid])
  user         User?         @relation(fields: [userUuid], references: [userUuid])

  @@map("user_roles")
}

model Staff {
  staffUuid        Bytes     @id @default(dbgenerated("(uuid_to_bin(uuid()))")) @map("staff_uuid")
  stableUuid       Bytes     @map("stable_uuid")
  name             String
  organizationUuid Bytes?    @map("organization_uuid") @db.Binary(16)
  user_uuid        Bytes?    @map("user_uuid") @db.Binary(16)
  deletedAt        DateTime? @map("deleted_at")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")

  stable       Stable?       @relation(fields: [stableUuid], references: [stableUuid])
  organization Organization? @relation(fields: [organizationUuid], references: [organizationUuid])
  user         User?         @relation(fields: [user_uuid], references: [userUuid])

  horseRaceRecapRecords      HorseRaceRecapRecord[]
  horseTrainingRecords       HorseTrainingRecord[]
  StableTmTransportOutStatus StableTmTransportOutStatus[]
  StableTmTransportInStatus  StableTmTransportInStatus[]

  @@map("staffs")
}

model Contract {
  contractId       BigInt   @id @default(autoincrement()) @map("contract_id")
  organizationUuid Bytes    @unique @map("organization_uuid") @db.Binary(16)
  hasHnContract    Boolean  @default(false) @map("has_hn_contract")
  hasOrmContract   Boolean  @default(false) @map("has_orm_contract")
  hasOrmAiContract Boolean  @default(false) @map("has_orm_ai_contract")
  hasStmContracts  Boolean  @default(false) @map("has_stm_contracts")
  hasRmContract    Boolean  @default(false) @map("has_rm_contract")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  organization Organization @relation(fields: [organizationUuid], references: [organizationUuid])

  @@map("contracts")
}

model FarmArea {
  farmAreaId         Bytes    @id @map("farm_area_id") @db.Binary(16)
  farmAreaInternalId BigInt   @unique @default(autoincrement()) @map("farm_area_internal_id") @db.UnsignedBigInt
  name               String   @map("name")
  order              Int      @map("order")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")

  masterFarms         MasterFarm[]
  StableTmOutsideFarm StableTmOutsideFarm[]

  @@map("farm_areas")
}

model MasterFarm {
  masterFarmId         Bytes    @id @map("master_farm_id") @db.Binary(16)
  masterFarmInternalId BigInt   @unique @default(autoincrement()) @map("master_farm_internal_id") @db.UnsignedBigInt
  farmAreaId           Bytes    @map("farm_area_id") @db.Binary(16)
  name                 String   @map("name")
  createdAt            DateTime @default(now()) @map("created_at")
  updatedAt            DateTime @updatedAt @map("updated_at")

  farmArea             FarmArea              @relation(fields: [farmAreaId], references: [farmAreaId])
  stableTmOutsideFarms StableTmOutsideFarm[]

  @@map("master_farms")
}
