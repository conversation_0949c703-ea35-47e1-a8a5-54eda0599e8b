model EmailLog {
  emailLogInternalId   BigInt   @unique @default(autoincrement()) @map("email_log_internal_id")
  email                String   @map("email")
  recipientFirebaseUid String?  @map("recipient_firebase_uid")
  senderFirebaseUid    String?  @map("sender_firebase_uid")
  emailTemplateKey     String   @map("email_template_key")
  createdAt            DateTime @default(now()) @map("created_at")

  @@map("email_logs")
}

model AiGenerateLog {
  aiGenerateLogId         String   @id @map("ai_generate_log_id") @db.VarChar(50)
  aiGenerateLogInternalId BigInt   @unique @default(autoincrement()) @map("ai_generate_log_internal_id") @db.UnsignedBigInt
  model                   String?  @map("model") @db.VarChar(100)
  requestPrompt           String?  @map("request_prompt") @db.Text
  response                String?  @map("response") @db.Text
  promptTokens            Int?     @map("prompt_tokens") @db.UnsignedInt
  completionTokens        Int?     @map("completion_tokens") @db.UnsignedInt
  totalTokens             Int?     @map("total_tokens") @db.UnsignedInt
  finishReason            String?  @map("finish_reason") @db.VarChar(100)
  createdAt               DateTime @default(now()) @map("created_at")
  updatedAt               DateTime @default(now()) @updatedAt @map("updated_at")

  reportGenerateRequests ReportGenerateRequest[]

  @@map("ai_generate_logs")
}
