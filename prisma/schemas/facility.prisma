model Facility {
  facilityId         String   @id @map("facility_id")
  facilityInternalId BigInt   @unique @default(autoincrement()) @map("facility_internal_id")
  associationId      String   @map("association_id")
  facilityName       String   @map("facility_name")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")

  association                    Association                     @relation(fields: [associationId], references: [associationId])
  trainingCourseMasters          TrainingCourseMaster[]
  reportSectionWorkoutConditions ReportSectionWorkoutCondition[]
  horseTrainingRecords           HorseTrainingRecord[]
  organizations                  Organization[]

  @@map("facilities")
}

model TrainingCourseMaster {
  trainingCourseMasterId         String   @id @map("training_course_master_id")
  trainingCourseMasterInternalId BigInt   @unique @default(autoincrement()) @map("training_course_master_internal_id")
  courseName                     String   @map("course_name")
  facilityName                   String?  @map("facility_name")
  facilityId                     String?  @map("facility_id")
  createdAt                      DateTime @default(now()) @map("created_at")
  updatedAt                      DateTime @updatedAt @map("updated_at")
  tempTrainingFacilityMasterUuid Bytes?   @map("temp_training_facility_master_uuid")

  trainingCourses TrainingCourse[]
  facility        Facility?        @relation(fields: [facilityId], references: [facilityId])

  @@map("training_course_masters")
}

model TrainingCourse {
  courseId               String    @id @map("course_id")
  courseInternalId       BigInt    @unique @default(autoincrement()) @map("course_internal_id")
  trainingCourseMasterId String?   @map("training_course_master_id")
  courseName             String    @map("course_name")
  distance               Float?
  lastNFurlong           Int?      @default(2) @map("last_n_furlong")
  openedAt               DateTime? @map("opened_at")
  closedAt               DateTime? @map("closed_at")
  createdAt              DateTime  @default(now()) @map("created_at")
  updatedAt              DateTime  @updatedAt @map("updated_at")
  tempFacilityId         BigInt?   @map("temp_facility_id")

  trainingCourseMaster           TrainingCourseMaster?           @relation(fields: [trainingCourseMasterId], references: [trainingCourseMasterId])
  reportSectionWorkoutConditions ReportSectionWorkoutCondition[]
  horseTrainingRecords           HorseTrainingRecord[]
  HorseCoursePitchAverage        HorseCoursePitchAverage[]
  TrainingPeriod                 TrainingPeriod[]

  @@map("training_courses")
}

enum FurlongLineDirection {
  left
  right
  straight
}

model FurlongLine {
  furlongLineInternalId BigInt               @id @default(autoincrement()) @map("furlong_line_internal_id")
  facilityId            BigInt               @map("facility_id")
  courseId              String?              @map("course_id") @db.VarChar(50)
  furlong               Int                  @map("furlong")
  direction             FurlongLineDirection @map("direction")
  line                  Bytes                @map("line")
  createdAt             DateTime             @default(now()) @map("created_at")
  updatedAt             DateTime             @updatedAt @map("updated_at")

  @@unique([facilityId, furlong, direction], map: "idx_facility_id_furlong_direction")
  @@map("furlong_lines")
}
