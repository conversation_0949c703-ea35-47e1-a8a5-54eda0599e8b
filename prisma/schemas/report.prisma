model Report {
  reportId         String    @id @map("report_id")
  reportInternalId BigInt    @unique @default(autoincrement()) @map("report_internal_id")
  organizationUuid Bytes     @map("organization_uuid") @db.Binary(16)
  horseId          BigInt    @map("horse_id")
  title            String    @map("title")
  templateId       String    @map("template_id") @db.VarChar(255)
  isDraft          Boolean   @default(true) @map("is_draft")
  printed          Boolean   @default(false) @map("printed")
  firstSentAt      DateTime? @map("first_sent_at")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")

  horse                 Horse                   @relation(fields: [horseId], references: [horseId])
  organization          Organization            @relation(fields: [organizationUuid], references: [organizationUuid])
  reportSections        ReportSection[]
  sentReports           SentReport[]
  pendingSendReports    PendingSendReport[]
  reportGenerateRequest ReportGenerateRequest[]
  shareReport           ShareReport[]

  @@map("reports")
}

model ReportSection {
  reportSectionId         Bytes    @id @default(dbgenerated("(UUID_TO_BIN(UUID()))")) @map("report_section_id") @db.Binary(16)
  reportSectionInternalId BigInt   @unique @default(autoincrement()) @map("report_section_internal_id")
  reportId                String   @map("report_id")
  type                    String   @map("type")
  templateInnerId         String   @map("template_inner_id")
  createdAt               DateTime @default(now()) @map("created_at")
  updatedAt               DateTime @updatedAt @map("updated_at")

  report                         Report                          @relation(fields: [reportId], references: [reportId])
  reportSectionHorseConditions   ReportSectionHorseCondition[]
  reportSectionPlainTexts        ReportSectionPlainText[]
  reportSectionImages            ReportSectionImage[]
  reportSectionWorkoutConditions ReportSectionWorkoutCondition[]
  reportSectionMonthlySummaries  ReportSectionMonthlySummary[]
  reportSectionMonthlyTimelines  ReportSectionMonthlyTimeline[]
  reportSectionMedicalTreatments ReportSectionMedicalTreatment[]

  @@unique([reportId, templateInnerId])
  @@map("report_sections")
}

model ReportSectionPlainText {
  reportSectionPlainTextId Bytes    @id @default(dbgenerated("(UUID_TO_BIN(UUID()))")) @map("report_section_plain_text_id") @db.Binary(16)
  reportSectionId          Bytes    @map("report_section_id") @db.Binary(16)
  body                     String?  @map("body")
  createdAt                DateTime @default(now()) @map("created_at")
  updatedAt                DateTime @updatedAt @map("updated_at")

  reportSection ReportSection @relation(fields: [reportSectionId], references: [reportSectionId])

  @@map("report_section_plain_texts")
}

model ReportSectionImage {
  reportSectionImageId Bytes     @id @default(dbgenerated("(UUID_TO_BIN(UUID()))")) @map("report_section_image_id") @db.Binary(16)
  reportSectionId      Bytes     @map("report_section_id") @db.Binary(16)
  imagePath            String?   @map("image_path")
  capturedAt           DateTime? @map("captured_at")
  createdAt            DateTime  @default(now()) @map("created_at")
  updatedAt            DateTime  @updatedAt @map("updated_at")

  reportSection ReportSection @relation(fields: [reportSectionId], references: [reportSectionId])

  @@map("report_section_images")
}

model ReportSectionHorseCondition {
  reportSectionHorseConditionId Bytes    @id @default(dbgenerated("(UUID_TO_BIN(UUID()))")) @map("report_section_horse_condition_id") @db.Binary(16)
  reportSectionId               Bytes    @map("report_section_id") @db.Binary(16)
  horseWeight                   Int?     @map("horse_weight")
  horseWeightMeasuredDate       String?  @map("horse_weight_measured_date")
  isGaitAbnormal                Boolean? @map("is_gait_abnormal")
  gaitComment                   String?  @map("gait_comment")
  createdAt                     DateTime @default(now()) @map("created_at")
  updatedAt                     DateTime @updatedAt @map("updated_at")

  reportSection ReportSection @relation(fields: [reportSectionId], references: [reportSectionId])

  @@map("report_section_horse_conditions")
}

model ReportSectionWorkoutCondition {
  reportSectionWorkoutConditionId Bytes   @id @default(dbgenerated("(UUID_TO_BIN(UUID()))")) @map("report_section_workout_condition_id") @db.Binary(16)
  reportSectionId                 Bytes   @map("report_section_id") @db.Binary(16)
  workoutTrainingDate             String? @map("workout_training_date")
  riderName                       String? @map("rider_name")
  runningStyle                    String? @map("running_style")
  facilityId                      String? @map("facility_id")
  courseId                        String? @map("course_id")
  workoutFurlongTime              String? @map("workout_furlong_time")
  workoutFurlongPosition          String? @map("workout_furlong_time_position")
  partnerNumber                   Int?    @map("partner_number")
  partner1Name                    String? @map("partner_1_name")
  partner2Name                    String? @map("partner_2_name")
  courseGoing                     String? @map("course_going")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  reportSection  ReportSection   @relation(fields: [reportSectionId], references: [reportSectionId])
  facility       Facility?       @relation(fields: [facilityId], references: [facilityId])
  trainingCourse TrainingCourse? @relation(fields: [courseId], references: [courseId])

  @@map("report_section_workout_conditions")
}

model ReportSectionMonthlySummary {
  reportSectionMonthlySummaryId         Bytes    @id @default(dbgenerated("(UUID_TO_BIN(UUID()))")) @map("report_section_monthly_summary_id") @db.Binary(16)
  reportSectionMonthlySummaryInternalId BigInt   @unique @default(autoincrement()) @map("report_section_monthly_summary_internal_id")
  reportSectionId                       Bytes    @map("report_section_id") @db.Binary(16)
  startYear                             Int      @map("start_year")
  startMonth                            Int      @map("start_month")
  startDay                              Int      @map("start_day")
  endYear                               Int      @map("end_year")
  endMonth                              Int      @map("end_month")
  endDay                                Int      @map("end_day")
  horseBodyWeightHistory                String?  @map("horse_body_weight_history") @db.Text
  createdAt                             DateTime @default(now()) @map("created_at")
  updatedAt                             DateTime @updatedAt @map("updated_at")

  reportSection                          ReportSection                           @relation(fields: [reportSectionId], references: [reportSectionId])
  reportSectionMonthlySummaryRaceRecords ReportSectionMonthlySummaryRaceRecord[]

  @@map("report_section_monthly_summaries")
}

model ReportSectionMonthlySummaryRaceRecord {
  reportSectionMonthlySummaryRaceRecordId         Bytes    @id @default(dbgenerated("(UUID_TO_BIN(UUID()))")) @map("report_section_monthly_summary_race_record_id") @db.Binary(16)
  reportSectionMonthlySummaryRaceRecordInternalId BigInt   @unique @default(autoincrement()) @map("report_section_monthly_summary_race_record_internal_id")
  reportSectionMonthlySummaryId                   Bytes    @map("report_section_monthly_summary_id") @db.Binary(16)
  year                                            Int      @map("year")
  month                                           Int      @map("month")
  day                                             Int      @map("day")
  body                                            String?  @map("body") @db.Text
  createdAt                                       DateTime @default(now()) @map("created_at")
  updatedAt                                       DateTime @updatedAt @map("updated_at")

  reportSectionMonthlySummary ReportSectionMonthlySummary @relation(fields: [reportSectionMonthlySummaryId], references: [reportSectionMonthlySummaryId])

  @@map("report_section_monthly_summary_race_records")
}

model ReportSectionMonthlyTimeline {
  reportSectionMonthlyTimelineId         Bytes    @id @default(dbgenerated("(UUID_TO_BIN(UUID()))")) @map("report_section_monthly_timeline_id") @db.Binary(16)
  reportSectionMonthlyTimelineInternalId BigInt   @unique @default(autoincrement()) @map("report_section_monthly_timeline_internal_id")
  reportSectionId                        Bytes    @map("report_section_id") @db.Binary(16)
  createdAt                              DateTime @default(now()) @map("created_at")
  updatedAt                              DateTime @updatedAt @map("updated_at")

  reportSection                       ReportSection                        @relation(fields: [reportSectionId], references: [reportSectionId])
  reportSectionMonthlyTimelineRecords ReportSectionMonthlyTimelineRecord[]

  @@map("report_section_monthly_timelines")
}

model ReportSectionMonthlyTimelineRecord {
  reportSectionMonthlyTimelineRecordId         Bytes    @id @default(dbgenerated("(UUID_TO_BIN(UUID()))")) @map("report_section_monthly_timeline_record_id") @db.Binary(16)
  reportSectionMonthlyTimelineRecordInternalId BigInt   @unique @default(autoincrement()) @map("report_section_monthly_timeline_record_internal_id")
  reportSectionMonthlyTimelineId               Bytes    @map("report_section_monthly_timeline_id") @db.Binary(16)
  year                                         Int      @map("year")
  month                                        Int      @map("month")
  day                                          Int      @map("day")
  body                                         String?  @map("body") @db.Text
  trainingMenu                                 String?  @map("training_menu") @db.Text
  assignee                                     String?  @map("assignee") @db.Text
  furlongTime                                  String?  @map("furlong_time") @db.VarChar(255)
  index                                        String   @map("index") @db.VarChar(255)
  createdAt                                    DateTime @default(now()) @map("created_at")
  updatedAt                                    DateTime @updatedAt @map("updated_at")

  reportSectionMonthlyTimeline ReportSectionMonthlyTimeline @relation(fields: [reportSectionMonthlyTimelineId], references: [reportSectionMonthlyTimelineId])

  @@map("report_section_monthly_timeline_records")
}

model ReportSectionMedicalTreatment {
  reportSectionMedicalTreatmentId         Bytes    @id @default(dbgenerated("(UUID_TO_BIN(UUID()))")) @map("report_section_medical_treatment_id") @db.Binary(16)
  reportSectionMedicalTreatmentInternalId BigInt   @unique @default(autoincrement()) @map("report_section_medical_treatment_internal_id")
  reportSectionId                         Bytes    @map("report_section_id") @db.Binary(16)
  createdAt                               DateTime @default(now()) @map("created_at")
  updatedAt                               DateTime @updatedAt @map("updated_at")

  reportSection                        ReportSection                         @relation(fields: [reportSectionId], references: [reportSectionId])
  reportSectionMedicalTreatmentRecords ReportSectionMedicalTreatmentRecord[]

  @@map("report_section_medical_treatments")
}

model ReportSectionMedicalTreatmentRecord {
  reportSectionMedicalTreatmentRecordId         Bytes    @id @default(dbgenerated("(UUID_TO_BIN(UUID()))")) @map("report_section_medical_treatment_record_id") @db.Binary(16)
  reportSectionMedicalTreatmentRecordInternalId BigInt   @unique @default(autoincrement()) @map("report_section_medical_treatment_record_internal_id")
  reportSectionMedicalTreatmentId               Bytes    @map("report_section_medical_treatment_id") @db.Binary(16)
  year                                          Int      @map("year")
  month                                         Int      @map("month")
  day                                           Int      @map("day")
  body                                          String?  @map("body") @db.Text
  veterinarian                                  String?  @map("veterinarian") @db.VarChar(255)
  createdAt                                     DateTime @default(now()) @map("created_at")
  updatedAt                                     DateTime @updatedAt @map("updated_at")

  reportSectionMedicalTreatment                   ReportSectionMedicalTreatment                    @relation(fields: [reportSectionMedicalTreatmentId], references: [reportSectionMedicalTreatmentId])
  reportSectionMedicalTreatmentAffectedAreaPhotos ReportSectionMedicalTreatmentAffectedAreaPhoto[]

  @@map("report_section_medical_treatment_records")
}

model ReportSectionMedicalTreatmentAffectedAreaPhoto {
  reportSectionMedicalTreatmentAffectedAreaPhotoId         Bytes    @id @default(dbgenerated("(UUID_TO_BIN(UUID()))")) @map("report_section_medical_treatment_affected_area_photo_id") @db.Binary(16)
  reportSectionMedicalTreatmentAffectedAreaPhotoInternalId BigInt   @unique @default(autoincrement()) @map("report_section_medical_treatment_affected_area_photo_internal_id")
  reportSectionMedicalTreatmentRecordId                    Bytes    @map("report_section_medical_treatment_record_id") @db.Binary(16)
  photoPath                                                String   @map("photo_path")
  createdAt                                                DateTime @default(now()) @map("created_at")
  updatedAt                                                DateTime @updatedAt @map("updated_at")

  reportSectionMedicalTreatmentRecord ReportSectionMedicalTreatmentRecord @relation(fields: [reportSectionMedicalTreatmentRecordId], references: [reportSectionMedicalTreatmentRecordId])

  @@map("report_section_medical_treatment_affected_area_photos")
}

model SentReport {
  sentReportId         String    @id @map("sent_report_id")
  sentReportInternalId BigInt    @unique @default(autoincrement()) @map("sent_report_internal_id")
  reportId             String    @map("report_id")
  organizationOwnerId  String    @map("organization_owner_id")
  ownerHorseId         String    @map("owner_horse_id")
  sentAt               DateTime  @default(now()) @map("sent_at")
  firstReadAt          DateTime? @map("first_read_at")
  createdAt            DateTime  @default(now()) @map("created_at")
  updatedAt            DateTime  @updatedAt @map("updated_at")

  report            Report            @relation(fields: [reportId], references: [reportId])
  organizationOwner OrganizationOwner @relation(fields: [organizationOwnerId], references: [organizationOwnerId])
  ownerHorse        OwnerHorse        @relation(fields: [ownerHorseId], references: [ownerHorseId])

  @@map("sent_reports")
}

model ShareReport {
  shareReportId         String   @id @map("share_report_id")
  shareReportInternalId BigInt   @unique @default(autoincrement()) @map("share_report_internal_id")
  reportId              String   @map("report_id")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")

  report Report @relation(fields: [reportId], references: [reportId])

  @@map("share_reports")
}

model PendingSendReport {
  pendingSendReportInternalId BigInt    @unique @default(autoincrement()) @map("pending_send_report_internal_id")
  reportId                    String    @map("report_id")
  organizationOwnerId         String    @map("organization_owner_id")
  horseId                     BigInt    @map("horse_id")
  sentAt                      DateTime? @map("sent_at")
  createdAt                   DateTime  @default(now()) @map("created_at")
  updatedAt                   DateTime  @updatedAt @map("updated_at")

  report            Report            @relation(fields: [reportId], references: [reportId])
  organizationOwner OrganizationOwner @relation(fields: [organizationOwnerId], references: [organizationOwnerId])
  horse             Horse             @relation(fields: [horseId], references: [horseId])

  @@map("pending_send_reports")
}

model ReportGenerateRequest {
  reportGenerateRequestId         String   @id @map("report_generate_request_id") @db.VarChar(50)
  reportGenerateRequestInternalId BigInt   @unique @default(autoincrement()) @map("report_generate_request_internal_id") @db.UnsignedBigInt
  horseId                         BigInt   @map("horse_id") @db.UnsignedBigInt
  reportId                        String?  @map("report_id") @db.VarChar(50)
  requestMemo                     String?  @map("request_memo") @db.Text
  generatedContent                String?  @map("generated_content") @db.Text
  aiGenerateLogId                 String?  @map("ai_generate_log_id") @db.VarChar(50)
  createdAt                       DateTime @default(now()) @map("created_at")
  updatedAt                       DateTime @default(now()) @updatedAt @map("updated_at")

  horse         Horse          @relation(fields: [horseId], references: [horseId])
  report        Report?        @relation(fields: [reportId], references: [reportId])
  aiGenerateLog AiGenerateLog? @relation(fields: [aiGenerateLogId], references: [aiGenerateLogId])

  @@map("report_generate_requests")
}
