model TrainersUserSettings {
  trainersUserSettingsId            Bytes    @id @default(dbgenerated("(uuid_to_bin(uuid()))")) @map("trainers_user_settings_id") @db.Binary(16)
  userUuid                          Bytes    @map("user_uuid") @db.Binary(16)
  reportUnreadFilterTruncateEnabled <PERSON><PERSON><PERSON>  @default(false) @map("report_unread_filter_truncate_enabled")
  createdAt                         DateTime @default(now()) @map("created_at")
  updatedAt                         DateTime @updatedAt @map("updated_at")

  user User? @relation(fields: [userUuid], references: [userUuid])

  @@map("trainers_user_settings")
}

model UserLangSetting {
  userLangSettingId BigInt   @id @default(autoincrement()) @map("user_lang_setting_id")
  userUuid          Bytes    @map("user_uuid") @db.Binary(16)
  lang              String   @map("lang")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  user User? @relation(fields: [userUuid], references: [userUuid])

  @@unique([userUuid], map: "user_lang_settings_unique")
  @@map("user_lang_settings")
}
