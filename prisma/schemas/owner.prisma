model OrganizationOwner {
  organizationOwnerId         String   @id @map("organization_owner_id")
  organizationOwnerInternalId BigInt   @unique @default(autoincrement()) @map("organization_owner_internal_id")
  organizationUuid            Bytes    @map("organization_uuid")
  ownerId                     String   @map("owner_id")
  organizationOwnerName       String   @map("organization_owner_name")
  createdAt                   DateTime @default(now()) @map("created_at")
  updatedAt                   DateTime @updatedAt @map("updated_at")

  organization Organization @relation(fields: [organizationUuid], references: [organizationUuid])
  owner        Owner        @relation(fields: [ownerId], references: [ownerId])

  invitations                     Invitation[]
  organizationOwnerHorseRelations OrganizationOwnerHorseRelation[]
  sentReports                     SentReport[]
  pendingSendReports              PendingSendReport[]

  @@unique([organizationUuid, ownerId])
  @@map("organization_owners")
}

model OrganizationOwnerHorseRelation {
  organizationOwnerHorseRelationId         String   @id @map("organization_owner_horse_relation_id")
  organizationOwnerHorseRelationInternalId BigInt   @unique @default(autoincrement()) @map("organization_owner_horse_relation_internal_id")
  organizationOwnerId                      String   @map("organization_owner_id")
  horseId                                  BigInt   @map("horse_id")
  createdAt                                DateTime @default(now()) @map("created_at")
  updatedAt                                DateTime @updatedAt @map("updated_at")

  organizationOwner OrganizationOwner @relation(fields: [organizationOwnerId], references: [organizationOwnerId])
  horse             Horse             @relation(fields: [horseId], references: [horseId])

  @@map("organization_owner_horse_relations")
}

enum InvitationMethod {
  email
  link
}

model Invitation {
  invitationId         String           @id @map("invitation_id")
  invitationInternalId BigInt           @unique @default(autoincrement()) @map("invitation_internal_id")
  organizationOwnerId  String           @map("organization_owner_id")
  token                String
  acceptedAt           DateTime?        @map("accepted_at")
  method               InvitationMethod
  inviteEmail          String?          @map("invite_email")
  email_dispatch_count Int              @default(0) @map("email_dispatch_count")
  createdAt            DateTime         @default(now()) @map("created_at")
  updatedAt            DateTime         @updatedAt @map("updated_at")

  organizationOwner OrganizationOwner @relation(fields: [organizationOwnerId], references: [organizationOwnerId])

  @@unique([organizationOwnerId, token])
  @@map("invitations")
}

model Owner {
  ownerId         String    @id @map("owner_id")
  ownerInternalId BigInt    @unique @default(autoincrement()) @map("owner_internal_id")
  ownerName       String?   @map("owner_name")
  firebaseUid     String?   @unique @map("firebase_uid")
  verifiedAt      DateTime? @map("verified_at")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")

  organizationOwners   OrganizationOwner[]
  ownerHorses          OwnerHorse[]
  userTermsAcceptances UserTermsAcceptances[]

  @@map("owners")
}

model OwnerHorse {
  ownerHorseId         String   @id @map("owner_horse_id")
  ownerHorseInternalId BigInt   @unique @default(autoincrement()) @map("owner_horse_internal_id")
  name                 String   @map("name")
  ownerId              String   @map("owner_id")
  masterHorseId        String   @map("master_horse_id")
  createdAt            DateTime @default(now()) @map("created_at")
  updatedAt            DateTime @updatedAt @map("updated_at")

  owner       Owner       @relation(fields: [ownerId], references: [ownerId])
  masterHorse MasterHorse @relation(fields: [masterHorseId], references: [masterHorseId])

  sentReports SentReport[]

  @@unique([ownerId, masterHorseId])
  @@map("owner_horses")
}
