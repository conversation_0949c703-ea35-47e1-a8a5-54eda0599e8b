model StableTmOutsideFarm {
  outsideFarmId         Bytes     @id @map("outside_farm_id") @db.Binary(16)
  outsideFarmInternalId BigInt    @unique @default(autoincrement()) @map("outside_farm_internal_id") @db.UnsignedBigInt
  organizationUuid      Bytes     @map("organization_uuid") @db.Binary(16)
  farmAreaId            Bytes     @map("farm_area_id") @db.Binary(16)
  name                  String    @map("name")
  masterFarmId          Bytes?    @map("master_farm_id") @db.Binary(16)
  deletedAt             DateTime? @map("deleted_at")
  createdAt             DateTime  @default(now()) @map("created_at")
  updatedAt             DateTime  @updatedAt @map("updated_at")

  organization               Organization                 @relation(fields: [organizationUuid], references: [organizationUuid])
  farmArea                   FarmArea                     @relation(fields: [farmAreaId], references: [farmAreaId])
  masterFarm                 MasterFarm?                  @relation(fields: [masterFarmId], references: [masterFarmId])
  StableTmTransportOutStatus StableTmTransportOutStatus[]
  HorseStatus                HorseStatus[]

  @@map("stable_tm_outside_farm")
}

model StableTmSection {
  sectionId         Bytes    @id @map("section_id") @db.Binary(16)
  sectionInternalId BigInt   @unique @default(autoincrement()) @map("section_internal_id") @db.UnsignedBigInt
  stableUuid        Bytes    @map("stable_uuid") @db.Binary(16)
  startDate         DateTime @map("start_date")
  endDate           DateTime @map("end_date")
  status            String?  @map("status")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  stable                Stable                         @relation(fields: [stableUuid], references: [stableUuid])
  transportQueueTickets StableTmTransportQueueTicket[]
  fixedSlots            StableTmFixedSlot[]
  transportDailyRecords StableTmTransportDailyRecord[]

  @@map("stable_tm_sections")
}

model StableTmFixedSlot {
  fixedSlotId         Bytes    @id @map("fixed_slot_id") @db.Binary(16)
  fixedSlotInternalId BigInt   @unique @default(autoincrement()) @map("fixed_slot_internal_id") @db.UnsignedBigInt
  sectionId           Bytes    @map("section_id") @db.Binary(16)
  numberOfSection     Int      @map("number_of_section")
  slotNum             Int      @map("slot_num")
  createdAt           DateTime @default(now()) @map("created_at")
  updatedAt           DateTime @updatedAt @map("updated_at")

  section StableTmSection @relation(fields: [sectionId], references: [sectionId])

  @@map("stable_tm_fixed_slots")
}

model StableTmTransportQueueTicket {
  transportQueueTicketId         Bytes    @id @map("transport_queue_ticket_id") @db.Binary(16)
  transportQueueTicketInternalId BigInt   @unique @default(autoincrement()) @map("transport_queue_ticket_internal_id") @db.UnsignedBigInt
  sectionId                      Bytes    @map("section_id") @db.Binary(16)
  ticketKey                      String   @map("ticket_key")
  createdAt                      DateTime @default(now()) @map("created_at")
  updatedAt                      DateTime @updatedAt @map("updated_at")

  section StableTmSection @relation(fields: [sectionId], references: [sectionId])

  @@map("stable_tm_transport_queue_tickets")
}

model StableTmTransportDailyRecord {
  transportDailyRecordId         Bytes    @id @map("transport_daily_record_id") @db.Binary(16)
  transportDailyRecordInternalId BigInt   @unique @default(autoincrement()) @map("transport_daily_record_internal_id") @db.UnsignedBigInt
  sectionId                      Bytes    @map("section_id") @db.Binary(16)
  stableUuid                     Bytes    @map("stable_uuid") @db.Binary(16)
  year                           Int      @map("year")
  month                          Int      @map("month")
  day                            Int      @map("day")
  isConfirmed                    Boolean  @default(false) @map("is_confirmed")
  createdAt                      DateTime @default(now()) @map("created_at")
  updatedAt                      DateTime @updatedAt @map("updated_at")

  section          StableTmSection           @relation(fields: [sectionId], references: [sectionId])
  stable           Stable                    @relation(fields: [stableUuid], references: [stableUuid])
  transportRecords StableTmTransportRecord[]

  @@map("stable_tm_transport_daily_records")
}

model StableTmTransportRecord {
  transportRecordId         Bytes    @id @map("transport_record_id") @db.Binary(16)
  transportRecordInternalId BigInt   @unique @default(autoincrement()) @map("transport_record_internal_id") @db.UnsignedBigInt
  transportDailyRecordId    Bytes?   @map("transport_daily_record_id") @db.Binary(16)
  type                      String   @map("type")
  horseId                   BigInt   @map("horse_id") @db.UnsignedBigInt
  index                     String   @map("index") @db.VarChar(255)
  createdAt                 DateTime @default(now()) @map("created_at")
  updatedAt                 DateTime @updatedAt @map("updated_at")

  transportDailyRecord StableTmTransportDailyRecord? @relation(fields: [transportDailyRecordId], references: [transportDailyRecordId])
  horse                Horse                         @relation(fields: [horseId], references: [horseId])
  transportInStatus    StableTmTransportInStatus?
  transportOutStatus   StableTmTransportOutStatus?

  @@map("stable_tm_transport_records")
}

model StableTmTransportInStatus {
  transportInStatusId         Bytes    @id @map("transport_in_status_id") @db.Binary(16)
  transportInStatusInternalId BigInt   @unique @default(autoincrement()) @map("transport_in_status_internal_id") @db.UnsignedBigInt
  transportRecordId           Bytes    @unique @map("transport_record_id") @db.Binary(16)
  staffUuid                   Bytes?   @map("staff_uuid") @db.Binary(16)
  isHorseVanArranged          Boolean  @default(false) @map("is_horse_van_arranged")
  isQuarantineApplied         Boolean  @default(false) @map("is_quarantine_applied")
  isOwnerContacted            Boolean  @default(false) @map("is_owner_contacted")
  isFarmContacted             Boolean  @default(false) @map("is_farm_contacted")
  nextRace                    String?  @map("next_race")
  comment                     String?  @map("comment")
  createdAt                   DateTime @default(now()) @map("created_at")
  updatedAt                   DateTime @updatedAt @map("updated_at")

  transportRecord StableTmTransportRecord @relation(fields: [transportRecordId], references: [transportRecordId])
  staff           Staff?                  @relation(fields: [staffUuid], references: [staffUuid])

  @@map("stable_tm_transport_in_statuses")
}

model StableTmTransportOutStatus {
  transportOutStatusId          Bytes    @id @map("transport_out_status_id") @db.Binary(16)
  transportOutStatusInternalId  BigInt   @unique @default(autoincrement()) @map("transport_out_status_internal_id") @db.UnsignedBigInt
  transportRecordId             Bytes    @unique @map("transport_record_id") @db.Binary(16)
  staffUuid                     Bytes?   @map("staff_uuid") @db.Binary(16)
  isStableOutProcedureCompleted Boolean  @default(false) @map("is_stable_out_procedure_completed")
  isHorseVanArranged            Boolean  @default(false) @map("is_horse_van_arranged")
  isOwnerContacted              Boolean  @default(false) @map("is_owner_contacted")
  isFarmContacted               Boolean  @default(false) @map("is_farm_contacted")
  farmId                        Bytes?   @map("farm_id") @db.Binary(16)
  comment                       String?  @map("comment")
  createdAt                     DateTime @default(now()) @map("created_at")
  updatedAt                     DateTime @updatedAt @map("updated_at")

  transportRecord           StableTmTransportRecord            @relation(fields: [transportRecordId], references: [transportRecordId])
  staff                     Staff?                             @relation(fields: [staffUuid], references: [staffUuid])
  farm                      StableTmOutsideFarm?               @relation(fields: [farmId], references: [outsideFarmId])
  transportOutHandoverNotes StableTmTransportOutHandoverNote[]

  @@map("stable_tm_transport_out_statuses")
}

model StableTmTransportOutHandoverNote {
  transportOutHandoverNoteId         Bytes     @id @map("transport_out_handover_note_id") @db.Binary(16)
  transportOutHandoverNoteInternalId BigInt    @unique @default(autoincrement()) @map("transport_out_handover_note_internal_id") @db.UnsignedBigInt
  transportOutStatusId               Bytes     @map("transport_out_status_id") @db.Binary(16)
  body                               String?   @map("body")
  latestHorseShoeingDate             DateTime? @map("latest_horse_shoeing_date")
  latestHorseShoeingFarrier          String?   @map("latest_horse_shoeing_farrier")
  latestHorseShoeingBody             String?   @map("latest_horse_shoeing_body")
  latestHorseBodyWeightDate          DateTime? @map("latest_horse_body_weight_date")
  latestHorseBodyWeight              String?   @map("latest_horse_body_weight")
  createdAt                          DateTime  @default(now()) @map("created_at")
  updatedAt                          DateTime  @updatedAt @map("updated_at")

  transportOutStatus StableTmTransportOutStatus @relation(fields: [transportOutStatusId], references: [transportOutStatusId])

  @@map("stable_tm_transport_out_handover_notes")
}
