model Training {
  trainingId         BigInt    @id @default(autoincrement()) @map("training_id")
  horseId            BigInt    @map("horse_id")
  trainerUuid        Bytes?    @map("trainer_uuid") @db.Binary(16)
  trainerId          BigInt?   @map("trainer_id")
  stableUuid         Bytes     @map("stable_uuid") @db.Binary(16)
  startAt            DateTime? @map("start_at")
  endAt              DateTime? @map("end_at")
  analysisStartedAt  DateTime? @map("analysis_started_at")
  analysisEndedAt    DateTime? @map("analysis_ended_at")
  locationDataPath   String?   @map("location_data_path") @db.VarChar(200)
  locationDataExt    String?   @map("location_data_ext") @db.VarChar(50)
  gyroAndAccDataPath String?   @map("gyro_and_acc_data_path") @db.VarChar(200)
  gyroAndAccDataExt  String?   @map("gyro_and_acc_data_ext") @db.<PERSON>ar<PERSON><PERSON>(50)
  deletedAt          DateTime? @map("deleted_at")
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @updatedAt @map("updated_at")

  timeSeriesHeartBeatResults TimeSeriesHeartBeatResult[]
  trainingIndicators         TrainingIndicator[]
  trainingPeriods            TrainingPeriod[]
  gaitAnalysisResult         GaitAnalysisResult[]
  horse                      Horse?                      @relation(fields: [horseId], references: [horseId])
  stable                     Stable?                     @relation(fields: [stableUuid], references: [stableUuid])

  // Indexes
  @@unique([trainingId])
  @@map("training")
}

model TrainingIndicator {
  trainingIndicatorId               BigInt   @id @default(autoincrement()) @map("training_indicator_id")
  trainingId                        BigInt   @map("training_id")
  periodGroupId                     Int      @map("period_group_id")
  facilityId                        BigInt?  @map("facility_id")
  courseId                          String?  @map("course_id") @db.VarChar(50)
  maxHeartRate                      Int?     @map("max_heart_rate")
  maxHeartRateInAll                 Int?     @map("max_heart_rate_in_all")
  maxHeartRateInLap                 Int?     @map("max_heart_rate_in_lap")
  thr100                            Int?     @map("thr100")
  v200                              Float?   @map("v200")
  oneMinuteHeartRate                Int?     @map("one_minute_heart_rate")
  threeMinutesMinHeartRate          Int?     @map("three_minutes_min_heart_rate")
  heartRateGap                      Int?     @map("heart_rate_gap")
  thirtySecondsAfterGoalHeartRate   Int?     @map("thirty_seconds_after_goal_heart_rate")
  oneMinuteAfterGoalHeartRate       Int?     @map("one_minute_after_goal_heart_rate")
  twoMinutesAfterGoalHeartRate      Int?     @map("two_minutes_after_goal_heart_rate")
  twoMinutesAfterGoalMinHeartRate   Int?     @map("two_minutes_after_goal_min_heart_rate")
  threeMinutesAfterGoalMinHeartRate Int?     @map("three_minutes_after_goal_min_heart_rate")
  createdAt                         DateTime @default(now()) @map("created_at")
  updatedAt                         DateTime @updatedAt @map("updated_at")

  training               Training                 @relation(fields: [trainingId], references: [trainingId])
  TrainingIndicatorLabel TrainingIndicatorLabel[]

  @@index([trainingId], map: "training_indicators_training_id_fk")
  @@map("training_indicators")
}

model TrainingPeriod {
  trainingPeriodUuid Bytes      @id @default(dbgenerated("(uuid_to_bin(uuid()))")) @map("training_period_uuid")
  trainingId         BigInt     @map("training_id")
  periodGroupId      Int        @map("period_group_id")
  periodType         PeriodType @map("period_type")
  facilityId         BigInt?    @map("facility_id")
  courseId           String?    @map("course_id") @db.VarChar(50)
  direction          String?    @map("direction") @db.VarChar(50)
  startAt            DateTime?  @map("start_at")
  startTime          Float      @map("start_time")
  endTime            Float      @map("end_time")
  startDistance      Float      @map("start_distance")
  endDistance        Float      @map("end_distance")
  lapCount           Int?       @map("lap_count")
  isMain             Boolean    @default(false) @map("is_main")
  leftLegRatio       Float?     @map("left_leg_ratio")
  totalDistance      Float?     @map("total_distance")
  createdAt          DateTime   @default(now()) @map("created_at")
  updatedAt          DateTime   @updatedAt @map("updated_at")

  training       Training        @relation(fields: [trainingId], references: [trainingId])
  trainingCourse TrainingCourse? @relation(fields: [courseId], references: [courseId])

  @@unique([trainingId, periodGroupId, lapCount], map: "training_periods_training_period_group_lap_unique")
  @@map("training_periods")
}

enum PeriodType {
  all
  all_in_facility
  one_lap
}

model TrainingIndicatorLabel {
  trainingIndicatorLabelId BigInt   @id @default(autoincrement()) @map("training_indicator_label_id")
  trainingIndicatorId      BigInt   @map("training_indicator_id")
  label                    String   @map("label") @db.VarChar(50)
  time                     Int      @map("time")
  distance                 Int      @map("distance")
  createdAt                DateTime @default(now()) @map("created_at")
  updatedAt                DateTime @updatedAt @map("updated_at")

  trainingIndicator TrainingIndicator @relation(fields: [trainingIndicatorId], references: [trainingIndicatorId])

  @@index([trainingIndicatorId], map: "training_indicator_labels_training_indicator_id_fk")
  @@map("training_indicator_labels")
}

model TimeSeriesHeartBeatResult {
  timeSeriesHeartBeatResultId BigInt   @id @default(autoincrement()) @map("time_series_heart_beat_result_id") @db.UnsignedBigInt
  trainingId                  BigInt   @map("training_id") @db.UnsignedBigInt
  time                        Int
  heartRate                   Int?     @map("heart_rate")
  sympatheticNerve            Float?   @map("sympathetic_nerve")
  parasympatheticNerve        Float?   @map("parasympathetic_nerve")
  createdAt                   DateTime @default(now()) @map("created_at")

  training Training @relation(fields: [trainingId], references: [trainingId])

  @@unique([trainingId, time])
  @@map("time_series_heart_beat_results")
}

model GaitAnalysisResult {
  gaitAnalysisResultUuid   Bytes    @id @default(dbgenerated("(uuid_to_bin(uuid()))")) @map("gait_analysis_result_uuid") @db.Binary(16)
  trainingId               BigInt   @map("training_id")
  startAt                  DateTime @map("start_at")
  endAt                    DateTime @map("end_at")
  gait                     Gait?    @map("gait")
  impactLeftFront          Float?   @map("impact_left_front")
  impactRightFront         Float?   @map("impact_right_front")
  impactLeftBack           Float?   @map("impact_left_back")
  impactRightBack          Float?   @map("impact_right_back")
  swingTimeRatioLeftFront  Float?   @map("swing_time_ratio_left_front")
  swingTimeRatioRightFront Float?   @map("swing_time_ratio_right_front")
  swingTimeRatioLeftBack   Float?   @map("swing_time_ratio_left_back")
  swingTimeRatioRightBack  Float?   @map("swing_time_ratio_right_back")
  footOnAngleLeftFront     Float?   @map("foot_on_angle_left_front")
  footOnAngleRightFront    Float?   @map("foot_on_angle_right_front")
  footOnAngleLeftBack      Float?   @map("foot_on_angle_left_back")
  footOnAngleRightBack     Float?   @map("foot_on_angle_right_back")
  footOffAngleLeftFront    Float?   @map("foot_off_angle_left_front")
  footOffAngleRightFront   Float?   @map("foot_off_angle_right_front")
  footOffAngleLeftBack     Float?   @map("foot_off_angle_left_back")
  footOffAngleRightBack    Float?   @map("foot_off_angle_right_back")
  createdAt                DateTime @default(now()) @map("created_at")

  training Training @relation(fields: [trainingId], references: [trainingId])

  @@map("gait_analysis_results")
}

enum Gait {
  stable
  walk
  trot
  canter
  gallop
}

model HorseCoursePitchAverage {
  horseCoursePitchAverageUuid Bytes    @id @default(dbgenerated("(uuid_to_bin(uuid()))")) @map("horse_course_pitch_average_uuid")
  masterHorseId               String   @map("master_horse_id") @db.VarChar(50)
  courseId                    String   @map("course_id") @db.VarChar(50)
  speed                       Int      @map("speed")
  relativeAveragePitch        Float?   @map("relative_average_pitch")
  absoluteAveragePitch        Float?   @map("absolute_average_pitch")
  deviationScore              Float?   @map("deviation_score")
  createdAt                   DateTime @default(now()) @map("created_at")
  updatedAt                   DateTime @updatedAt @map("updated_at")

  masterHorse MasterHorse    @relation(fields: [masterHorseId], references: [masterHorseId])
  course      TrainingCourse @relation(fields: [courseId], references: [courseId])

  @@unique([masterHorseId, courseId, speed], map: "unique_horse_course_speed")
  @@index([masterHorseId], map: "horse_course_pitch_averages_master_horse_id_fk")
  @@index([courseId], map: "horse_course_pitch_averages_course_id_idx")
  @@map("horse_course_pitch_averages")
}

model TrainingMenu {
  trainingMenuUuid       Bytes     @id @map("training_menu_uuid") @db.Binary(16)
  trainingMenuInternalId BigInt    @unique @default(autoincrement()) @map("training_menu_internal_id") @db.UnsignedBigInt
  trainingMenuName       String    @map("training_menu_name")
  trainingType           String?   @map("training_type") @db.VarChar(255)
  stableUuid             Bytes     @map("stable_uuid") @db.Binary(16)
  deletedAt              DateTime? @map("deleted_at")
  createdAt              DateTime  @default(now()) @map("created_at")
  updatedAt              DateTime  @updatedAt @map("updated_at")

  stable               Stable                @relation(fields: [stableUuid], references: [stableUuid])
  horseTrainingRecords HorseTrainingRecord[]

  @@map("training_menus")
}
