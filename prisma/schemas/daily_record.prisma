model HorseDailyRecord {
  horseDailyRecordId Bytes    @id @map("horse_daily_record_id") @db.Binary(16)
  organizationUuid   Bytes    @map("organization_uuid") @db.Binary(16)
  horseId            BigInt   @map("horse_id")
  createdUserUuid    Bytes?   @map("created_user_uuid") @db.Binary(16)
  year               Int      @map("year")
  month              Int      @map("month")
  day                Int      @map("day")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")

  organization                 Organization                  @relation(fields: [organizationUuid], references: [organizationUuid])
  horse                        Horse                         @relation(fields: [horseId], references: [horseId])
  createdUser                  User?                         @relation(fields: [createdUserUuid], references: [userUuid])
  horseBodyPhotos              HorseBodyPhoto[]
  horseBodyRecord              HorseBodyRecord?
  horseRaceResultRecord        HorseRaceResultRecord?
  horseRaceRecapRecord         HorseRaceRecapRecord?
  horseShoeingRecords          HorseShoeingRecord[]
  horseMedicalTreatmentRecords HorseMedicalTreatmentRecord[]
  horseTrainingRecords         HorseTrainingRecord[]
  horseBodyAffectedAreaPhotos  HorseBodyAffectedAreaPhoto[]

  @@unique([horseId, year, month, day])
  @@map("horse_daily_records")
}

model HorseBodyPhoto {
  horseBodyPhotoId   Bytes    @id @map("horse_body_photo_id") @db.Binary(16)
  horseDailyRecordId Bytes    @map("horse_daily_record_id") @db.Binary(16)
  photoPath          String   @map("photo_path")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")

  horseDailyRecord HorseDailyRecord @relation(fields: [horseDailyRecordId], references: [horseDailyRecordId])

  @@map("horse_daily_records_horse_body_photos")
}

model HorseBodyAffectedAreaPhoto {
  horseBodyAffectedAreaPhotoId         Bytes    @id @map("horse_body_affected_area_photo_id") @db.Binary(16)
  horseBodyAffectedAreaPhotoInternalId BigInt   @unique @default(autoincrement()) @map("horse_body_affected_area_photo_internal_id")
  horseDailyRecordId                   Bytes    @map("horse_daily_record_id") @db.Binary(16)
  daypart                              String   @map("daypart") @db.VarChar(50)
  photoPath                            String   @map("photo_path")
  createdAt                            DateTime @default(now()) @map("created_at")
  updatedAt                            DateTime @updatedAt @map("updated_at")

  horseDailyRecord HorseDailyRecord @relation(fields: [horseDailyRecordId], references: [horseDailyRecordId])

  @@map("horse_daily_records_horse_body_affected_area_photos")
}

model HorseBodyRecord {
  horseBodyRecordId  Bytes    @id @map("horse_body_record_id") @db.Binary(16)
  horseDailyRecordId Bytes    @unique @map("horse_daily_record_id") @db.Binary(16)
  bodyWeight         Int?     @map("body_weight")
  amBodyTemperature  Float?   @map("am_body_temperature")
  pmBodyTemperature  Float?   @map("pm_body_temperature")
  amHorseBodyComment String?  @map("am_horse_body_comment")
  pmHorseBodyComment String?  @map("pm_horse_body_comment")
  amHorseBodyCare    String?  @map("am_horse_body_care")
  pmHorseBodyCare    String?  @map("pm_horse_body_care")
  freeComment        String?  @map("free_comment")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")

  horseDailyRecord HorseDailyRecord @relation(fields: [horseDailyRecordId], references: [horseDailyRecordId])

  @@map("horse_daily_records_horse_bodies")
}

model HorseTrainingRecord {
  trainingRecordUuid       Bytes    @id @map("training_record_uuid") @db.Binary(16)
  trainingRecordInternalId BigInt   @unique @default(autoincrement()) @map("training_record_internal_id") @db.UnsignedBigInt
  horseDailyRecordId       Bytes    @map("horse_daily_record_id") @db.Binary(16)
  isGaitAbnormal           Boolean? @map("is_gait_abnormal")
  gaitAbnormalDescription  String?  @map("gait_abnormal_description")
  trainingType             String?  @map("training_type")
  riderUuid                Bytes?   @map("rider_uuid") @db.Binary(16)
  trainingMenuUuid         Bytes?   @map("training_menu_uuid") @db.Binary(16)
  gateTrainingType         String?  @map("gate_training_type")
  facilityId               String?  @map("facility_id")
  facilityName             String?  @map("facility_name")
  courseId                 String?  @map("course_id")
  courseGoing              String?  @map("course_going")
  furlongTime              String?  @map("furlong_time")
  furlongTimePosition      String?  @map("furlong_time_position")
  poolTrainingType         String?  @map("pool_training_type")
  lactateLevel             Float?   @map("lactate_level")
  trainingComment          String?  @map("training_comment")
  createdAt                DateTime @default(now()) @map("created_at")
  updatedAt                DateTime @updatedAt @map("updated_at")

  horseDailyRecord HorseDailyRecord  @relation(fields: [horseDailyRecordId], references: [horseDailyRecordId])
  rider            Staff?            @relation(fields: [riderUuid], references: [staffUuid])
  trainingMenu     TrainingMenu?     @relation(fields: [trainingMenuUuid], references: [trainingMenuUuid])
  facility         Facility?         @relation(fields: [facilityId], references: [facilityId])
  trainingCourse   TrainingCourse?   @relation(fields: [courseId], references: [courseId])
  trainingPartners TrainingPartner[]

  @@map("horse_daily_records_trainings")
}

model TrainingPartner {
  trainingPartnerId  Bytes    @id @map("training_partner_id") @db.Binary(16)
  trainingRecordUuid Bytes    @map("training_record_uuid") @db.Binary(16)
  horseId            BigInt?  @map("horse_id")
  rank               Int?     @map("rank")
  horseName          String   @map("horse_name")
  trackPosition      String?  @map("track_position")
  startingOrder      Int?     @map("starting_order")
  detail             String?  @map("detail")
  intensity          String?  @map("intensity")
  margin             String?  @map("margin")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")

  horseTrainingRecord HorseTrainingRecord @relation(fields: [trainingRecordUuid], references: [trainingRecordUuid])
  horse               Horse?              @relation(fields: [horseId], references: [horseId])

  @@map("horse_daily_records_training_partners")
}

model OrganizationVeterinarian {
  organizationVeterinarianId   Bytes                         @id @map("organization_veterinarian_id") @db.Binary(16)
  veterinarianName             String                        @map("veterinarian_name")
  organizationUuid             Bytes                         @map("organization_uuid") @db.Binary(16)
  deletedAt                    DateTime?                     @map("deleted_at")
  createdAt                    DateTime                      @default(now()) @map("created_at")
  updatedAt                    DateTime                      @updatedAt @map("updated_at")
  horseMedicalTreatmentRecords HorseMedicalTreatmentRecord[]

  organization Organization @relation(fields: [organizationUuid], references: [organizationUuid])

  @@map("organization_veterinarians")
}

model OrganizationFarrier {
  organizationFarrierId Bytes                @id @map("organization_farrier_id") @db.Binary(16)
  farrierName           String               @map("farrier_name")
  organizationUuid      Bytes                @map("organization_uuid") @db.Binary(16)
  deletedAt             DateTime?            @map("deleted_at")
  createdAt             DateTime             @default(now()) @map("created_at")
  updatedAt             DateTime             @updatedAt @map("updated_at")
  horseShoeingRecords   HorseShoeingRecord[]

  organization Organization @relation(fields: [organizationUuid], references: [organizationUuid])

  @@map("organization_farriers")
}

model HorseMedicalTreatmentRecord {
  horseMedicalTreatmentRecordId   Bytes    @id @map("horse_medical_treatment_record_id") @db.Binary(16)
  horseDailyRecordId              Bytes    @map("horse_daily_record_id") @db.Binary(16)
  horseMedicalTreatmentReason     String?  @map("medical_treatment_reason")
  horseMedicalTreatmentInspection String?  @map("medical_treatment_inspection")
  horseMedicalTreatmentResult     String?  @map("medical_treatment_result")
  horseMedicalTreatmentDetail     String?  @map("medical_treatment_detail")
  organizationVeterinarianId      Bytes?   @map("organization_veterinarian_id") @db.Binary(16)
  createdAt                       DateTime @default(now()) @map("created_at")
  updatedAt                       DateTime @updatedAt @map("updated_at")

  horseDailyRecord HorseDailyRecord          @relation(fields: [horseDailyRecordId], references: [horseDailyRecordId])
  veterinarian     OrganizationVeterinarian? @relation(fields: [organizationVeterinarianId], references: [organizationVeterinarianId])

  horseMedicalTreatmentInvoicePhotos      HorseMedicalTreatmentInvoicePhoto[]
  horseMedicalTreatmentAffectedAreaPhotos HorseMedicalTreatmentAffectedAreaPhoto[]

  @@map("horse_daily_records_horse_medical_treatments")
}

model HorseMedicalTreatmentInvoicePhoto {
  horseMedicalTreatmentRecordInvoicePhotoId         Bytes    @id @map("horse_medical_treatment_invoice_photo_id") @db.Binary(16)
  horseMedicalTreatmentRecordInvoicePhotoInternalId BigInt   @unique @default(autoincrement()) @map("horse_medical_treatment_invoice_photo_internal_id")
  horseMedicalTreatmentRecordId                     Bytes    @map("horse_medical_treatment_record_id") @db.Binary(16)
  photoPath                                         String   @map("photo_path")
  createdAt                                         DateTime @default(now()) @map("created_at")
  updatedAt                                         DateTime @updatedAt @map("updated_at")

  horseMedicalTreatmentRecord HorseMedicalTreatmentRecord @relation(fields: [horseMedicalTreatmentRecordId], references: [horseMedicalTreatmentRecordId])

  @@map("horse_daily_records_horse_medical_treatment_invoice_photos")
}

model HorseMedicalTreatmentAffectedAreaPhoto {
  horseMedicalTreatmentAffectedAreaPhotoId         Bytes    @id @default(dbgenerated("(UUID_TO_BIN(UUID()))")) @map("horse_medical_treatment_affected_area_photo_id") @db.Binary(16)
  horseMedicalTreatmentAffectedAreaPhotoInternalId BigInt   @unique @default(autoincrement()) @map("horse_medical_treatment_affected_area_photo_internal_id")
  horseMedicalTreatmentRecordId                    Bytes    @map("horse_medical_treatment_record_id") @db.Binary(16)
  photoPath                                        String   @map("photo_path")
  createdAt                                        DateTime @default(now()) @map("created_at")
  updatedAt                                        DateTime @updatedAt @map("updated_at")

  horseMedicalTreatmentRecord HorseMedicalTreatmentRecord @relation(fields: [horseMedicalTreatmentRecordId], references: [horseMedicalTreatmentRecordId])

  @@map("horse_daily_records_horse_medical_treatment_affected_area_photos")
}

model HorseShoeingRecord {
  horseShoeingRecordId      Bytes    @id @map("horse_shoeing_record_id") @db.Binary(16)
  horseDailyRecordId        Bytes    @map("horse_daily_record_id") @db.Binary(16)
  organizationFarrierId     Bytes?   @map("organization_farrier_id") @db.Binary(16)
  horseShoeingTreatmentType String?  @map("horse_shoeing_treatment_type")
  createdAt                 DateTime @default(now()) @map("created_at")
  updatedAt                 DateTime @updatedAt @map("updated_at")
  comment                   String?  @map("comment")

  horseDailyRecord          HorseDailyRecord           @relation(fields: [horseDailyRecordId], references: [horseDailyRecordId])
  farrier                   OrganizationFarrier?       @relation(fields: [organizationFarrierId], references: [organizationFarrierId])
  horseShoeingInvoicePhotos HorseShoeingInvoicePhoto[]

  @@map("horse_daily_records_horse_shoeings")
}

model HorseShoeingInvoicePhoto {
  horseShoeingInvoicePhotoId         Bytes    @id @map("horse_shoeing_invoice_photo_id") @db.Binary(16)
  horseShoeingInvoicePhotoInternalId BigInt   @unique @default(autoincrement()) @map("horse_shoeing_invoice_photo_internal_id")
  horseShoeingRecordId               Bytes    @map("horse_shoeing_record_id") @db.Binary(16)
  photoPath                          String   @map("photo_path")
  createdAt                          DateTime @default(now()) @map("created_at")
  updatedAt                          DateTime @updatedAt @map("updated_at")

  horseShoeingRecord HorseShoeingRecord @relation(fields: [horseShoeingRecordId], references: [horseShoeingRecordId])

  @@map("horse_daily_records_horse_shoeing_invoice_photos")
}

model HorseRaceResultRecord {
  raceResultId         Bytes    @id @map("race_result_id") @db.Binary(16)
  raceResultInternalId BigInt   @unique @default(autoincrement()) @map("race_result_internal_id") @db.UnsignedBigInt
  horseDailyRecordId   Bytes    @unique @map("horse_daily_record_id") @db.Binary(16)
  racePlaceId          Int?     @map("race_place_id")
  raceName             String?  @map("race_name") @db.VarChar(255)
  raceNumber           Int?     @map("race_number")
  distance             Int?
  going                String?  @db.VarChar(50)
  trackType            String?  @map("track_type") @db.VarChar(100)
  jockeyName           String?  @map("jockey_name") @db.VarChar(100)
  weight               Int?
  beforeRaceWeightDiff Int?     @map("before_race_weight_diff")
  rank                 Int?
  createdAt            DateTime @default(now()) @map("created_at")
  updatedAt            DateTime @updatedAt @map("updated_at")

  horseDailyRecord HorseDailyRecord @relation(fields: [horseDailyRecordId], references: [horseDailyRecordId])
  racePlace        RacePlace?       @relation(fields: [racePlaceId], references: [racePlaceId])

  @@map("horse_daily_records_race_results")
}

model HorseRaceRecapRecord {
  raceRecapId         Bytes    @id @map("race_recap_id") @db.Binary(16)
  raceRecapInternalId BigInt   @unique @default(autoincrement()) @map("race_recap_internal_id") @db.UnsignedBigInt
  horseDailyRecordId  Bytes    @unique @map("horse_daily_record_id") @db.Binary(16)
  attendance          String?  @db.VarChar(100)
  staffUuid           Bytes?   @map("staff_uuid") @db.Binary(16)
  equipment           String?  @db.VarChar(255)
  transportComment    String?  @map("transport_comment") @db.Text
  stallComment        String?  @map("stall_comment") @db.Text
  paddockComment      String?  @map("paddock_comment") @db.Text
  warmUpComment       String?  @map("warm_up_comment") @db.Text
  gateComment         String?  @map("gate_comment") @db.Text
  raceStrategyComment String?  @map("race_strategy_comment") @db.Text
  afterRaceComment    String?  @map("after_race_comment") @db.Text
  jockeyComment       String?  @map("jockey_comment") @db.Text
  trainerComment      String?  @map("trainer_comment") @db.Text
  nextRaceComment     String?  @map("next_race_comment") @db.Text
  createdAt           DateTime @default(now()) @map("created_at")
  updatedAt           DateTime @updatedAt @map("updated_at")

  horseDailyRecord HorseDailyRecord @relation(fields: [horseDailyRecordId], references: [horseDailyRecordId])
  staff            Staff?           @relation(fields: [staffUuid], references: [staffUuid])

  @@map("horse_daily_records_race_recaps")
}

model RacePlace {
  racePlaceId Int     @id @default(autoincrement()) @map("race_place_id")
  name        String  @db.VarChar(255)
  fullName    String? @map("full_name") @db.VarChar(255)
  code        String? @unique @db.VarChar(20)
  region      String? @db.VarChar(255)

  horseRaceResultRecords HorseRaceResultRecord[]

  @@map("race_places")
}

model BusinessTripHistory {
  businessTripHistoryId Bytes    @id @default(dbgenerated("(uuid_to_bin(uuid()))")) @map("business_trip_history_id") @db.Binary(16)
  organizationUuid      Bytes    @map("organization_uuid") @db.Binary(16)
  staffName             String?  @map("staff_name")
  destinationName       String?  @map("destination_name")
  startYear             Int?     @map("start_year")
  startMonth            Int?     @map("start_month")
  startDay              Int?     @map("start_day")
  endYear               Int?     @map("end_year")
  endMonth              Int?     @map("end_month")
  endDay                Int?     @map("end_day")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @default(now()) @updatedAt @map("updated_at")

  organization Organization               @relation(fields: [organizationUuid], references: [organizationUuid])
  horses       BusinessTripHistoryHorse[]

  @@map("business_trip_histories")
}

model BusinessTripHistoryHorse {
  businessTripHistoryHorseId Bytes    @id @default(dbgenerated("(uuid_to_bin(uuid()))")) @map("business_trip_history_horse_id") @db.Binary(16)
  businessTripHistoryId      Bytes    @map("business_trip_history_id") @db.Binary(16)
  horseId                    BigInt   @map("horse_id")
  createdAt                  DateTime @default(now()) @map("created_at")
  updatedAt                  DateTime @default(now()) @updatedAt @map("updated_at")

  businessTripHistory BusinessTripHistory @relation(fields: [businessTripHistoryId], references: [businessTripHistoryId])
  horse               Horse               @relation(fields: [horseId], references: [horseId])

  @@unique([businessTripHistoryId, horseId], map: "business_trip_history_horses_unique")
  @@map("business_trip_history_horses")
}
