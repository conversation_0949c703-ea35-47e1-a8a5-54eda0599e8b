#!/bin/bash

# Development setup script for Equtum Batch Processing

set -e

echo "🚀 Setting up Equtum Batch development environment..."

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from .env.example..."
    cp .env.example .env
    echo "✅ .env file created. Please update it with your configuration."
else
    echo "✅ .env file already exists."
fi

# Install dependencies
echo "📦 Installing Node.js dependencies..."
npm install

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

# Run database migrations
echo "🗃️  Running database migrations..."
npx prisma migrate dev --name init

echo "🎉 Development environment setup complete!"
echo ""
echo "📖 Next steps:"
echo "   1. Update your .env file with proper database credentials"
echo "   2. Start development: npm run dev"
echo "   3. Open Prisma Studio: npm run prisma:studio"
echo ""
echo "🐳 To use with Docker:"
echo "   docker-compose -f docker-compose.dev.yml up -d" 