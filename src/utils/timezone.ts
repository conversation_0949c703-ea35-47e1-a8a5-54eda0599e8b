import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

// プラグインを拡張
dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * UTCの現在時刻を取得
 * @returns UTCの現在時刻（ISO文字列形式）
 */
export const getCurrentUTCTime = () => dayjs().utc().toISOString();

/**
 * UTCの現在時刻を取得（Date形式）
 * @returns UTCの現在時刻（Date形式）
 */
export const getCurrentUTCDate = () => dayjs().utc().toDate();

/**
 * ローカル時刻をUTCに変換
 * @param localTime ローカル時刻（ISO文字列形式）
 * @returns UTC時刻（Date形式）
 */
export const getLocalTimeToUTCDate = (localTime: string, timezone: string) =>
  dayjs.tz(localTime, timezone).utc().toDate();

/**
 * 指定されたタイムゾーンの現在時刻を取得
 * @param timezone タイムゾーン（例: 'Asia/Tokyo', 'America/New_York'）
 * @returns 指定されたタイムゾーンの現在時刻（ISO文字列形式）
 */
export const getCurrentTimeInTimezone = (timezone: string) => dayjs().tz(timezone).toISOString();

/**
 * UTC時刻を指定されたタイムゾーンに変換
 * @param utcTime UTC時刻（ISO文字列形式）
 * @param timezone 変換先タイムゾーン
 * @returns 変換された時刻（ISO文字列形式）
 */
export const convertUTCToTimezone = (utcTime: string, timezone: string) =>
  dayjs(utcTime).tz(timezone).toISOString();

/**
 * 指定されたタイムゾーンの時刻をUTCに変換
 * @param localTime ローカル時刻（ISO文字列形式）
 * @param timezone 元のタイムゾーン
 * @returns UTC時刻（ISO文字列形式）
 */
export const convertTimezoneToUTC = (localTime: string, timezone: string) =>
  dayjs.tz(localTime, timezone).utc().toISOString();

/**
 * 現在のシステムのタイムゾーンを取得
 * @returns システムのタイムゾーン名
 */
export const getSystemTimezone = () => dayjs.tz.guess();

/**
 * 日時をフォーマットして表示
 * @param date 日時（DateまたはISO文字列）
 * @param format フォーマット文字列（例: 'YYYY-MM-DD HH:mm:ss'）
 * @param timezone タイムゾーン（省略時はUTC）
 * @returns フォーマットされた日時文字列
 */
export const formatDateTime = (
  date: Date | string,
  format: string = 'YYYY-MM-DD HH:mm:ss',
  timezone?: string,
) => {
  const dayjsDate = dayjs(date);
  if (timezone) {
    return dayjsDate.tz(timezone).format(format);
  }
  return dayjsDate.utc().format(format);
};
