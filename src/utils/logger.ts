import { config } from '@/config';

export interface LogContext {
  [key: string]: unknown;
}

class Logger {
  private logLevel: string;

  constructor() {
    this.logLevel = config.LOG_LEVEL;
  }

  private formatMessage(level: string, message: string, context?: LogContext): string {
    const timestamp = new Date().toISOString();
    const baseLog = { timestamp, level, message };

    if (context) {
      return JSON.stringify({ ...baseLog, ...context });
    }

    return JSON.stringify(baseLog);
  }

  debug(message: string, context?: LogContext): void {
    if (this.shouldLog('debug')) {
      console.log(this.formatMessage('DEBUG', message, context));
    }
  }

  info(message: string, context?: LogContext): void {
    if (this.shouldLog('info')) {
      console.log(this.formatMessage('INFO', message, context));
    }
  }

  warn(message: string, context?: LogContext): void {
    if (this.shouldLog('warn')) {
      console.warn(this.formatMessage('WARN', message, context));
    }
  }

  error(message: string, context?: LogContext): void {
    if (this.shouldLog('error')) {
      console.error(this.formatMessage('ERROR', message, context));
    }
  }

  private shouldLog(level: string): boolean {
    const levels = ['debug', 'info', 'warn', 'error'];
    const currentLevelIndex = levels.indexOf(this.logLevel.toLowerCase());
    const messageLevelIndex = levels.indexOf(level.toLowerCase());

    return messageLevelIndex >= currentLevelIndex;
  }
}

export const logger = new Logger();
