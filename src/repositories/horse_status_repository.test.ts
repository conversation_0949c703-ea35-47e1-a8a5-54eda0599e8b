import { HorseFactory } from '@test/factories/horse_factory';
import { HorseStatusFactory } from '@test/factories/horse_status_factory';
import { StableTmOutsideFarmFactory } from '@test/factories/stable_tm_outside_farm_factory';
import { describe, expect, it } from 'vitest';
import { upsertHorseStatus } from './horse_status_repository';

describe('horse_status_repository', () => {
  describe('upsertHorseStatus', () => {
    it('新しいレコードを正常に作成できる', async () => {
      // Arrange
      const horse = await HorseFactory.create();
      const input = {
        horseId: horse.horseId,
        inStable: true,
        outsideFarmId: null,
        latestStableInDate: new Date('2024-01-01'),
        latestStableOutDate: null,
      };

      // Act
      const result = await upsertHorseStatus(input);

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.horseId).toBe(input.horseId);
        expect(result.value.inStable).toBe(input.inStable);
        expect(result.value.outsideFarmId).toBe(input.outsideFarmId);
        expect(result.value.latestStableInDate).toEqual(input.latestStableInDate);
        expect(result.value.latestStableOutDate).toBe(input.latestStableOutDate);
      }
    });

    it('既存のレコードを正常に更新できる', async () => {
      // 初期データを作成
      const horseStatus = await HorseStatusFactory.create({
        inStable: true,
        latestStableInDate: new Date('2024-01-01'),
        latestStableOutDate: null,
      });
      const outsideFarm = await StableTmOutsideFarmFactory.create();

      // 更新データ
      const updateData = {
        horseId: horseStatus.horseId,
        inStable: false,
        outsideFarmId: outsideFarm.outsideFarmId,
        latestStableInDate: new Date('2024-02-01'),
        latestStableOutDate: new Date('2024-01-15'),
      };

      // Act
      const result = await upsertHorseStatus(updateData);

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.horseId).toBe(updateData.horseId);
        expect(result.value.inStable).toBe(updateData.inStable);
        expect(result.value.outsideFarmId).toEqual(updateData.outsideFarmId);
        expect(result.value.latestStableInDate).toEqual(updateData.latestStableInDate);
        expect(result.value.latestStableOutDate).toEqual(updateData.latestStableOutDate);
      }
    });

    it('部分的な更新が正常に動作する', async () => {
      // Arrange
      const horseStatus = await HorseStatusFactory.create({
        inStable: true,
        latestStableInDate: new Date('2024-01-01'),
        latestStableOutDate: null,
      });

      // 部分的な更新データ（一部のフィールドのみ）
      const partialUpdateData = {
        horseId: horseStatus.horseId,
        inStable: false,
        // 他のフィールドは undefined のまま
      };

      // Act
      const result = await upsertHorseStatus(partialUpdateData);

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.horseId).toBe(partialUpdateData.horseId);
        expect(result.value.inStable).toBe(partialUpdateData.inStable);
        // 他のフィールドは初期値のまま
        expect(result.value.outsideFarmId).toBe(horseStatus.outsideFarmId);
        expect(result.value.latestStableInDate).toEqual(horseStatus.latestStableInDate);
        expect(result.value.latestStableOutDate).toBe(horseStatus.latestStableOutDate);
      }
    });

    it('null値での更新が正常に動作する', async () => {
      // Arrange
      const initialData = {
        inStable: true,
        latestStableInDate: new Date('2024-01-01'),
        latestStableOutDate: new Date('2024-01-15'),
      };

      // 初期データを作成
      const outsideFarm = await StableTmOutsideFarmFactory.create();

      const horseStatus = await HorseStatusFactory.create({
        inStable: initialData.inStable,
        outsideFarm: {
          connect: {
            outsideFarmId: outsideFarm.outsideFarmId,
          },
        },
        latestStableInDate: initialData.latestStableInDate,
        latestStableOutDate: initialData.latestStableOutDate,
      });

      // null値での更新
      const nullUpdateData = {
        horseId: horseStatus.horseId,
        inStable: false,
        outsideFarmId: null,
        latestStableInDate: null,
        latestStableOutDate: null,
      };

      // Act
      const result = await upsertHorseStatus(nullUpdateData);

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.horseId).toBe(nullUpdateData.horseId);
        expect(result.value.inStable).toBe(nullUpdateData.inStable);
        expect(result.value.outsideFarmId).toBe(nullUpdateData.outsideFarmId);
        expect(result.value.latestStableInDate).toBe(nullUpdateData.latestStableInDate);
        expect(result.value.latestStableOutDate).toBe(nullUpdateData.latestStableOutDate);
      }
    });

    it('データベースエラーが発生した場合、DatabaseErrorを返す', async () => {
      // Arrange
      const invalidInput = {
        horseId: BigInt(-1), // 無効なID
        inStable: true,
        outsideFarmId: null,
        latestStableInDate: new Date('2024-01-01'),
        latestStableOutDate: null,
      };

      // Act
      const result = await upsertHorseStatus(invalidInput);

      // Assert
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.name).toBe('DatabaseError');
        expect(result.error.message).toContain('Failed to upsert horse status');
      }
    });

    it('同じhorseIdで複数回upsertしても正常に動作する', async () => {
      // Arrange
      const horse = await HorseFactory.create();
      const outsideFarm = await StableTmOutsideFarmFactory.create();
      const firstData = {
        horseId: horse.horseId,
        inStable: true,
        outsideFarmId: null,
        latestStableInDate: new Date('2024-01-01'),
        latestStableOutDate: null,
      };

      const secondData = {
        horseId: horse.horseId,
        inStable: false,
        outsideFarmId: outsideFarm.outsideFarmId,
        latestStableInDate: new Date('2024-02-01'),
        latestStableOutDate: new Date('2024-01-15'),
      };

      // Act
      const firstResult = await upsertHorseStatus(firstData);
      const secondResult = await upsertHorseStatus(secondData);

      // Assert
      expect(firstResult.isOk()).toBe(true);
      expect(secondResult.isOk()).toBe(true);

      if (firstResult.isOk() && secondResult.isOk()) {
        // 最初の結果は作成されたデータ
        expect(firstResult.value.horseId).toBe(firstData.horseId);
        expect(firstResult.value.inStable).toBe(firstData.inStable);

        // 2番目の結果は更新されたデータ
        expect(secondResult.value.horseId).toBe(secondData.horseId);
        expect(secondResult.value.inStable).toBe(secondData.inStable);
        expect(secondResult.value.outsideFarmId).toEqual(secondData.outsideFarmId);
      }
    });
  });
});
