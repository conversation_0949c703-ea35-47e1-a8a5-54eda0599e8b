import type { Batch<PERSON><PERSON>, BatchStatus } from '@prisma/client';
import { ResultAsync } from 'neverthrow';
import { parse, v7 as uuidv7 } from 'uuid';
import { client as prisma } from '@/repositories/client';
import { DatabaseError } from '@/repositories/database_error';
import type { RepositoryFunction as RF } from '@/repositories/repository';

export const getBatchJobsByStatus: RF<{ status: BatchStatus }, BatchJob[]> = ({ status }, tx) => {
  const client = tx || prisma;

  const result = ResultAsync.fromPromise(
    client.batchJob.findMany({
      where: {
        status,
      },
      orderBy: [{ createdAt: 'desc' }],
    }),
    error => {
      const message = error instanceof Error ? error.message : 'Unknown database error';
      return new DatabaseError(`Failed to fetch batch jobs by status: ${message}`);
    },
  );
  return result;
};

export const getBatchJobsByName: RF<{ name: string }, Batch<PERSON>ob[]> = ({ name }, tx) => {
  const client = tx || prisma;

  const result = ResultAsync.fromPromise(
    client.batchJob.findMany({
      where: {
        name,
      },
      orderBy: [{ createdAt: 'desc' }],
    }),
    error => {
      const message = error instanceof Error ? error.message : 'Unknown database error';
      return new DatabaseError(`Failed to fetch batch jobs by name: ${message}`);
    },
  );
  return result;
};

export const getBatchJobById: RF<{ batchJobId: Uint8Array }, BatchJob | null> = (
  { batchJobId },
  tx,
) => {
  const client = tx || prisma;

  const result = ResultAsync.fromPromise(
    client.batchJob.findUnique({
      where: {
        batchJobId,
      },
    }),
    error => {
      const message = error instanceof Error ? error.message : 'Unknown database error';
      return new DatabaseError(`Failed to fetch batch job by id: ${message}`);
    },
  );
  return result;
};

export const createBatchJob: RF<
  {
    name: string;
    description?: string;
    status?: BatchStatus;
    metadata?: string;
    startedAt?: Date;
  },
  BatchJob
> = ({ name, description, status = 'PENDING', metadata, startedAt }, tx) => {
  const client = tx || prisma;

  const result = ResultAsync.fromPromise(
    client.batchJob.create({
      data: {
        batchJobId: parse(uuidv7()),
        name,
        description,
        status,
        metadata,
        startedAt,
      },
    }),
    error => {
      const message = error instanceof Error ? error.message : 'Unknown database error';
      return new DatabaseError(`Failed to create batch job: ${message}`);
    },
  );
  return result;
};

export const updateBatchJobStatus: RF<
  {
    batchJobId: Uint8Array;
    status: BatchStatus;
    startedAt?: Date;
    completedAt?: Date;
    errorMessage?: string;
  },
  BatchJob
> = ({ batchJobId, status, startedAt, completedAt, errorMessage }, tx) => {
  const client = tx || prisma;

  const updateData: Record<string, unknown> = {
    status,
  };

  if (startedAt) {
    updateData.startedAt = startedAt;
  }

  if (completedAt) {
    updateData.completedAt = completedAt;
  }

  if (errorMessage) {
    updateData.errorMessage = errorMessage;
  }

  const result = ResultAsync.fromPromise(
    client.batchJob.update({
      where: {
        batchJobId,
      },
      data: updateData,
    }),
    error => {
      const message = error instanceof Error ? error.message : 'Unknown database error';
      return new DatabaseError(`Failed to update batch job status: ${message}`);
    },
  );
  return result;
};

export const getRunningBatchJobs: RF<object, BatchJob[]> = (_, tx) => {
  const client = tx || prisma;

  const result = ResultAsync.fromPromise(
    client.batchJob.findMany({
      where: {
        status: 'RUNNING',
      },
      orderBy: [{ startedAt: 'desc' }],
    }),
    error => {
      const message = error instanceof Error ? error.message : 'Unknown database error';
      return new DatabaseError(`Failed to fetch running batch jobs: ${message}`);
    },
  );
  return result;
};

export const getFailedBatchJobs: RF<{ days?: number }, BatchJob[]> = ({ days = 7 }, tx) => {
  const client = tx || prisma;

  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - days);

  const result = ResultAsync.fromPromise(
    client.batchJob.findMany({
      where: {
        status: 'FAILED',
        createdAt: {
          gte: cutoffDate,
        },
      },
      orderBy: [{ createdAt: 'desc' }],
    }),
    error => {
      const message = error instanceof Error ? error.message : 'Unknown database error';
      return new DatabaseError(`Failed to fetch failed batch jobs: ${message}`);
    },
  );
  return result;
};

export const deleteBatchJob: RF<{ batchJobId: Uint8Array }, BatchJob> = ({ batchJobId }, tx) => {
  const client = tx || prisma;

  const result = ResultAsync.fromPromise(
    client.batchJob.delete({
      where: {
        batchJobId,
      },
    }),
    error => {
      const message = error instanceof Error ? error.message : 'Unknown database error';
      return new DatabaseError(`Failed to delete batch job: ${message}`);
    },
  );
  return result;
};
