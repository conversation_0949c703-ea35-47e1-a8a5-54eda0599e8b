import type { HorseStatus } from '@prisma/client';
import { ResultAsync } from 'neverthrow';
import { parse, v7 as uuidv7 } from 'uuid';
import { client as prisma } from '@/repositories/client';
import type { RepositoryFunction as RF } from '@/repositories/repository';
import { DatabaseError } from './database_error';

interface UpsertHorseStatusInput {
  horseId: bigint;
  inStable: boolean;
  outsideFarmId?: Uint8Array | null;
  latestStableInDate?: Date | null;
  latestStableOutDate?: Date | null;
}

export const upsertHorseStatus: RF<UpsertHorseStatusInput, HorseStatus> = (input, tx) => {
  const client = tx ?? prisma;
  const { horseId, inStable, outsideFarmId, latestStableInDate, latestStableOutDate } = input;
  return ResultAsync.fromPromise(
    client.horseStatus.upsert({
      where: {
        horseId,
      },
      create: {
        horseStatusId: parse(uuidv7()),
        horseId,
        inStable,
        outsideFarmId,
        latestStableInDate,
        latestStableOutDate,
      },
      update: {
        inStable,
        outsideFarmId,
        latestStableInDate,
        latestStableOutDate,
      },
    }),
    error => {
      const message = error instanceof Error ? error.message : 'Unknown database error';
      return new DatabaseError(`Failed to upsert horse status: ${message}`);
    },
  );
};
