import type { BatchStatus } from '@prisma/client';
import { BatchJobFactory } from '@test/factories/batch_job_factory';
import { describe, expect, it } from 'vitest';
import { createBatchJob, getBatchJobsByStatus, updateBatchJobStatus } from './batch_job_repository';

describe('batch_job_repository', () => {
  describe('getBatchJobsByStatus', () => {
    it('指定されたステータスのバッチジョブを正常に取得できる', async () => {
      // Arrange
      await BatchJobFactory.create({
        name: 'test-job-1',
        status: 'COMPLETED',
      });

      await BatchJobFactory.create({
        name: 'test-job-2',
        status: 'COMPLETED',
      });

      // Act
      const result = await getBatchJobsByStatus({ status: 'COMPLETED' });

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toHaveLength(2);
        expect(result.value.every(job => job.status === 'COMPLETED')).toBe(true);
      }
    });
  });

  describe('createBatchJob', () => {
    it('新しいバッチジョブを正常に作成できる', async () => {
      // Arrange
      const jobData = {
        name: 'new-test-job',
        description: 'Test description',
        status: 'PENDING' as BatchStatus,
        metadata: JSON.stringify({ test: true }),
      };

      // Act
      const result = await createBatchJob(jobData);

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.name).toBe(jobData.name);
        expect(result.value.description).toBe(jobData.description);
        expect(result.value.status).toBe(jobData.status);
        expect(result.value.batchJobId).toBeDefined();
      }
    });
  });

  describe('updateBatchJobStatus', () => {
    it('バッチジョブのステータスを正常に更新できる', async () => {
      // Arrange
      const createdJob = await BatchJobFactory.create({
        name: 'job-to-update',
        status: 'PENDING',
      });

      const updateData = {
        batchJobId: createdJob.batchJobId,
        status: 'RUNNING' as BatchStatus,
        startedAt: new Date(),
      };

      // Act
      const result = await updateBatchJobStatus(updateData);

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toEqual(
          expect.objectContaining({
            batchJobId: createdJob.batchJobId,
            status: 'RUNNING',
            startedAt: expect.any(Date),
          }),
        );
      }
    });
  });
});
