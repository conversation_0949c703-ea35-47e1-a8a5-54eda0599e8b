import { TransportDailyRecordFactory } from '@test/factories/stable_tm_transport_daily_record_factory';
import { describe, expect, it } from 'vitest';
import {
  confirmTransportDailyRecords,
  getUnconfirmedRecordsBeforeDate,
} from './stable_tm_transport_daily_record_repository';

describe('stable_transport_daily_record_repository', () => {
  describe('getUnconfirmedRecordsBeforeDate', () => {
    it('指定された日付以前の未確認レコードを正常に取得できる', async () => {
      // Arrange
      const testDate = new Date('2024-12-31');

      // ファクトリーを使用してテストデータを作成
      await TransportDailyRecordFactory.create({
        year: 2024,
        month: 12,
        day: 30,
        isConfirmed: false,
      });

      await TransportDailyRecordFactory.create({
        year: 2024,
        month: 11,
        day: 15,
        isConfirmed: false,
      });

      // Act
      const result = await getUnconfirmedRecordsBeforeDate({ date: testDate });

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toHaveLength(2);

        // 日付順でソートされているかチェック
        const sortedResults = result.value.sort((a, b) => {
          if (a.year !== b.year) return a.year - b.year;
          if (a.month !== b.month) return a.month - b.month;
          return a.day - b.day;
        });

        expect(sortedResults[0]?.year).toBe(2024);
        expect(sortedResults[0]?.month).toBe(11);
        expect(sortedResults[0]?.day).toBe(15);
        expect(sortedResults[0]?.isConfirmed).toBe(false);

        expect(sortedResults[1]?.year).toBe(2024);
        expect(sortedResults[1]?.month).toBe(12);
        expect(sortedResults[1]?.day).toBe(30);
        expect(sortedResults[1]?.isConfirmed).toBe(false);
      }
    });

    it('レコードが存在しない場合は空配列を返す', async () => {
      // Arrange
      const testDate = new Date('2024-01-01');
      // 指定日付以前のレコードは作成しない

      // Act
      const result = await getUnconfirmedRecordsBeforeDate({ date: testDate });

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toHaveLength(0);
      }
    });

    it('確認済みレコードは取得されない', async () => {
      // Arrange
      const testDate = new Date('2024-12-31');

      // 確認済みレコードを作成
      await TransportDailyRecordFactory.create({
        year: 2024,
        month: 12,
        day: 30,
        isConfirmed: true, // 確認済み
      });

      // 未確認レコードを作成
      await TransportDailyRecordFactory.create({
        year: 2024,
        month: 12,
        day: 29,
        isConfirmed: false, // 未確認
      });

      // Act
      const result = await getUnconfirmedRecordsBeforeDate({ date: testDate });

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toHaveLength(1);
        expect(result.value[0]?.isConfirmed).toBe(false);
        expect(result.value[0]?.day).toBe(29);
      }
    });

    it('年をまたぐ日付の検索が正しく動作する', async () => {
      // Arrange
      const testDate = new Date('2025-01-15');

      // 2024年のレコードを作成（検索対象）
      await TransportDailyRecordFactory.create({
        year: 2024,
        month: 12,
        day: 31,
        isConfirmed: false,
      });

      // 2025年1月15日以降のレコードを作成（検索対象外）
      await TransportDailyRecordFactory.create({
        year: 2025,
        month: 1,
        day: 15,
        isConfirmed: false,
      });

      // Act
      const result = await getUnconfirmedRecordsBeforeDate({ date: testDate });

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toHaveLength(1);
        expect(result.value[0]?.year).toBe(2024);
        expect(result.value[0]?.month).toBe(12);
        expect(result.value[0]?.day).toBe(31);
      }
    });

    it('月をまたぐ日付の検索が正しく動作する', async () => {
      // Arrange
      const testDate = new Date('2024-03-01');

      // 2月のレコードを作成（検索対象）
      await TransportDailyRecordFactory.create({
        year: 2024,
        month: 2,
        day: 29,
        isConfirmed: false,
      });

      // 3月1日以降のレコードを作成（検索対象外）
      await TransportDailyRecordFactory.create({
        year: 2024,
        month: 3,
        day: 1,
        isConfirmed: false,
      });

      // Act
      const result = await getUnconfirmedRecordsBeforeDate({ date: testDate });

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toHaveLength(1);
        expect(result.value[0]?.year).toBe(2024);
        expect(result.value[0]?.month).toBe(2);
        expect(result.value[0]?.day).toBe(29);
      }
    });
  });

  describe('confirmTransportDailyRecords', () => {
    it('指定されたIDのレコードを正常に確認済みに更新できる', async () => {
      // Arrange
      const record1 = await TransportDailyRecordFactory.create({
        year: 2024,
        month: 12,
        day: 30,
        isConfirmed: false,
      });

      const record2 = await TransportDailyRecordFactory.create({
        year: 2024,
        month: 12,
        day: 29,
        isConfirmed: false,
      });

      const transportDailyRecordIds = [
        record1.transportDailyRecordId,
        record2.transportDailyRecordId,
      ];

      // Act
      const result = await confirmTransportDailyRecords({ transportDailyRecordIds });

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.count).toBe(2);
      }
    });

    it('存在しないIDを指定した場合は0件更新される', async () => {
      // Arrange
      const nonExistentId = Buffer.alloc(16);
      const transportDailyRecordIds = [nonExistentId];

      // Act
      const result = await confirmTransportDailyRecords({ transportDailyRecordIds });

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.count).toBe(0);
      }
    });

    it('空の配列を指定した場合は0件更新される', async () => {
      // Arrange
      const transportDailyRecordIds: Uint8Array[] = [];

      // Act
      const result = await confirmTransportDailyRecords({ transportDailyRecordIds });

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.count).toBe(0);
      }
    });
  });
});
