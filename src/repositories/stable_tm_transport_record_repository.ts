import type {
  StableTmOutsideFarm,
  StableTmTransportDailyRecord,
  StableTmTransportInStatus,
  StableTmTransportOutHandoverNote,
  StableTmTransportOutStatus,
  StableTmTransportRecord,
  Staff,
} from '@prisma/client';
import { ResultAsync } from 'neverthrow';
import { client as prisma } from '@/repositories/client';
import { DatabaseError } from '@/repositories/database_error';
import type { HorsesWithStableHistories } from '@/repositories/horse_repository';
import type { RepositoryFunction as RF } from '@/repositories/repository';

export type HorseTransportRecord = StableTmTransportRecord & {
  horse: HorsesWithStableHistories;
  transportDailyRecord: StableTmTransportDailyRecord | null;
  transportInStatus:
    | (StableTmTransportInStatus & {
        staff: Staff | null;
      })
    | null;
  transportOutStatus:
    | (StableTmTransportOutStatus & {
        transportOutHandoverNotes: StableTmTransportOutHandoverNote[];
        farm: StableTmOutsideFarm | null;
        staff: Staff | null;
      })
    | null;
};

interface FetchHorseTransportRecordsInput {
  transportDailyRecordIds: Uint8Array[];
}

export const fetchHorseTransportRecords: RF<
  FetchHorseTransportRecordsInput,
  HorseTransportRecord[]
> = (input, tx) => {
  const client = tx ?? prisma;
  const { transportDailyRecordIds } = input;

  return ResultAsync.fromPromise(
    client.stableTmTransportRecord.findMany({
      where: {
        transportDailyRecordId: { in: transportDailyRecordIds },
      },
      include: {
        horse: {
          include: {
            horseStableHistories: {
              orderBy: {
                createdAt: 'desc',
              },
            },
            horseStatus: {
              include: {
                outsideFarm: {
                  include: {
                    farmArea: true,
                  },
                },
              },
            },
          },
        },
        transportDailyRecord: true,
        transportInStatus: {
          include: {
            staff: true,
          },
        },
        transportOutStatus: {
          include: {
            transportOutHandoverNotes: true,
            farm: {
              include: {
                farmArea: true,
              },
            },
            staff: true,
          },
        },
      },
      orderBy: [
        {
          transportDailyRecordId: 'asc',
        },
        {
          index: 'asc',
        },
      ],
    }),
    error => {
      const message = error instanceof Error ? error.message : 'Unknown database error';
      return new DatabaseError(`Failed to fetch horse transport records: ${message}`);
    },
  );
};
