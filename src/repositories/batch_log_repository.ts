import type { BatchLog, LogLevel } from '@prisma/client';
import { ResultAsync } from 'neverthrow';
import { parse, v7 as uuidv7 } from 'uuid';
import { client as prisma } from '@/repositories/client';
import { DatabaseError } from '@/repositories/database_error';
import type { RepositoryFunction as RF } from '@/repositories/repository';

export const createBatchJobLog: RF<
  {
    batchJobId: Uint8Array;
    message: string;
    level: LogLevel;
    metadata?: string;
  },
  BatchLog
> = ({ batchJobId, message, level, metadata }, tx) => {
  const client = tx || prisma;

  const result = ResultAsync.fromPromise(
    client.batchLog.create({
      data: {
        batchLogId: parse(uuidv7()),
        batchJobId,
        message,
        level,
        metadata,
      },
    }),
    error => {
      const message = error instanceof Error ? error.message : 'Unknown database error';
      return new DatabaseError(`Failed to create batch log: ${message}`);
    },
  );
  return result;
};
