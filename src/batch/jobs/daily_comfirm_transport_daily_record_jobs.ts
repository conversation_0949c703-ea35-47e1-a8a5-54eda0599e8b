import { logger } from '@/utils/logger';
import { getLocalTimeToUTCDate } from '@/utils/timezone';

export const dailyConfirmTransportDailyRecordJob = async (): Promise<void> => {
  logger.info('Executing daily confirm transport daily record job...');
  const utcDate = getLocalTimeToUTCDate(new Date().toISOString(), 'Asia/Tokyo');
  logger.info(`UTC Date: ${utcDate}`);
  logger.info('Daily confirm transport daily record job completed');
};
