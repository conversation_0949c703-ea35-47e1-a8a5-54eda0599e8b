import { client as prisma } from '@/repositories/client';
import { upsertHorseStatus } from '@/repositories/horse_status_repository';
import {
  confirmTransportDailyRecords,
  getUnconfirmedRecordsBeforeDate,
} from '@/repositories/stable_tm_transport_daily_record_repository';
import { fetchHorseTransportRecords } from '@/repositories/stable_tm_transport_record_repository';
import { logger } from '@/utils/logger';
import { getLocalTimeToUTCDate } from '@/utils/timezone';

export const dailyConfirmTransportDailyRecordJob = async (): Promise<void> => {
  logger.info('Executing daily confirm transport daily record job...');

  try {
    // JST 00:10に実行されるため、現在の日付（JST）を取得
    const now = new Date();
    const jstDate = getLocalTimeToUTCDate(now.toISOString(), 'Asia/Tokyo');
    logger.info(`Current JST date: ${jstDate}`);

    // 前日以前の未確認レコードを取得
    const unconfirmedRecordsResult = await getUnconfirmedRecordsBeforeDate({ date: jstDate });
    if (unconfirmedRecordsResult.isErr()) {
      throw unconfirmedRecordsResult.error;
    }

    const unconfirmedRecords = unconfirmedRecordsResult.value;
    logger.info(`Found ${unconfirmedRecords.length} unconfirmed transport daily records`);

    if (unconfirmedRecords.length === 0) {
      logger.info('No unconfirmed records found. Job completed.');
      return;
    }

    // トランザクション内で処理を実行
    await prisma.$transaction(async tx => {
      // 1. 未確認レコードをすべて確認済みに更新
      const transportDailyRecordIds = unconfirmedRecords.map(
        record => record.transportDailyRecordId,
      );
      const confirmResult = await confirmTransportDailyRecords({ transportDailyRecordIds }, tx);
      if (confirmResult.isErr()) {
        throw confirmResult.error;
      }

      logger.info(`Confirmed ${confirmResult.value.count} transport daily records`);

      // 2. 対象のtransportRecordを取得
      const horseTransportRecordsResult = await fetchHorseTransportRecords(
        { transportDailyRecordIds },
        tx,
      );
      if (horseTransportRecordsResult.isErr()) {
        throw horseTransportRecordsResult.error;
      }

      const horseTransportRecords = horseTransportRecordsResult.value;
      logger.info(`Found ${horseTransportRecords.length} horse transport records`);

      // 3. 馬IDでグループ化（最新のレコードのみ保持）
      const groupByHorseId = Array.from(
        horseTransportRecords
          .reduce((map, item) => {
            map.set(item.horseId.toString(), item);
            return map;
          }, new Map<string, (typeof horseTransportRecords)[0]>())
          .values(),
      );

      logger.info(`Processing ${groupByHorseId.length} unique horses`);

      // 4. 各馬のhorseStatusを更新
      await Promise.all(
        groupByHorseId.map(async record => {
          if (!record.transportDailyRecord) {
            logger.warn(`Transport daily record not found for horse ${record.horseId}`);
            return;
          }

          const latestStableChangeDate = new Date(
            record.transportDailyRecord.year,
            record.transportDailyRecord.month - 1,
            record.transportDailyRecord.day,
          );

          if (record.type === 'in') {
            const result = await upsertHorseStatus(
              {
                horseId: record.horseId,
                inStable: true,
                outsideFarmId: null,
                latestStableInDate: latestStableChangeDate,
              },
              tx,
            );
            if (result.isErr()) {
              throw result.error;
            }
            logger.info(`Updated horse status for horse ${record.horseId}: in stable`);
          } else if (record.type === 'out') {
            const result = await upsertHorseStatus(
              {
                horseId: record.horseId,
                inStable: false,
                outsideFarmId: record.transportOutStatus?.farmId || null,
                latestStableOutDate: latestStableChangeDate,
              },
              tx,
            );
            if (result.isErr()) {
              throw result.error;
            }
            logger.info(
              `Updated horse status for horse ${record.horseId}: out of stable (farm: ${record.transportOutStatus?.farmId || 'none'})`,
            );
          } else {
            logger.warn(`Unknown transport type: ${record.type} for horse ${record.horseId}`);
          }
        }),
      );
    });

    logger.info('Daily confirm transport daily record job completed successfully');
  } catch (error) {
    logger.error('Daily confirm transport daily record job failed', { error });
    throw error;
  }
};
