import type { Bat<PERSON><PERSON><PERSON> } from '@prisma/client';
import { createBatchJob, updateBatchJobStatus } from '@/repositories/batch_job_repository';
import { createBatchJobLog } from '@/repositories/batch_log_repository';
import { logger } from '@/utils/logger';
import { testJob } from './jobs/test_job';

// Available jobs registry
const jobs: Record<string, () => Promise<void>> = {
  test: testJob,
  // Add more jobs here
};

export const executeJob = async (jobName: string): Promise<void> => {
  const job = jobs[jobName];
  if (!job) {
    throw new Error(`Unknown job: ${jobName}. Available jobs: ${Object.keys(jobs).join(', ')}`);
  }

  const startTime = Date.now();
  let batchJobRecord: BatchJob | undefined;

  try {
    // Create batch job record
    const result = await createBatchJob({
      name: jobName,
      description: `Batch job: ${jobName}`,
      status: 'RUNNING',
      startedAt: new Date(),
    });
    if (result.isErr()) {
      throw result.error;
    }
    batchJobRecord = result.value;

    // Create batch job log record
    await createBatchJobLog({
      batchJobId: batchJobRecord.batchJobId,
      message: `Starting job: ${jobName}`,
      level: 'INFO',
      metadata: JSON.stringify({ jobId: batchJobRecord.batchJobId }),
    });

    logger.info(`Starting job: ${jobName}`, { jobId: batchJobRecord.batchJobId });

    // Execute the job
    await job();

    // Update job status to completed
    await updateBatchJobStatus({
      batchJobId: batchJobRecord.batchJobId,
      status: 'COMPLETED',
      completedAt: new Date(),
    });
    if (result.isErr()) {
      throw result.error;
    }
    await createBatchJobLog({
      batchJobId: batchJobRecord.batchJobId,
      message: `Job completed successfully: ${jobName}`,
      level: 'INFO',
      metadata: JSON.stringify({ jobId: batchJobRecord.batchJobId }),
    });

    const duration = Date.now() - startTime;
    logger.info(`Job completed successfully: ${jobName}`, {
      jobId: batchJobRecord.batchJobId,
      duration: `${duration}ms`,
    });
  } catch (error) {
    const duration = Date.now() - startTime;
    logger.error(`Job failed: ${jobName}`, {
      jobId: batchJobRecord?.batchJobId,
      duration: `${duration}ms`,
      error,
    });

    // Update job status to failed
    if (batchJobRecord) {
      await updateBatchJobStatus({
        batchJobId: batchJobRecord.batchJobId,
        status: 'FAILED',
        completedAt: new Date(),
        errorMessage: error instanceof Error ? error.message : String(error),
      });
    }

    throw error;
  }
};
