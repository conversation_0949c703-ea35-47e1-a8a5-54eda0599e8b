import { config as dotenvConfig } from 'dotenv';

// Load environment variables from .env file
dotenvConfig();

export interface Config {
  NODE_ENV: string;
  DATABASE_URL: string;
  LOG_LEVEL: string;
  PRISMA_TRANSACTION_TIMEOUT: number;
}

export const config: Config = {
  NODE_ENV: process.env.NODE_ENV || 'development',
  DATABASE_URL: process.env.DATABASE_URL || '',
  LOG_LEVEL: process.env.LOG_LEVEL || 'info',
  PRISMA_TRANSACTION_TIMEOUT: Number.parseInt(process.env.PRISMA_TRANSACTION_TIMEOUT || '10000'),
};

// Validate required environment variables
if (!config.DATABASE_URL) {
  throw new Error('DATABASE_URL environment variable is required');
}
