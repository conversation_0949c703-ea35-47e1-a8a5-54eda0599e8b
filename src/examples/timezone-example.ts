import {
  convertTimezoneToUTC,
  convertUTCToTimezone,
  formatDateTime,
  getAvailableTimezones,
  getCurrentTimeInTimezone,
  getCurrentUTCDate,
  getCurrentUTCTime,
  getSystemTimezone,
} from '../utils/timezone';

/**
 * dayjsのタイムゾーン機能の使用例
 */
export function demonstrateTimezoneFeatures(): void {
  console.log('=== dayjs タイムゾーン機能のデモ ===\n');

  // 1. UTCの現在時刻を取得
  console.log('1. UTCの現在時刻:');
  console.log(`   ISO文字列: ${getCurrentUTCTime()}`);
  console.log(`   Date形式: ${getCurrentUTCDate()}`);
  console.log();

  // 2. システムのタイムゾーンを取得
  console.log('2. システムのタイムゾーン:');
  console.log(`   ${getSystemTimezone()}`);
  console.log();

  // 3. 様々なタイムゾーンの現在時刻を取得
  console.log('3. 様々なタイムゾーンの現在時刻:');
  const timezones = ['Asia/Tokyo', 'America/New_York', 'Europe/London', 'Australia/Sydney'];

  timezones.forEach(tz => {
    const time = getCurrentTimeInTimezone(tz);
    console.log(`   ${tz}: ${time}`);
  });
  console.log();

  // 4. UTC時刻を他のタイムゾーンに変換
  console.log('4. UTC時刻を他のタイムゾーンに変換:');
  const utcTime = getCurrentUTCTime();
  console.log(`   元のUTC時刻: ${utcTime}`);

  timezones.forEach(tz => {
    const converted = convertUTCToTimezone(utcTime, tz);
    console.log(`   ${tz}: ${converted}`);
  });
  console.log();

  // 5. ローカル時刻をUTCに変換
  console.log('5. ローカル時刻をUTCに変換:');
  const tokyoTime = getCurrentTimeInTimezone('Asia/Tokyo');
  console.log(`   東京時刻: ${tokyoTime}`);
  const convertedToUTC = convertTimezoneToUTC(tokyoTime, 'Asia/Tokyo');
  console.log(`   UTCに変換: ${convertedToUTC}`);
  console.log();

  // 6. 日時フォーマット
  console.log('6. 日時フォーマット:');
  const now = new Date();
  console.log(`   デフォルト形式: ${formatDateTime(now)}`);
  console.log(`   カスタム形式: ${formatDateTime(now, 'YYYY年MM月DD日 HH時mm分ss秒')}`);
  console.log(`   東京時間: ${formatDateTime(now, 'YYYY-MM-DD HH:mm:ss', 'Asia/Tokyo')}`);
  console.log(
    `   ニューヨーク時間: ${formatDateTime(now, 'MM/DD/YYYY h:mm A', 'America/New_York')}`,
  );
  console.log();

  // 7. 利用可能なタイムゾーンの一部を表示
  console.log('7. 利用可能なタイムゾーンの一部:');
  const availableTimezones = getAvailableTimezones();
  const sampleTimezones = availableTimezones.slice(0, 10);
  sampleTimezones.forEach(tz => {
    console.log(`   - ${tz}`);
  });
  console.log(`   ... 他 ${availableTimezones.length - 10} 個のタイムゾーン`);
  console.log();

  console.log('=== デモ完了 ===');
}

// デモを実行
if (require.main === module) {
  demonstrateTimezoneFeatures();
}
