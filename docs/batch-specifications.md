# バッチ処理仕様書

## 概要

Equtum Batch Processing System で実行される各バッチジョブの詳細仕様を定義します。

## バッチジョブ共通仕様

### 実行環境

- **ランタイム**: Node.js 20 (ECS Fargate)
- **メモリ**: 4GB (設定可能)
- **CPU**: 1 vCPU (設定可能)
- **タイムアウト**: 15 分 (設定可能)
- **ログレベル**: INFO (環境変数で変更可能)

### 共通処理フロー

```mermaid
flowchart TD
    A[ジョブ開始] --> B[BatchJob レコード作成]
    B --> C[STATUS: RUNNING に更新]
    C --> D[ビジネスロジック実行]
    D --> E{処理成功?}
    E -->|Yes| F[STATUS: COMPLETED に更新]
    E -->|No| G[STATUS: FAILED に更新]
    F --> H[ログ出力]
    G --> I[エラーログ出力]
    H --> J[ジョブ終了]
    I --> J
```

### エラーハンドリング

- **リトライ**: EventBridge Scheduler レベルで設定
- **ログ出力**: すべてのエラーを CloudWatch Logs に記録
- **通知**: 重要なエラーは SNS 経由で運用チームに通知
- **データ整合性**: トランザクション使用による原子性保証

### 監視・メトリクス

各バッチジョブは以下のメトリクスを記録：

- **実行時間**: 開始〜終了までの時間
- **成功率**: 成功 / 総実行回数
- **処理件数**: 対象データの件数
- **エラー率**: エラー / 総実行回数

## 現在実装されているジョブ

### 1. example-job（サンプルジョブ）

#### 概要

システムの動作確認とテスト用のサンプルバッチジョブです。

#### 処理内容

1. 1 秒間のスリープ処理（処理時間のシミュレーション）
2. バッチログへの処理記録
3. 正常終了

#### 実行条件

- **トリガー**: 手動実行のみ
- **実行時間**: 平均 1-2 秒
- **リソース要件**: 最小限

#### パラメータ

なし

#### 期待される結果

- BatchJob テーブルに実行履歴が記録される
- BatchLog テーブルに INFO レベルのログが記録される
- CloudWatch Logs に実行ログが出力される

#### コマンド例

```bash
# 開発環境
npm run dev example-job

# 本番環境
npm start example-job
```

---

### 2. data-cleanup（データクリーンアップジョブ）

#### 概要

古いバッチログを削除してデータベースの容量を最適化するジョブです。

#### 処理内容

1. 30 日以前の BatchLog レコードを特定
2. 対象レコードを一括削除
3. 削除件数をログに記録

#### 実行条件

- **推奨スケジュール**: 毎日深夜 2 時（JST）
- **実行時間**: データ量により変動（通常 1-5 分）
- **リソース要件**: 標準

#### パラメータ

- **保持期間**: 30 日（ハードコード、今後設定可能予定）

#### 期待される結果

- 30 日以前の BatchLog レコードが削除される
- 削除件数がログに記録される
- データベース容量の最適化

#### 注意事項

- 本番環境での実行は慎重に行う
- 削除前のバックアップ確認を推奨
- 大量削除時はパフォーマンスに注意

#### コマンド例

```bash
# 開発環境
npm run dev data-cleanup

# 本番環境
npm start data-cleanup
```

## バッチジョブ開発ガイド

### 新しいジョブの追加手順

1. **ジョブメソッドの実装**

   ```typescript
   private async newBatchJob(): Promise<void> {
     logger.info('新しいバッチジョブを開始');

     try {
       // ビジネスロジックの実装
       // ...

       logger.info('新しいバッチジョブが完了');
     } catch (error) {
       logger.error('新しいバッチジョブでエラーが発生', { error });
       throw error;
     }
   }
   ```

2. **ジョブの登録**

   ```typescript
   this.jobs = {
     'example-job': this.exampleJob.bind(this),
     'data-cleanup': this.dataCleanupJob.bind(this),
     'new-batch-job': this.newBatchJob.bind(this), // 追加
   };
   ```

3. **Terraform 設定の更新**

   ```hcl
   locals {
     batch_schedules = {
       new_batch_job = {
         name        = "new-batch-job"
         description = "新しいバッチジョブの説明"
         schedule    = "cron(0 2 * * ? *)"  # 毎日2時
         command     = ["npm", "start", "new-batch-job"]
       }
     }
   }
   ```

4. **テストの実装**
   - 単体テスト
   - 統合テスト
   - E2E テスト

### コーディング規約

#### エラーハンドリング

```typescript
private async sampleJob(): Promise<void> {
  const startTime = Date.now();

  try {
    // メイン処理
    await this.processData();

    logger.info('ジョブ完了', {
      duration: Date.now() - startTime
    });
  } catch (error) {
    logger.error('ジョブ失敗', {
      error: error instanceof Error ? error.message : String(error),
      duration: Date.now() - startTime
    });
    throw error; // 重要: エラーを再スロー
  }
}
```

#### ログ出力

```typescript
// 処理開始
logger.info('データ処理を開始', { targetDate: '2024-01-01' });

// 進捗状況
logger.info('データ処理中', { processed: 100, total: 500 });

// 処理完了
logger.info('データ処理完了', {
  processed: 500,
  duration: '120ms',
});

// エラー時
logger.error('データ処理エラー', {
  error: error.message,
  context: { userId: 123, action: 'update' },
});
```

#### データベースアクセス

```typescript
// トランザクションの使用
await this.prisma.$transaction(async tx => {
  await tx.dataTable.updateMany({
    where: { status: 'pending' },
    data: { status: 'processing' },
  });

  await tx.logTable.create({
    data: { message: 'バッチ処理開始' },
  });
});

// 大量データの処理（ページング）
const batchSize = 1000;
let offset = 0;
let hasMore = true;

while (hasMore) {
  const records = await this.prisma.dataTable.findMany({
    skip: offset,
    take: batchSize,
    where: { needsProcessing: true },
  });

  if (records.length === 0) {
    hasMore = false;
    break;
  }

  // レコードを処理
  await this.processRecords(records);

  offset += batchSize;
  logger.info(`処理済み: ${offset} 件`);
}
```

## 運用における注意事項

### パフォーマンス

- **メモリ使用量**: 大量データ処理時はストリーミング処理を検討
- **CPU 使用率**: 高負荷処理は適切な間隔を設けて実行
- **データベース負荷**: 大量更新時はバッチサイズを調整

### 監視

- **実行時間の監視**: 通常の 2 倍を超えた場合はアラート
- **エラー率の監視**: 10%を超えた場合は調査が必要
- **リソース使用量**: CPU/メモリが 80%を超えた場合は設定見直し

### 障害対応

1. **ジョブの停止**

   ```bash
   # ECS タスクの停止
   aws ecs stop-task --cluster <cluster-name> --task <task-arn>
   ```

2. **ログの確認**

   ```bash
   # CloudWatch Logs の確認
   aws logs get-log-events --log-group-name /ecs/equtum-batch
   ```

3. **手動実行**
   ```bash
   # 問題解決後の手動実行
   aws ecs run-task --cluster <cluster> --task-definition <task-def>
   ```

## 今後の拡張予定

### 短期（1-3 ヶ月）

- [ ] パラメータ化されたジョブ実行
- [ ] 依存関係のあるジョブチェーン
- [ ] 実行結果の通知機能

### 中期（3-6 ヶ月）

- [ ] 動的スケジューリング
- [ ] 負荷分散型並列処理
- [ ] 詳細なパフォーマンス分析

### 長期（6 ヶ月以上）

- [ ] ワークフロー管理機能
- [ ] 予測的スケーリング
- [ ] マルチリージョン対応
