# AWS 構成ガイド

## 概要

Equtum Batch Processing System で使用される AWS サービスの詳細構成と運用ガイドです。

## AWS サービス構成

### 1. Amazon ECS (Elastic Container Service)

#### クラスター構成

```hcl
# infra/dev/ecs.cluster.tf
resource "aws_ecs_cluster" "dev-equtum-batch" {
  name = "dev-equtum-batch"

  configuration {
    execute_command_configuration {
      kms_key_id = null
      logging    = "DEFAULT"
    }
  }

  setting {
    name  = "containerInsights"
    value = "enabled"
  }
}
```

#### タスク定義

- **起動タイプ**: FARGATE
- **ネットワークモード**: awsvpc
- **CPU**: 1024 (1 vCPU)
- **メモリ**: 4096 MB
- **実行ロール**: ecsTaskExecutionRole
- **タスクロール**: カスタム IAM ロール

#### セキュリティグループ

```hcl
resource "aws_security_group" "ecs_tasks" {
  name        = "equtum-ecs-tasks"
  description = "Security group for ECS tasks"
  vpc_id      = aws_vpc.equtum_vpc.id

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "equtum-ecs-tasks"
  }
}
```

### 2. Amazon ECR (Elastic Container Registry)

#### リポジトリ設定

```hcl
resource "aws_ecr_repository" "equtum_batch" {
  name                 = "equtum-batch"
  image_tag_mutability = "MUTABLE"

  image_scanning_configuration {
    scan_on_push = true
  }

  lifecycle_policy {
    policy = jsonencode({
      rules = [
        {
          rulePriority = 1
          description  = "Keep last 10 images"
          selection = {
            tagStatus     = "tagged"
            tagPrefixList = ["v"]
            countType     = "imageCountMoreThan"
            countNumber   = 10
          }
          action = {
            type = "expire"
          }
        }
      ]
    })
  }
}
```

### 3. Amazon EventBridge Scheduler

#### スケジュール設定例

```hcl
resource "aws_scheduler_schedule" "batch_schedules" {
  for_each = local.batch_schedules

  name                = each.value.name
  description         = each.value.description
  schedule_expression = each.value.schedule

  flexible_time_window {
    mode = "OFF"
  }

  target {
    arn      = aws_ecs_cluster.equtum_batch.arn
    role_arn = aws_iam_role.ecs_events_role.arn

    ecs_parameters {
      task_definition_arn = aws_ecs_task_definition.equtum_batch.arn
      launch_type         = "FARGATE"
      platform_version    = "LATEST"
      task_count          = 1

      network_configuration {
        subnets = [
          aws_subnet.private_subnet_1.id,
          aws_subnet.private_subnet_2.id,
          aws_subnet.private_subnet_3.id
        ]
        security_groups  = [aws_security_group.ecs_tasks.id]
        assign_public_ip = true
      }
    }

    input = jsonencode({
      containerOverrides = [
        {
          name    = "batch"
          command = each.value.command
        }
      ]
    })
  }
}
```

### 4. AWS Secrets Manager

#### シークレット管理

```hcl
resource "aws_secretsmanager_secret" "database_url" {
  name                    = "equtum-batch/database-url"
  description             = "Database connection URL for batch processing"
  recovery_window_in_days = 7

  tags = {
    Environment = var.environment
    Project     = "equtum-batch"
  }
}

resource "aws_secretsmanager_secret_version" "database_url" {
  secret_id     = aws_secretsmanager_secret.database_url.id
  secret_string = var.database_url
}
```

### 5. Amazon CloudWatch

#### ロググループ

```hcl
resource "aws_cloudwatch_log_group" "batch_logs" {
  name              = "/ecs/equtum-batch"
  retention_in_days = 30

  tags = {
    Environment = var.environment
    Project     = "equtum-batch"
  }
}
```

#### CloudWatch Alarms

```hcl
# タスク失敗アラーム
resource "aws_cloudwatch_metric_alarm" "task_failed" {
  alarm_name          = "equtum-batch-task-failed"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "TasksFailed"
  namespace           = "AWS/ECS"
  period              = "300"
  statistic           = "Sum"
  threshold           = "0"
  alarm_description   = "This metric monitors failed batch tasks"
  alarm_actions       = [aws_sns_topic.alerts.arn]

  dimensions = {
    ClusterName = aws_ecs_cluster.equtum_batch.name
  }
}

# 実行時間超過アラーム
resource "aws_cloudwatch_metric_alarm" "task_duration" {
  alarm_name          = "equtum-batch-task-duration"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "TaskDuration"
  namespace           = "Custom/EqutumBatch"
  period              = "300"
  statistic           = "Average"
  threshold           = "900"  # 15分
  alarm_description   = "This metric monitors batch task duration"
  alarm_actions       = [aws_sns_topic.alerts.arn]
}
```

### 6. Amazon VPC

#### ネットワーク構成

```hcl
resource "aws_vpc" "equtum_vpc" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name = "equtum-vpc"
  }
}

# プライベートサブネット
resource "aws_subnet" "private_subnet_1" {
  vpc_id            = aws_vpc.equtum_vpc.id
  cidr_block        = "********/24"
  availability_zone = "ap-northeast-1a"

  tags = {
    Name = "equtum-private-subnet-1"
  }
}

# NAT Gateway
resource "aws_nat_gateway" "equtum_nat" {
  allocation_id = aws_eip.nat_eip.id
  subnet_id     = aws_subnet.public_subnet_1.id

  tags = {
    Name = "equtum-nat-gateway"
  }
}
```

## IAM 権限設定

### 1. ECS Task Execution Role

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "ecr:GetAuthorizationToken",
        "ecr:BatchCheckLayerAvailability",
        "ecr:GetDownloadUrlForLayer",
        "ecr:BatchGetImage",
        "logs:CreateLogStream",
        "logs:PutLogEvents",
        "secretsmanager:GetSecretValue"
      ],
      "Resource": "*"
    }
  ]
}
```

### 2. ECS Task Role

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": ["secretsmanager:GetSecretValue"],
      "Resource": ["arn:aws:secretsmanager:ap-northeast-1:*:secret:equtum-batch/*"]
    },
    {
      "Effect": "Allow",
      "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"],
      "Resource": "arn:aws:logs:ap-northeast-1:*:log-group:/ecs/equtum-batch*"
    }
  ]
}
```

### 3. EventBridge Scheduler Role

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": ["ecs:RunTask"],
      "Resource": ["arn:aws:ecs:ap-northeast-1:*:task-definition/equtum-batch:*"]
    },
    {
      "Effect": "Allow",
      "Action": ["iam:PassRole"],
      "Resource": [
        "arn:aws:iam::*:role/ecsTaskExecutionRole",
        "arn:aws:iam::*:role/equtum-batch-task-role"
      ]
    }
  ]
}
```

## セキュリティ設定

### 1. ネットワークセキュリティ

- **プライベートサブネット**: ECS タスクは外部からの直接アクセス不可
- **NAT Gateway**: アウトバウンド通信のみ許可
- **セキュリティグループ**: 最小権限でのアクセス制御

### 2. データ暗号化

- **転送時**: TLS 1.2 以上での通信
- **保存時**: EBS・S3 での暗号化
- **Secrets Manager**: 機密情報の暗号化保存

### 3. アクセス制御

- **IAM**: 最小権限の原則
- **リソースベースポリシー**: きめ細かいアクセス制御
- **CloudTrail**: 全 API 呼び出しの監査ログ

## 監視・ログ設定

### 1. CloudWatch Metrics

#### カスタムメトリクス

```typescript
// アプリケーション内でのメトリクス送信例
await cloudWatch
  .putMetricData({
    Namespace: 'Custom/EqutumBatch',
    MetricData: [
      {
        MetricName: 'JobExecutionTime',
        Value: executionTime,
        Unit: 'Seconds',
        Dimensions: [
          {
            Name: 'JobName',
            Value: jobName,
          },
        ],
      },
    ],
  })
  .promise();
```

#### 標準メトリクス

- **CPUUtilization**: CPU 使用率
- **MemoryUtilization**: メモリ使用率
- **TaskCount**: 実行中タスク数
- **TasksFailed**: 失敗タスク数

### 2. ログ集約

```json
{
  "timestamp": "2024-01-01T10:00:00.000Z",
  "level": "INFO",
  "message": "Batch job started",
  "jobName": "data-cleanup",
  "jobId": "batch-123",
  "environment": "production"
}
```

### 3. アラート設定

```hcl
# SNS トピック
resource "aws_sns_topic" "batch_alerts" {
  name = "equtum-batch-alerts"
}

# メール通知
resource "aws_sns_topic_subscription" "email" {
  topic_arn = aws_sns_topic.batch_alerts.arn
  protocol  = "email"
  endpoint  = "<EMAIL>"
}

# Slack通知
resource "aws_sns_topic_subscription" "slack" {
  topic_arn = aws_sns_topic.batch_alerts.arn
  protocol  = "https"
  endpoint  = "https://hooks.slack.com/services/..."
}
```

## コスト最適化

### 1. ECS Fargate

- **適切なサイジング**: CPU/メモリの最適化
- **スポットキャパシティ**: 開発環境での利用
- **スケジューリング**: 非ピーク時間での実行

### 2. CloudWatch

- **ログ保持期間**: 必要最小限の設定
- **メトリクス頻度**: 適切な収集間隔
- **不要なログの削除**: 定期的なクリーンアップ

### 3. Secrets Manager

- **シークレット数の最適化**: 統合可能なシークレットの整理
- **自動ローテーション**: セキュリティと運用コストのバランス

## 災害復旧・BCP

### 1. バックアップ戦略

```hcl
# MySQL バックアップ設定
resource "aws_db_instance" "batch_db" {
  engine                 = "mysql"
  engine_version         = "8.0"
  backup_retention_period = 7
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"

  # 暗号化
  storage_encrypted = true
}
```

### 2. マルチリージョン構成

- **プライマリリージョン**: ap-northeast-1 (東京)
- **セカンダリリージョン**: ap-northeast-3 (大阪) - 将来的に
- **データ同期**: RDS Read Replica

### 3. 復旧手順

1. **障害検知**: CloudWatch Alarms
2. **自動復旧**: ECS による自動再起動
3. **手動復旧**: 運用チームによる介入
4. **データ復旧**: RDS バックアップからの復元

## 運用ベストプラクティス

### 1. デプロイメント

- **ブルーグリーンデプロイ**: ダウンタイムなしの更新
- **ロールバック**: 問題発生時の迅速な巻き戻し
- **段階的リリース**: dev → staging → production

### 2. 監視

- **プロアクティブ監視**: しきい値の適切な設定
- **ログ分析**: 異常パターンの早期発見
- **定期ヘルスチェック**: システム健全性の確認

### 3. セキュリティ

- **定期的な権限見直し**: 最小権限の維持
- **セキュリティパッチ**: 定期的な更新
- **侵入検知**: AWS GuardDuty の活用

## トラブルシューティング

### 1. よくある問題

#### ECS タスクが起動しない

```bash
# タスク定義の確認
aws ecs describe-task-definition --task-definition equtum-batch

# タスクの詳細確認
aws ecs describe-tasks --cluster equtum-batch --tasks <task-arn>

# ログの確認
aws logs get-log-events --log-group-name /ecs/equtum-batch
```

#### メモリ不足エラー

```bash
# CloudWatch Metrics の確認
aws cloudwatch get-metric-statistics \
  --namespace AWS/ECS \
  --metric-name MemoryUtilization \
  --dimensions Name=ClusterName,Value=equtum-batch
```

### 2. パフォーマンス問題

#### データベース接続エラー

```typescript
// 接続プール設定の確認
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
  // 接続プール設定
  __internal: {
    engine: {
      connectionPooling: {
        maxConnections: 10,
        minConnections: 2,
      },
    },
  },
});
```

### 3. セキュリティインシデント

#### 異常なアクセスパターン

1. **CloudTrail ログの確認**
2. **IAM 権限の見直し**
3. **ネットワークアクセスの制限**
4. **認証情報のローテーション**

## 今後の改善計画

### 短期（1-3 ヶ月）

- [ ] より詳細なメトリクス収集
- [ ] 自動スケーリング設定
- [ ] コスト最適化の実施

### 中期（3-6 ヶ月）

- [ ] マルチリージョン対応
- [ ] セキュリティ強化
- [ ] パフォーマンス最適化

### 長期（6 ヶ月以上）

- [ ] 完全自動化された運用
- [ ] 予測的スケーリング
- [ ] AI/ML を活用した監視
