# 技術ドキュメント

Equtum Batch Processing System の技術ドキュメント集です。

## 📚 ドキュメント一覧

### 🏗️ [アーキテクチャ設計](./architecture.md)

システム全体のアーキテクチャと技術スタック、設計思想について説明しています。

**内容:**

- システム構成図
- 技術スタック詳細
- データフローとアクセスパターン
- セキュリティ設計
- パフォーマンス設計
- 拡張性の考慮
- 災害復旧・BCP

**対象読者:** アーキテクト、シニアエンジニア、新規参画者

---

### 📋 [バッチ処理仕様](./batch-specifications.md)

実装されているバッチジョブの詳細仕様と開発ガイドです。

**内容:**

- バッチジョブ共通仕様
- 現在実装されているジョブ詳細
- 新しいジョブの追加手順
- コーディング規約
- 運用における注意事項
- 今後の拡張予定

**対象読者:** アプリケーション開発者、運用担当者

---

### ☁️ [AWS 構成ガイド](./aws-configuration.md)

使用する AWS サービスの詳細構成と運用ガイドです。

**内容:**

- AWS サービス構成詳細
- IAM 権限設定
- セキュリティ設定
- 監視・ログ設定
- コスト最適化
- 災害復旧・BCP
- トラブルシューティング

**対象読者:** インフラエンジニア、DevOps エンジニア、運用担当者

---

### 🚀 [デプロイメント手順](./deployment.md)

アプリケーションとインフラのデプロイメント手順書です。

**内容:**

- デプロイメント方式
- 事前準備
- インフラストラクチャのデプロイ
- アプリケーションのデプロイ
- デプロイメント検証
- ロールバック手順
- 環境別設定
- トラブルシューティング

**対象読者:** DevOps エンジニア、リリース担当者、運用担当者

## 🔄 ドキュメント更新フロー

### 更新タイミング

1. **機能追加・変更時**

   - 新しいバッチジョブの追加
   - アーキテクチャの変更
   - 新しい AWS サービスの追加

2. **設定変更時**

   - インフラ設定の変更
   - デプロイ手順の変更
   - 監視設定の変更

3. **定期見直し**
   - 四半期ごとの内容確認
   - 古い情報の更新
   - 新しいベストプラクティスの追加

### 更新手順

1. **Issue 作成**

   ```bash
   # ドキュメント更新のIssue作成
   gh issue create \
     --title "docs: update architecture documentation" \
     --body "Update system architecture documentation with new services" \
     --label documentation
   ```

2. **ブランチ作成・編集**

   ```bash
   # ドキュメント更新用ブランチ作成
   git checkout -b docs/issue-123-update-architecture

   # ドキュメント編集
   # ...

   # コミット
   git commit -m "docs(architecture): update system architecture diagram"
   ```

3. **レビュー・マージ**
   - プルリクエスト作成
   - 技術的内容のレビュー
   - ドキュメント品質のチェック
   - main ブランチへマージ

## 📖 読み方ガイド

### 新規参画者向け

1. **[アーキテクチャ設計](./architecture.md)** - システム全体の理解
2. **[バッチ処理仕様](./batch-specifications.md)** - 実装詳細の理解
3. **[AWS 構成ガイド](./aws-configuration.md)** - インフラ環境の理解
4. **[デプロイメント手順](./deployment.md)** - 運用手順の理解

### 機能開発時

1. **[バッチ処理仕様](./batch-specifications.md)** - 実装ガイドライン確認
2. **[アーキテクチャ設計](./architecture.md)** - 設計原則の確認
3. **[デプロイメント手順](./deployment.md)** - リリース手順の確認

### 障害対応時

1. **[AWS 構成ガイド](./aws-configuration.md)** - トラブルシューティング
2. **[デプロイメント手順](./deployment.md)** - ロールバック手順
3. **[バッチ処理仕様](./batch-specifications.md)** - 運用注意事項

### インフラ変更時

1. **[AWS 構成ガイド](./aws-configuration.md)** - 現在の構成確認
2. **[アーキテクチャ設計](./architecture.md)** - 設計思想の確認
3. **[デプロイメント手順](./deployment.md)** - デプロイ影響の確認

## 🤝 貢献方法

### ドキュメント改善

ドキュメントの改善提案は以下の方法で受け付けています：

1. **Issue 作成**

   - 間違いや不明確な記載の報告
   - 新しい情報の追加提案
   - 構成改善の提案

2. **Pull Request**
   - 軽微な修正（typo、リンク修正など）
   - 新しい情報の追加
   - 図表の改善

### レビューのポイント

- **正確性**: 技術的な内容が正しいか
- **明確性**: 理解しやすい説明になっているか
- **完全性**: 必要な情報が含まれているか
- **最新性**: 情報が最新の状態か
- **一貫性**: 他のドキュメントとの整合性

## 📝 ドキュメント規約

### ファイル命名規則

- ケバブケース使用: `batch-specifications.md`
- 日本語ファイル名は避ける
- 内容が分かりやすい名前を使用

### Markdown 記法

- 見出しレベルの統一
- コードブロックの言語指定
- 適切なリンク設定
- 図表の代替テキスト設定

### 図表作成

- Mermaid 記法を優先使用
- 複雑な図は外部ツールで作成してリンク
- 図表には適切なキャプション追加

## 🔗 関連リンク

- **プロジェクト README**: [../README.md](../README.md)
- **インフラ設定**: [../infra/](../infra/)
- **ソースコード**: [../src/](../src/)
- **GitHub Issues**: [Issues](https://github.com/abelorg/equtum-batch/issues)

## 📧 お問い合わせ

技術ドキュメントに関するご質問や提案は、以下の方法でお知らせください：

- **GitHub Issue**: 新しい Issue を作成
- **Pull Request**: 改善提案を送信
- **チーム内 Slack**: #equtum-batch チャンネル

---

_最終更新: 2024 年 7 月 29 日_
