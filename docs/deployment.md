# デプロイメント手順書

## 概要

Equtum Batch Processing System のデプロイメント手順と運用ガイドです。

## デプロイメント方式

### 1. GitOps によるデプロイメント

```mermaid
flowchart LR
    Dev[開発者] --> PR[Pull Request]
    PR --> Review[Code Review]
    Review --> Merge[main ブランチマージ]
    Merge --> CI[GitHub Actions]
    CI --> Build[Docker Build]
    Build --> Push[ECR Push]
    Push --> Deploy[ECS Deploy]
    Deploy --> Verify[動作確認]
```

### 2. 環境別デプロイメント戦略

- **開発環境 (dev)**: 全ブランチの自動デプロイ
- **本番環境 (prd)**: main ブランチのタグベースデプロイ

## 事前準備

### 1. 必要なツール

```bash
# AWS CLI のインストール
brew install awscli

# Docker のインストール
brew install --cask docker

# Terraform のインストール
brew install terraform

# GitHub CLI のインストール
brew install gh
```

### 2. AWS 認証設定

```bash
# AWS プロファイル設定
aws configure --profile equtum-dev
aws configure --profile equtum-prd

# 認証確認
aws sts get-caller-identity --profile equtum-dev
```

### 3. 環境変数設定

```bash
# .env.local (開発環境用)
export AWS_PROFILE=equtum-dev
export AWS_REGION=ap-northeast-1
export TF_VAR_environment=dev

# .env.production (本番環境用)
export AWS_PROFILE=equtum-prd
export AWS_REGION=ap-northeast-1
export TF_VAR_environment=prd
```

## インフラストラクチャのデプロイ

### 1. Terraform による基盤構築

#### 開発環境のデプロイ

```bash
# infra/dev ディレクトリに移動
cd infra/dev

# Terraform 初期化
terraform init

# プランの確認
terraform plan

# 実行
terraform apply

# 出力の確認
terraform output
```

#### 本番環境のデプロイ

```bash
# infra/prd ディレクトリに移動
cd infra/prd

# Terraform 初期化
terraform init

# プランの確認
terraform plan

# 実行（慎重に）
terraform apply

# 出力の確認
terraform output
```

### 2. 必要な AWS リソース

- **VPC**: ネットワーク基盤
- **ECS Cluster**: コンテナ実行環境
- **ECR Repository**: Docker イメージ保存
- **Secrets Manager**: 機密情報管理
- **CloudWatch**: ログ・監視
- **EventBridge Scheduler**: バッチスケジューリング
- **IAM Roles**: 権限管理

## アプリケーションのデプロイ

### 1. 手動デプロイ手順

#### Docker イメージのビルド

```bash
# プロジェクトルートで実行
docker build -t equtum-batch:latest .

# イメージの確認
docker images | grep equtum-batch
```

#### ECR への Push

```bash
# ECR ログイン
aws ecr get-login-password --region ap-northeast-1 | \
  docker login --username AWS --password-stdin \
  <account-id>.dkr.ecr.ap-northeast-1.amazonaws.com

# タグ付け
docker tag equtum-batch:latest \
  <account-id>.dkr.ecr.ap-northeast-1.amazonaws.com/equtum-batch:latest

# Push
docker push \
  <account-id>.dkr.ecr.ap-northeast-1.amazonaws.com/equtum-batch:latest
```

#### ECS タスク定義の更新

```bash
# 最新のタスク定義を取得
aws ecs describe-task-definition \
  --task-definition equtum-batch \
  --query 'taskDefinition' > task-definition.json

# イメージURIを更新（task-definition.json を編集）

# 新しいタスク定義を登録
aws ecs register-task-definition \
  --cli-input-json file://task-definition.json
```

### 2. GitHub Actions による自動デプロイ

#### ワークフロー設定

```yaml
# .github/workflows/deploy.yml
name: Deploy Batch Application

on:
  push:
    branches: [main]
    tags: ['v*']

env:
  AWS_REGION: ap-northeast-1
  ECR_REPOSITORY: equtum-batch

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build and push Docker image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: ${{ github.sha }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          docker tag $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG \
            $ECR_REGISTRY/$ECR_REPOSITORY:latest
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest

      - name: Update ECS task definition
        run: |
          # タスク定義の更新スクリプト実行
          ./scripts/update-task-definition.sh
```

#### 必要な GitHub Secrets

```bash
# GitHub リポジトリの Settings > Secrets で設定
AWS_ACCESS_KEY_ID=<IAMユーザーのアクセスキー>
AWS_SECRET_ACCESS_KEY=<IAMユーザーのシークレットキー>
DATABASE_URL=<データベース接続URL>
```

## デプロイメント検証

### 1. 機能テスト

#### バッチジョブの手動実行

```bash
# ECS タスクの手動実行
aws ecs run-task \
  --cluster equtum-batch \
  --task-definition equtum-batch:latest \
  --launch-type FARGATE \
  --network-configuration "awsvpcConfiguration={subnets=[subnet-xxx],securityGroups=[sg-xxx],assignPublicIp=ENABLED}" \
  --overrides '{
    "containerOverrides": [
      {
        "name": "batch",
        "command": ["npm", "start", "example-job"]
      }
    ]
  }'
```

#### ログの確認

```bash
# CloudWatch Logs の確認
aws logs get-log-events \
  --log-group-name /ecs/equtum-batch \
  --log-stream-name <stream-name>

# 最新のログを取得
aws logs tail /ecs/equtum-batch --follow
```

### 2. ヘルスチェック

#### データベース接続確認

```bash
# Prisma Studio の起動（開発環境）
npm run prisma:studio

# データベース接続テスト
npx prisma db seed
```

#### メトリクス確認

```bash
# CloudWatch メトリクスの確認
aws cloudwatch get-metric-statistics \
  --namespace AWS/ECS \
  --metric-name CPUUtilization \
  --dimensions Name=ClusterName,Value=equtum-batch \
  --start-time 2024-01-01T00:00:00Z \
  --end-time 2024-01-01T23:59:59Z \
  --period 3600 \
  --statistics Average
```

## ロールバック手順

### 1. 前バージョンへの復帰

#### イメージタグの確認

```bash
# ECR の全イメージタグを確認
aws ecr describe-images \
  --repository-name equtum-batch \
  --query 'imageDetails[*].imageTags' \
  --output table
```

#### タスク定義のロールバック

```bash
# 前のタスク定義バージョンを確認
aws ecs list-task-definitions \
  --family-prefix equtum-batch \
  --status ACTIVE

# 前バージョンにロールバック
aws ecs update-service \
  --cluster equtum-batch \
  --service equtum-batch-service \
  --task-definition equtum-batch:N  # N は前のバージョン番号
```

### 2. 緊急時の対応

#### 全バッチジョブの停止

```bash
# 実行中のタスクを全て停止
aws ecs list-tasks --cluster equtum-batch \
  --query 'taskArns[*]' --output text | \
  xargs -I {} aws ecs stop-task --cluster equtum-batch --task {}
```

#### スケジューラーの無効化

```bash
# EventBridge スケジューラーの無効化
aws scheduler update-schedule \
  --name data-cleanup-schedule \
  --state DISABLED
```

## 環境別デプロイメント設定

### 1. 開発環境 (dev)

```yaml
# 設定値
environment: dev
cpu: 512
memory: 1024
log_level: debug
schedule_enabled: false
auto_deploy: true
```

#### 特徴

- リソース最小構成
- デバッグログ有効
- 自動スケジュール無効
- 全ブランチ自動デプロイ

### 2. 本番環境 (prd)

```yaml
# 設定値
environment: prd
cpu: 1024
memory: 4096
log_level: info
schedule_enabled: true
auto_deploy: false
```

#### 特徴

- 本番相当のリソース
- 最適化されたログレベル
- 自動スケジュール有効
- タグベースの手動デプロイ

## 監視・アラート設定

### 1. デプロイメント監視

#### デプロイ成功・失敗の通知

```bash
# SNS によるデプロイ通知
aws sns publish \
  --topic-arn arn:aws:sns:ap-northeast-1:xxx:deploy-notifications \
  --message "Deployment completed successfully for equtum-batch"
```

#### ロールバック監視

```bash
# CloudWatch アラームによる自動ロールバック
aws cloudwatch put-metric-alarm \
  --alarm-name "equtum-batch-high-error-rate" \
  --alarm-description "High error rate detected" \
  --metric-name ErrorRate \
  --namespace Custom/EqutumBatch \
  --statistic Average \
  --period 300 \
  --threshold 10.0 \
  --comparison-operator GreaterThanThreshold \
  --evaluation-periods 2
```

### 2. パフォーマンス監視

```bash
# カスタムメトリクスの設定
aws logs put-metric-filter \
  --log-group-name /ecs/equtum-batch \
  --filter-name batch-error-count \
  --filter-pattern "ERROR" \
  --metric-transformations \
    metricName=BatchErrors,metricNamespace=Custom/EqutumBatch,metricValue=1
```

## セキュリティ考慮事項

### 1. 認証情報管理

- **Secrets Manager**: すべての機密情報
- **環境変数**: 非機密の設定値のみ
- **IAM ロール**: 最小権限の原則

### 2. ネットワークセキュリティ

- **プライベートサブネット**: ECS タスクの配置
- **NAT Gateway**: 制限されたアウトバウンド通信
- **セキュリティグループ**: 最小限のポート開放

### 3. 監査ログ

```bash
# CloudTrail によるデプロイ監査
aws cloudtrail lookup-events \
  --lookup-attributes AttributeKey=EventName,AttributeValue=RunTask \
  --start-time 2024-01-01T00:00:00Z \
  --end-time 2024-01-01T23:59:59Z
```

## トラブルシューティング

### 1. デプロイ失敗時の対応

#### タスク定義の問題

```bash
# タスク定義の詳細確認
aws ecs describe-task-definition \
  --task-definition equtum-batch:latest \
  --include TAGS

# 問題のあるタスクの詳細
aws ecs describe-tasks \
  --cluster equtum-batch \
  --tasks <task-arn> \
  --include TAGS
```

#### ネットワーク問題

```bash
# セキュリティグループの確認
aws ec2 describe-security-groups \
  --group-ids sg-xxx

# サブネットの確認
aws ec2 describe-subnets \
  --subnet-ids subnet-xxx
```

### 2. 実行時問題の診断

#### メモリ不足

```bash
# メモリ使用率の確認
aws cloudwatch get-metric-statistics \
  --namespace AWS/ECS \
  --metric-name MemoryUtilization \
  --dimensions Name=ClusterName,Value=equtum-batch \
  --start-time $(date -d '1 hour ago' -u +%Y-%m-%dT%H:%M:%S) \
  --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
  --period 300 \
  --statistics Average,Maximum
```

#### データベース接続問題

```bash
# 接続エラーログの検索
aws logs filter-log-events \
  --log-group-name /ecs/equtum-batch \
  --filter-pattern "connection" \
  --start-time $(date -d '1 hour ago' +%s)000
```

## 今後の改善計画

### 短期（1-3 ヶ月）

- [ ] ブルーグリーンデプロイの実装
- [ ] 自動ロールバック機能
- [ ] より詳細な監視ダッシュボード

### 中期（3-6 ヶ月）

- [ ] カナリアデプロイメント
- [ ] インフラのドリフト検知
- [ ] セキュリティスキャンの自動化

### 長期（6 ヶ月以上）

- [ ] GitOps ツール（ArgoCD 等）の導入
- [ ] 予測的スケーリング
- [ ] 完全自動化されたデプロイパイプライン
