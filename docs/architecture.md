# アーキテクチャ設計

## 概要

Equtum Batch Processing System は、TypeScript + Prisma を使用したスケーラブルなバッチ処理システムです。AWS ECS Fargate 上で動作し、EventBridge Scheduler によって定期実行されます。

## システム構成

```mermaid
graph TB
    subgraph "AWS Cloud"
        subgraph "ECS Cluster"
            T1[ECS Task<br/>Batch Processor]
            T2[ECS Task<br/>Batch Processor]
            T3[ECS Task<br/>Batch Processor]
        end

        EB[EventBridge Scheduler]
        ECR[ECR Repository]
        CW[CloudWatch Logs/Metrics]
        SM[Secrets Manager]

        subgraph "Database"
            MySQL[(MySQL 8.0<br/>Database)]
        end
    end

    subgraph "CI/CD"
        GH[GitHub Actions]
        GHR[GitHub Repository]
    end

    EB --> T1
    EB --> T2
    EB --> T3

    T1 --> MySQL
    T2 --> MySQL
    T3 --> MySQL

    T1 --> CW
    T2 --> CW
    T3 --> CW

    T1 --> SM
    T2 --> SM
    T3 --> SM

    GH --> ECR
    GHR --> GH

    ECR --> T1
    ECR --> T2
    ECR --> T3
```

## アーキテクチャの特徴

### 1. スケーラビリティ

- **水平スケーリング**: ECS Fargate により必要に応じてタスクを増減
- **垂直スケーリング**: CPU/メモリ要件に応じたリソース調整
- **並列処理**: 複数のバッチジョブを同時実行可能

### 2. 信頼性

- **耐障害性**: ECS による自動復旧機能
- **リトライ機能**: EventBridge Scheduler による失敗時の再実行
- **ログ集約**: CloudWatch Logs による包括的なログ管理

### 3. セキュリティ

- **IAM ロールベース**: 最小権限の原則に基づくアクセス制御
- **シークレット管理**: AWS Secrets Manager による機密情報の保護
- **ネットワーク分離**: VPC 内での安全な通信

### 4. 運用性

- **モニタリング**: CloudWatch Metrics/Alarms による監視
- **自動デプロイ**: GitHub Actions による CI/CD パイプライン
- **設定管理**: Terraform による Infrastructure as Code

## 技術スタック

### アプリケーション層

| コンポーネント | 技術        | 説明                   |
| -------------- | ----------- | ---------------------- |
| 言語           | TypeScript  | 型安全性と DX 向上     |
| ランタイム     | Node.js 20  | LTS 版による安定性     |
| ORM            | Prisma      | 型安全な DB 操作       |
| ログ           | 自作 Logger | 構造化ログとレベル制御 |
| 品質管理       | Biome       | 高速な Lint/Format     |

### インフラ層

| コンポーネント       | 技術                  | 説明                          |
| -------------------- | --------------------- | ----------------------------- |
| コンテナ             | Docker                | 軽量な Node.js 20-slim ベース |
| オーケストレーション | ECS Fargate           | サーバーレスコンテナ実行      |
| スケジューリング     | EventBridge Scheduler | 柔軟な Cron 設定              |
| データベース         | MySQL 8.0             | 軽量・高性能 DB               |
| 監視                 | CloudWatch            | ログ・メトリクス集約          |
| 機密管理             | Secrets Manager       | 環境変数・認証情報            |
| IaC                  | Terraform             | インフラ定義とバージョン管理  |

### 開発・運用

| コンポーネント     | 技術               | 説明                         |
| ------------------ | ------------------ | ---------------------------- |
| 開発環境           | DevContainer       | 統一された開発環境           |
| CI/CD              | GitHub Actions     | 自動テスト・ビルド・デプロイ |
| コンテナレジストリ | ECR                | Docker イメージ保存          |
| 設定管理           | 環境変数 + Secrets | 設定の外部化                 |

## データフロー

### 1. バッチ実行フロー

```mermaid
sequenceDiagram
    participant EB as EventBridge Scheduler
    participant ECS as ECS Fargate
    participant App as Batch App
    participant DB as MySQL
    participant CW as CloudWatch

    EB->>ECS: トリガー実行
    ECS->>App: コンテナ起動

    App->>DB: BatchJob作成 (RUNNING)
    App->>CW: 開始ログ出力

    App->>App: ビジネスロジック実行
    App->>DB: データ処理

    alt 成功時
        App->>DB: BatchJob更新 (COMPLETED)
        App->>CW: 成功ログ出力
    else 失敗時
        App->>DB: BatchJob更新 (FAILED)
        App->>CW: エラーログ出力
    end

    App->>ECS: プロセス終了
    ECS->>EB: 実行結果通知
```

### 2. データアクセスパターン

```mermaid
graph LR
    App[Batch Application]
    Prisma[Prisma Client]
    Pool[Connection Pool]
    DB[(MySQL)]

    App --> Prisma
    Prisma --> Pool
    Pool --> DB

    subgraph "設定"
        Config[DATABASE_URL<br/>PRISMA_TRANSACTION_TIMEOUT]
    end

    Config --> Prisma
```

## セキュリティ設計

### 1. 認証・認可

- **IAM ロール**: ECS タスクロールによる最小権限アクセス
- **Secrets Manager**: DB 接続文字列等の機密情報管理
- **VPC**: プライベートサブネット内でのタスク実行

### 2. ネットワークセキュリティ

- **セキュリティグループ**: 必要最小限のポート開放
- **NAT Gateway**: アウトバウンド通信の制御
- **VPC Endpoints**: AWS サービスへの安全な接続

### 3. 監査・ログ

- **CloudTrail**: API 呼び出しの記録
- **CloudWatch Logs**: アプリケーションログの集約
- **セキュリティ監視**: 異常なアクセスパターンの検知

## パフォーマンス設計

### 1. リソース設定

```typescript
// ECS Task Definition (例)
{
  cpu: "1024",        // 1 vCPU
  memory: "4096",     // 4 GB
  ephemeralStorage: "20GB"
}
```

### 2. データベース最適化

- **接続プール**: Prisma による効率的な接続管理
- **トランザクション**: 適切なタイムアウト設定
- **インデックス**: クエリパフォーマンスの最適化

### 3. 監視メトリクス

- **実行時間**: バッチジョブの処理時間
- **メモリ使用量**: ECS タスクのリソース消費
- **エラー率**: 失敗ジョブの割合
- **データベース性能**: クエリ実行時間・接続数

## 拡張性の考慮

### 1. 新しいバッチジョブの追加

1. `BatchProcessor.ts` にメソッド追加
2. `jobs` オブジェクトに登録
3. Terraform でスケジュール設定追加
4. デプロイ

### 2. パフォーマンス要件の変更

- ECS Task Definition の CPU/メモリ調整
- データベース接続プールサイズ調整
- タイムアウト値の調整

### 3. 新しい技術要件

- 新しい AWS サービスとの連携
- 外部 API との統合
- 新しいデータソースの追加

## 災害復旧・BCP

### 1. データバックアップ

- **自動バックアップ**: Cloud SQL の自動バックアップ機能
- **ポイントインタイム復旧**: 7 日間の復旧ポイント
- **リージョン間複製**: 災害時の復旧対応

### 2. サービス継続性

- **マルチ AZ**: 可用性ゾーン間での冗長性
- **ヘルスチェック**: ECS によるタスクの自動復旧
- **フェイルオーバー**: データベースの自動フェイルオーバー

### 3. 監視・アラート

- **異常検知**: CloudWatch Alarms による即座の通知
- **エスカレーション**: 重要度に応じた通知先設定
- **復旧手順**: 運用チームによる迅速な対応
