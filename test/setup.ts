import { afterEach, beforeEach, vi } from 'vitest';
import { initialize } from './factories/fabbrica';

vi.mock('../src/repositories/client', () => ({
  client: vPrisma.client,
}));

initialize({ prisma: vPrisma.client });

beforeEach(async () => {
  vi.clearAllMocks();

  await Promise.all([
    vPrismaDelegate.handleTestEvent({ name: 'test_start' }),
    vPrismaDelegate.handleTestEvent({ name: 'test_fn_start' }),
  ]);
});

afterEach(async () => {
  vi.useRealTimers();

  await Promise.all([
    vPrismaDelegate.handleTestEvent({ name: 'test_done' }),
    vPrismaDelegate.handleTestEvent({
      name: 'test_fn_success',
      test: { parent: null },
    }),
  ]);
});
