import { existsSync } from 'node:fs';
import { config } from 'dotenv';

// docs: https://vitest.dev/config/#globalsetup
// example: https://github.com/vitest-dev/vitest/tree/main/test/global-setup/globalSetup
export const setup = async () => {
  // Load environment variables from .env file if it exists
  const envPath = '.env';
  if (existsSync(envPath)) {
    config({ path: envPath });
  } else {
    // Using default environment variables when .env is not found
  }
};
