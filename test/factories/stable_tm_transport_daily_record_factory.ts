import { parse, v7 as uuidv7 } from 'uuid';
import { defineStableTmTransportDailyRecordFactory } from './fabbrica';
import { SectionFactory } from './section_factory';
import { StableFactory } from './stable_factory';

export const TransportDailyRecordFactory = defineStableTmTransportDailyRecordFactory({
  defaultData: ({ seq }) => {
    return {
      transportDailyRecordId: Buffer.from(parse(uuidv7(undefined, undefined, seq))),
      section: SectionFactory,
      stable: StableFactory,
    };
  },
});
