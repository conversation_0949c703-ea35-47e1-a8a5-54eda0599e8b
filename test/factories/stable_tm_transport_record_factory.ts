import { parse, v7 as uuidv7 } from 'uuid';
import { defineStableTmTransportRecordFactory } from './fabbrica';
import { HorseFactory } from './horse_factory';

export const TransportRecordFactory = defineStableTmTransportRecordFactory({
  defaultData: ({ seq }) => {
    return {
      transportRecordId: Buffer.from(parse(uuidv7(undefined, undefined, seq))),
      horse: HorseFactory,
      type: 'in',
      index: `${seq}`,
    };
  },
});
