import { BatchStatus } from '@prisma/client';
import { parse, v7 as uuidv7 } from 'uuid';
import { defineBatchJobFactory } from './fabbrica';

export const BatchJobFactory = defineBatchJobFactory({
  defaultData: ({ seq }) => {
    return {
      batchJobId: Buffer.from(parse(uuidv7(undefined, undefined, seq))),
      name: `test-job-${seq}`,
      description: `Test batch job ${seq}`,
      status: BatchStatus.PENDING,
      metadata: JSON.stringify({ test: true, sequence: seq }),
    };
  },
});
