import { parse, v7 as uuidv7 } from 'uuid';
import { defineStableTmOutsideFarmFactory } from './fabbrica';
import { FarmAreaFactory } from './farm_area_factory';
import { OrganizationFactory } from './organization_factory';

export const StableTmOutsideFarmFactory = defineStableTmOutsideFarmFactory({
  defaultData: () => ({
    outsideFarmId: Buffer.from(parse(uuidv7())),
    organization: OrganizationFactory,
    farmArea: FarmAreaFactory,
  }),
});
