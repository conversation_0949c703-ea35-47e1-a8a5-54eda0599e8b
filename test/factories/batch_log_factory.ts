import { LogLevel } from '@prisma/client';
import { parse, v7 as uuidv7 } from 'uuid';
import { BatchJobFactory } from './batch_job_factory';
import { defineBatchLogFactory } from './fabbrica';

export const BatchLogFactory = defineBatchLogFactory({
  defaultData: ({ seq }) => {
    return {
      batchLogId: Buffer.from(parse(uuidv7(undefined, undefined, seq))),
      batchJob: BatchJobFactory,
      message: `test-log-${seq}`,
      level: LogLevel.INFO,
      metadata: JSON.stringify({ test: true, sequence: seq }),
    };
  },
});
