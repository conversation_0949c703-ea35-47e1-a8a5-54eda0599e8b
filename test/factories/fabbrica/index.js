"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.defineReportSectionFactory = exports.defineReportFactory = exports.defineOwnerHorseFactory = exports.defineOwnerFactory = exports.defineInvitationFactory = exports.defineOrganizationOwnerHorseRelationFactory = exports.defineOrganizationOwnerFactory = exports.defineAiGenerateLogFactory = exports.defineEmailLogFactory = exports.defineHorseHandoverNoteFactory = exports.defineMasterHorseFactory = exports.defineHorseStatusFactory = exports.defineHorseStableHistoryFactory = exports.defineHorseFactory = exports.defineFurlongLineFactory = exports.defineTrainingCourseFactory = exports.defineTrainingCourseMasterFactory = exports.defineFacilityFactory = exports.defineBusinessTripHistoryHorseFactory = exports.defineBusinessTripHistoryFactory = exports.defineRacePlaceFactory = exports.defineHorseRaceRecapRecordFactory = exports.defineHorseRaceResultRecordFactory = exports.defineHorseShoeingInvoicePhotoFactory = exports.defineHorseShoeingRecordFactory = exports.defineHorseMedicalTreatmentAffectedAreaPhotoFactory = exports.defineHorseMedicalTreatmentInvoicePhotoFactory = exports.defineHorseMedicalTreatmentRecordFactory = exports.defineOrganizationFarrierFactory = exports.defineOrganizationVeterinarianFactory = exports.defineTrainingPartnerFactory = exports.defineHorseTrainingRecordFactory = exports.defineHorseBodyRecordFactory = exports.defineHorseBodyAffectedAreaPhotoFactory = exports.defineHorseBodyPhotoFactory = exports.defineHorseDailyRecordFactory = exports.defineMasterFarmFactory = exports.defineFarmAreaFactory = exports.defineContractFactory = exports.defineStaffFactory = exports.defineUserRoleFactory = exports.defineUserFactory = exports.defineStableStatusFactory = exports.defineStableFactory = exports.defineOrganizationFactory = exports.defineAssociationFactory = exports.initialize = exports.resetScalarFieldValueGenerator = exports.registerScalarFieldValueGenerator = exports.resetSequence = void 0;
exports.defineUserLangSettingFactory = exports.defineTrainersUserSettingsFactory = exports.defineTrainingMenuFactory = exports.defineHorseCoursePitchAverageFactory = exports.defineGaitAnalysisResultFactory = exports.defineTimeSeriesHeartBeatResultFactory = exports.defineTrainingIndicatorLabelFactory = exports.defineTrainingPeriodFactory = exports.defineTrainingIndicatorFactory = exports.defineTrainingFactory = exports.defineBatchLogFactory = exports.defineBatchJobFactory = exports.defineFeatureFlagFactory = exports.defineHorseNoteUserDeviceFactory = exports.defineUserTermsAcceptancesFactory = exports.defineOnetimeCodeFactory = exports.defineStableTmTransportOutHandoverNoteFactory = exports.defineStableTmTransportOutStatusFactory = exports.defineStableTmTransportInStatusFactory = exports.defineStableTmTransportRecordFactory = exports.defineStableTmTransportDailyRecordFactory = exports.defineStableTmTransportQueueTicketFactory = exports.defineStableTmFixedSlotFactory = exports.defineStableTmSectionFactory = exports.defineStableTmOutsideFarmFactory = exports.defineReportGenerateRequestFactory = exports.definePendingSendReportFactory = exports.defineShareReportFactory = exports.defineSentReportFactory = exports.defineReportSectionMedicalTreatmentAffectedAreaPhotoFactory = exports.defineReportSectionMedicalTreatmentRecordFactory = exports.defineReportSectionMedicalTreatmentFactory = exports.defineReportSectionMonthlyTimelineRecordFactory = exports.defineReportSectionMonthlyTimelineFactory = exports.defineReportSectionMonthlySummaryRaceRecordFactory = exports.defineReportSectionMonthlySummaryFactory = exports.defineReportSectionWorkoutConditionFactory = exports.defineReportSectionHorseConditionFactory = exports.defineReportSectionImageFactory = exports.defineReportSectionPlainTextFactory = void 0;
const internal_1 = require("@quramy/prisma-fabbrica/lib/internal");
var internal_2 = require("@quramy/prisma-fabbrica/lib/internal");
Object.defineProperty(exports, "resetSequence", { enumerable: true, get: function () { return internal_2.resetSequence; } });
Object.defineProperty(exports, "registerScalarFieldValueGenerator", { enumerable: true, get: function () { return internal_2.registerScalarFieldValueGenerator; } });
Object.defineProperty(exports, "resetScalarFieldValueGenerator", { enumerable: true, get: function () { return internal_2.resetScalarFieldValueGenerator; } });
const initializer = (0, internal_1.createInitializer)();
const { getClient } = initializer;
exports.initialize = initializer.initialize;
const modelFieldDefinitions = [{
        name: "Association",
        fields: [{
                name: "facilities",
                type: "Facility",
                relationName: "AssociationToFacility"
            }, {
                name: "organizations",
                type: "Organization",
                relationName: "AssociationToOrganization"
            }]
    }, {
        name: "Organization",
        fields: [{
                name: "facility",
                type: "Facility",
                relationName: "FacilityToOrganization"
            }, {
                name: "association",
                type: "Association",
                relationName: "AssociationToOrganization"
            }, {
                name: "stables",
                type: "Stable",
                relationName: "OrganizationToStable"
            }, {
                name: "userRoles",
                type: "UserRole",
                relationName: "OrganizationToUserRole"
            }, {
                name: "horses",
                type: "Horse",
                relationName: "HorseToOrganization"
            }, {
                name: "organizationOwners",
                type: "OrganizationOwner",
                relationName: "OrganizationToOrganizationOwner"
            }, {
                name: "reports",
                type: "Report",
                relationName: "OrganizationToReport"
            }, {
                name: "organizationVeterinarians",
                type: "OrganizationVeterinarian",
                relationName: "OrganizationToOrganizationVeterinarian"
            }, {
                name: "organizationFarriers",
                type: "OrganizationFarrier",
                relationName: "OrganizationToOrganizationFarrier"
            }, {
                name: "staffs",
                type: "Staff",
                relationName: "OrganizationToStaff"
            }, {
                name: "contracts",
                type: "Contract",
                relationName: "ContractToOrganization"
            }, {
                name: "featureFlag",
                type: "FeatureFlag",
                relationName: "FeatureFlagToOrganization"
            }, {
                name: "horseDailyRecords",
                type: "HorseDailyRecord",
                relationName: "HorseDailyRecordToOrganization"
            }, {
                name: "businessTripHistories",
                type: "BusinessTripHistory",
                relationName: "BusinessTripHistoryToOrganization"
            }, {
                name: "StableTmOutsideFarm",
                type: "StableTmOutsideFarm",
                relationName: "OrganizationToStableTmOutsideFarm"
            }]
    }, {
        name: "Stable",
        fields: [{
                name: "organization",
                type: "Organization",
                relationName: "OrganizationToStable"
            }, {
                name: "stableStatus",
                type: "StableStatus",
                relationName: "StableToStableStatus"
            }, {
                name: "userRoles",
                type: "UserRole",
                relationName: "StableToUserRole"
            }, {
                name: "horses",
                type: "Horse",
                relationName: "HorseToStable"
            }, {
                name: "horseStableHistories",
                type: "HorseStableHistory",
                relationName: "HorseStableHistoryToStable"
            }, {
                name: "staffs",
                type: "Staff",
                relationName: "StableToStaff"
            }, {
                name: "trainingMenus",
                type: "TrainingMenu",
                relationName: "StableToTrainingMenu"
            }, {
                name: "trainings",
                type: "Training",
                relationName: "StableToTraining"
            }, {
                name: "stableTmSections",
                type: "StableTmSection",
                relationName: "StableToStableTmSection"
            }, {
                name: "stableTmTransportDailyRecord",
                type: "StableTmTransportDailyRecord",
                relationName: "StableToStableTmTransportDailyRecord"
            }]
    }, {
        name: "StableStatus",
        fields: [{
                name: "stable",
                type: "Stable",
                relationName: "StableToStableStatus"
            }]
    }, {
        name: "User",
        fields: [{
                name: "userRoles",
                type: "UserRole",
                relationName: "UserToUserRole"
            }, {
                name: "staffs",
                type: "Staff",
                relationName: "StaffToUser"
            }, {
                name: "HorseNoteUserDevice",
                type: "HorseNoteUserDevice",
                relationName: "HorseNoteUserDeviceToUser"
            }, {
                name: "TrainersUserSettings",
                type: "TrainersUserSettings",
                relationName: "TrainersUserSettingsToUser"
            }, {
                name: "UserLangSettings",
                type: "UserLangSetting",
                relationName: "UserToUserLangSetting"
            }, {
                name: "horseDailyRecords",
                type: "HorseDailyRecord",
                relationName: "HorseDailyRecordToUser"
            }]
    }, {
        name: "UserRole",
        fields: [{
                name: "organization",
                type: "Organization",
                relationName: "OrganizationToUserRole"
            }, {
                name: "stable",
                type: "Stable",
                relationName: "StableToUserRole"
            }, {
                name: "user",
                type: "User",
                relationName: "UserToUserRole"
            }]
    }, {
        name: "Staff",
        fields: [{
                name: "stable",
                type: "Stable",
                relationName: "StableToStaff"
            }, {
                name: "organization",
                type: "Organization",
                relationName: "OrganizationToStaff"
            }, {
                name: "user",
                type: "User",
                relationName: "StaffToUser"
            }, {
                name: "horseRaceRecapRecords",
                type: "HorseRaceRecapRecord",
                relationName: "HorseRaceRecapRecordToStaff"
            }, {
                name: "horseTrainingRecords",
                type: "HorseTrainingRecord",
                relationName: "HorseTrainingRecordToStaff"
            }, {
                name: "StableTmTransportOutStatus",
                type: "StableTmTransportOutStatus",
                relationName: "StableTmTransportOutStatusToStaff"
            }, {
                name: "StableTmTransportInStatus",
                type: "StableTmTransportInStatus",
                relationName: "StableTmTransportInStatusToStaff"
            }]
    }, {
        name: "Contract",
        fields: [{
                name: "organization",
                type: "Organization",
                relationName: "ContractToOrganization"
            }]
    }, {
        name: "FarmArea",
        fields: [{
                name: "masterFarms",
                type: "MasterFarm",
                relationName: "FarmAreaToMasterFarm"
            }, {
                name: "StableTmOutsideFarm",
                type: "StableTmOutsideFarm",
                relationName: "FarmAreaToStableTmOutsideFarm"
            }]
    }, {
        name: "MasterFarm",
        fields: [{
                name: "farmArea",
                type: "FarmArea",
                relationName: "FarmAreaToMasterFarm"
            }, {
                name: "stableTmOutsideFarms",
                type: "StableTmOutsideFarm",
                relationName: "MasterFarmToStableTmOutsideFarm"
            }]
    }, {
        name: "HorseDailyRecord",
        fields: [{
                name: "organization",
                type: "Organization",
                relationName: "HorseDailyRecordToOrganization"
            }, {
                name: "horse",
                type: "Horse",
                relationName: "HorseToHorseDailyRecord"
            }, {
                name: "createdUser",
                type: "User",
                relationName: "HorseDailyRecordToUser"
            }, {
                name: "horseBodyPhotos",
                type: "HorseBodyPhoto",
                relationName: "HorseBodyPhotoToHorseDailyRecord"
            }, {
                name: "horseBodyRecord",
                type: "HorseBodyRecord",
                relationName: "HorseBodyRecordToHorseDailyRecord"
            }, {
                name: "horseRaceResultRecord",
                type: "HorseRaceResultRecord",
                relationName: "HorseDailyRecordToHorseRaceResultRecord"
            }, {
                name: "horseRaceRecapRecord",
                type: "HorseRaceRecapRecord",
                relationName: "HorseDailyRecordToHorseRaceRecapRecord"
            }, {
                name: "horseShoeingRecords",
                type: "HorseShoeingRecord",
                relationName: "HorseDailyRecordToHorseShoeingRecord"
            }, {
                name: "horseMedicalTreatmentRecords",
                type: "HorseMedicalTreatmentRecord",
                relationName: "HorseDailyRecordToHorseMedicalTreatmentRecord"
            }, {
                name: "horseTrainingRecords",
                type: "HorseTrainingRecord",
                relationName: "HorseDailyRecordToHorseTrainingRecord"
            }, {
                name: "horseBodyAffectedAreaPhotos",
                type: "HorseBodyAffectedAreaPhoto",
                relationName: "HorseBodyAffectedAreaPhotoToHorseDailyRecord"
            }]
    }, {
        name: "HorseBodyPhoto",
        fields: [{
                name: "horseDailyRecord",
                type: "HorseDailyRecord",
                relationName: "HorseBodyPhotoToHorseDailyRecord"
            }]
    }, {
        name: "HorseBodyAffectedAreaPhoto",
        fields: [{
                name: "horseDailyRecord",
                type: "HorseDailyRecord",
                relationName: "HorseBodyAffectedAreaPhotoToHorseDailyRecord"
            }]
    }, {
        name: "HorseBodyRecord",
        fields: [{
                name: "horseDailyRecord",
                type: "HorseDailyRecord",
                relationName: "HorseBodyRecordToHorseDailyRecord"
            }]
    }, {
        name: "HorseTrainingRecord",
        fields: [{
                name: "horseDailyRecord",
                type: "HorseDailyRecord",
                relationName: "HorseDailyRecordToHorseTrainingRecord"
            }, {
                name: "rider",
                type: "Staff",
                relationName: "HorseTrainingRecordToStaff"
            }, {
                name: "trainingMenu",
                type: "TrainingMenu",
                relationName: "HorseTrainingRecordToTrainingMenu"
            }, {
                name: "facility",
                type: "Facility",
                relationName: "FacilityToHorseTrainingRecord"
            }, {
                name: "trainingCourse",
                type: "TrainingCourse",
                relationName: "HorseTrainingRecordToTrainingCourse"
            }, {
                name: "trainingPartners",
                type: "TrainingPartner",
                relationName: "HorseTrainingRecordToTrainingPartner"
            }]
    }, {
        name: "TrainingPartner",
        fields: [{
                name: "horseTrainingRecord",
                type: "HorseTrainingRecord",
                relationName: "HorseTrainingRecordToTrainingPartner"
            }, {
                name: "horse",
                type: "Horse",
                relationName: "HorseToTrainingPartner"
            }]
    }, {
        name: "OrganizationVeterinarian",
        fields: [{
                name: "horseMedicalTreatmentRecords",
                type: "HorseMedicalTreatmentRecord",
                relationName: "HorseMedicalTreatmentRecordToOrganizationVeterinarian"
            }, {
                name: "organization",
                type: "Organization",
                relationName: "OrganizationToOrganizationVeterinarian"
            }]
    }, {
        name: "OrganizationFarrier",
        fields: [{
                name: "horseShoeingRecords",
                type: "HorseShoeingRecord",
                relationName: "HorseShoeingRecordToOrganizationFarrier"
            }, {
                name: "organization",
                type: "Organization",
                relationName: "OrganizationToOrganizationFarrier"
            }]
    }, {
        name: "HorseMedicalTreatmentRecord",
        fields: [{
                name: "horseDailyRecord",
                type: "HorseDailyRecord",
                relationName: "HorseDailyRecordToHorseMedicalTreatmentRecord"
            }, {
                name: "veterinarian",
                type: "OrganizationVeterinarian",
                relationName: "HorseMedicalTreatmentRecordToOrganizationVeterinarian"
            }, {
                name: "horseMedicalTreatmentInvoicePhotos",
                type: "HorseMedicalTreatmentInvoicePhoto",
                relationName: "HorseMedicalTreatmentInvoicePhotoToHorseMedicalTreatmentRecord"
            }, {
                name: "horseMedicalTreatmentAffectedAreaPhotos",
                type: "HorseMedicalTreatmentAffectedAreaPhoto",
                relationName: "HorseMedicalTreatmentAffectedAreaPhotoToHorseMedicalTreatmentRecord"
            }]
    }, {
        name: "HorseMedicalTreatmentInvoicePhoto",
        fields: [{
                name: "horseMedicalTreatmentRecord",
                type: "HorseMedicalTreatmentRecord",
                relationName: "HorseMedicalTreatmentInvoicePhotoToHorseMedicalTreatmentRecord"
            }]
    }, {
        name: "HorseMedicalTreatmentAffectedAreaPhoto",
        fields: [{
                name: "horseMedicalTreatmentRecord",
                type: "HorseMedicalTreatmentRecord",
                relationName: "HorseMedicalTreatmentAffectedAreaPhotoToHorseMedicalTreatmentRecord"
            }]
    }, {
        name: "HorseShoeingRecord",
        fields: [{
                name: "horseDailyRecord",
                type: "HorseDailyRecord",
                relationName: "HorseDailyRecordToHorseShoeingRecord"
            }, {
                name: "farrier",
                type: "OrganizationFarrier",
                relationName: "HorseShoeingRecordToOrganizationFarrier"
            }, {
                name: "horseShoeingInvoicePhotos",
                type: "HorseShoeingInvoicePhoto",
                relationName: "HorseShoeingInvoicePhotoToHorseShoeingRecord"
            }]
    }, {
        name: "HorseShoeingInvoicePhoto",
        fields: [{
                name: "horseShoeingRecord",
                type: "HorseShoeingRecord",
                relationName: "HorseShoeingInvoicePhotoToHorseShoeingRecord"
            }]
    }, {
        name: "HorseRaceResultRecord",
        fields: [{
                name: "horseDailyRecord",
                type: "HorseDailyRecord",
                relationName: "HorseDailyRecordToHorseRaceResultRecord"
            }, {
                name: "racePlace",
                type: "RacePlace",
                relationName: "HorseRaceResultRecordToRacePlace"
            }]
    }, {
        name: "HorseRaceRecapRecord",
        fields: [{
                name: "horseDailyRecord",
                type: "HorseDailyRecord",
                relationName: "HorseDailyRecordToHorseRaceRecapRecord"
            }, {
                name: "staff",
                type: "Staff",
                relationName: "HorseRaceRecapRecordToStaff"
            }]
    }, {
        name: "RacePlace",
        fields: [{
                name: "horseRaceResultRecords",
                type: "HorseRaceResultRecord",
                relationName: "HorseRaceResultRecordToRacePlace"
            }]
    }, {
        name: "BusinessTripHistory",
        fields: [{
                name: "organization",
                type: "Organization",
                relationName: "BusinessTripHistoryToOrganization"
            }, {
                name: "horses",
                type: "BusinessTripHistoryHorse",
                relationName: "BusinessTripHistoryToBusinessTripHistoryHorse"
            }]
    }, {
        name: "BusinessTripHistoryHorse",
        fields: [{
                name: "businessTripHistory",
                type: "BusinessTripHistory",
                relationName: "BusinessTripHistoryToBusinessTripHistoryHorse"
            }, {
                name: "horse",
                type: "Horse",
                relationName: "BusinessTripHistoryHorseToHorse"
            }]
    }, {
        name: "Facility",
        fields: [{
                name: "association",
                type: "Association",
                relationName: "AssociationToFacility"
            }, {
                name: "trainingCourseMasters",
                type: "TrainingCourseMaster",
                relationName: "FacilityToTrainingCourseMaster"
            }, {
                name: "reportSectionWorkoutConditions",
                type: "ReportSectionWorkoutCondition",
                relationName: "FacilityToReportSectionWorkoutCondition"
            }, {
                name: "horseTrainingRecords",
                type: "HorseTrainingRecord",
                relationName: "FacilityToHorseTrainingRecord"
            }, {
                name: "organizations",
                type: "Organization",
                relationName: "FacilityToOrganization"
            }]
    }, {
        name: "TrainingCourseMaster",
        fields: [{
                name: "trainingCourses",
                type: "TrainingCourse",
                relationName: "TrainingCourseToTrainingCourseMaster"
            }, {
                name: "facility",
                type: "Facility",
                relationName: "FacilityToTrainingCourseMaster"
            }]
    }, {
        name: "TrainingCourse",
        fields: [{
                name: "trainingCourseMaster",
                type: "TrainingCourseMaster",
                relationName: "TrainingCourseToTrainingCourseMaster"
            }, {
                name: "reportSectionWorkoutConditions",
                type: "ReportSectionWorkoutCondition",
                relationName: "ReportSectionWorkoutConditionToTrainingCourse"
            }, {
                name: "horseTrainingRecords",
                type: "HorseTrainingRecord",
                relationName: "HorseTrainingRecordToTrainingCourse"
            }, {
                name: "HorseCoursePitchAverage",
                type: "HorseCoursePitchAverage",
                relationName: "HorseCoursePitchAverageToTrainingCourse"
            }, {
                name: "TrainingPeriod",
                type: "TrainingPeriod",
                relationName: "TrainingCourseToTrainingPeriod"
            }]
    }, {
        name: "FurlongLine",
        fields: []
    }, {
        name: "Horse",
        fields: [{
                name: "stable",
                type: "Stable",
                relationName: "HorseToStable"
            }, {
                name: "organization",
                type: "Organization",
                relationName: "HorseToOrganization"
            }, {
                name: "masterHorse",
                type: "MasterHorse",
                relationName: "HorseToMasterHorse"
            }, {
                name: "horseStableHistories",
                type: "HorseStableHistory",
                relationName: "HorseToHorseStableHistory"
            }, {
                name: "organizationOwnerHorseRelations",
                type: "OrganizationOwnerHorseRelation",
                relationName: "HorseToOrganizationOwnerHorseRelation"
            }, {
                name: "reports",
                type: "Report",
                relationName: "HorseToReport"
            }, {
                name: "pendingSendReports",
                type: "PendingSendReport",
                relationName: "HorseToPendingSendReport"
            }, {
                name: "horseDailyRecords",
                type: "HorseDailyRecord",
                relationName: "HorseToHorseDailyRecord"
            }, {
                name: "horseHandoverNotes",
                type: "HorseHandoverNote",
                relationName: "HorseToHorseHandoverNote"
            }, {
                name: "trainingPartners",
                type: "TrainingPartner",
                relationName: "HorseToTrainingPartner"
            }, {
                name: "ReportGenerateRequest",
                type: "ReportGenerateRequest",
                relationName: "HorseToReportGenerateRequest"
            }, {
                name: "trainings",
                type: "Training",
                relationName: "HorseToTraining"
            }, {
                name: "businessTripHistories",
                type: "BusinessTripHistoryHorse",
                relationName: "BusinessTripHistoryHorseToHorse"
            }, {
                name: "StableTmTransportRecord",
                type: "StableTmTransportRecord",
                relationName: "HorseToStableTmTransportRecord"
            }, {
                name: "horseStatus",
                type: "HorseStatus",
                relationName: "HorseToHorseStatus"
            }]
    }, {
        name: "HorseStableHistory",
        fields: [{
                name: "horse",
                type: "Horse",
                relationName: "HorseToHorseStableHistory"
            }, {
                name: "stable",
                type: "Stable",
                relationName: "HorseStableHistoryToStable"
            }]
    }, {
        name: "HorseStatus",
        fields: [{
                name: "horse",
                type: "Horse",
                relationName: "HorseToHorseStatus"
            }, {
                name: "outsideFarm",
                type: "StableTmOutsideFarm",
                relationName: "HorseStatusToStableTmOutsideFarm"
            }]
    }, {
        name: "MasterHorse",
        fields: [{
                name: "horses",
                type: "Horse",
                relationName: "HorseToMasterHorse"
            }, {
                name: "ownerHorses",
                type: "OwnerHorse",
                relationName: "MasterHorseToOwnerHorse"
            }, {
                name: "HorseCoursePitchAverage",
                type: "HorseCoursePitchAverage",
                relationName: "HorseCoursePitchAverageToMasterHorse"
            }]
    }, {
        name: "HorseHandoverNote",
        fields: [{
                name: "horse",
                type: "Horse",
                relationName: "HorseToHorseHandoverNote"
            }]
    }, {
        name: "EmailLog",
        fields: []
    }, {
        name: "AiGenerateLog",
        fields: [{
                name: "reportGenerateRequests",
                type: "ReportGenerateRequest",
                relationName: "AiGenerateLogToReportGenerateRequest"
            }]
    }, {
        name: "OrganizationOwner",
        fields: [{
                name: "organization",
                type: "Organization",
                relationName: "OrganizationToOrganizationOwner"
            }, {
                name: "owner",
                type: "Owner",
                relationName: "OrganizationOwnerToOwner"
            }, {
                name: "invitations",
                type: "Invitation",
                relationName: "InvitationToOrganizationOwner"
            }, {
                name: "organizationOwnerHorseRelations",
                type: "OrganizationOwnerHorseRelation",
                relationName: "OrganizationOwnerToOrganizationOwnerHorseRelation"
            }, {
                name: "sentReports",
                type: "SentReport",
                relationName: "OrganizationOwnerToSentReport"
            }, {
                name: "pendingSendReports",
                type: "PendingSendReport",
                relationName: "OrganizationOwnerToPendingSendReport"
            }]
    }, {
        name: "OrganizationOwnerHorseRelation",
        fields: [{
                name: "organizationOwner",
                type: "OrganizationOwner",
                relationName: "OrganizationOwnerToOrganizationOwnerHorseRelation"
            }, {
                name: "horse",
                type: "Horse",
                relationName: "HorseToOrganizationOwnerHorseRelation"
            }]
    }, {
        name: "Invitation",
        fields: [{
                name: "organizationOwner",
                type: "OrganizationOwner",
                relationName: "InvitationToOrganizationOwner"
            }]
    }, {
        name: "Owner",
        fields: [{
                name: "organizationOwners",
                type: "OrganizationOwner",
                relationName: "OrganizationOwnerToOwner"
            }, {
                name: "ownerHorses",
                type: "OwnerHorse",
                relationName: "OwnerToOwnerHorse"
            }, {
                name: "userTermsAcceptances",
                type: "UserTermsAcceptances",
                relationName: "OwnerToUserTermsAcceptances"
            }]
    }, {
        name: "OwnerHorse",
        fields: [{
                name: "owner",
                type: "Owner",
                relationName: "OwnerToOwnerHorse"
            }, {
                name: "masterHorse",
                type: "MasterHorse",
                relationName: "MasterHorseToOwnerHorse"
            }, {
                name: "sentReports",
                type: "SentReport",
                relationName: "OwnerHorseToSentReport"
            }]
    }, {
        name: "Report",
        fields: [{
                name: "horse",
                type: "Horse",
                relationName: "HorseToReport"
            }, {
                name: "organization",
                type: "Organization",
                relationName: "OrganizationToReport"
            }, {
                name: "reportSections",
                type: "ReportSection",
                relationName: "ReportToReportSection"
            }, {
                name: "sentReports",
                type: "SentReport",
                relationName: "ReportToSentReport"
            }, {
                name: "pendingSendReports",
                type: "PendingSendReport",
                relationName: "PendingSendReportToReport"
            }, {
                name: "reportGenerateRequest",
                type: "ReportGenerateRequest",
                relationName: "ReportToReportGenerateRequest"
            }, {
                name: "shareReport",
                type: "ShareReport",
                relationName: "ReportToShareReport"
            }]
    }, {
        name: "ReportSection",
        fields: [{
                name: "report",
                type: "Report",
                relationName: "ReportToReportSection"
            }, {
                name: "reportSectionHorseConditions",
                type: "ReportSectionHorseCondition",
                relationName: "ReportSectionToReportSectionHorseCondition"
            }, {
                name: "reportSectionPlainTexts",
                type: "ReportSectionPlainText",
                relationName: "ReportSectionToReportSectionPlainText"
            }, {
                name: "reportSectionImages",
                type: "ReportSectionImage",
                relationName: "ReportSectionToReportSectionImage"
            }, {
                name: "reportSectionWorkoutConditions",
                type: "ReportSectionWorkoutCondition",
                relationName: "ReportSectionToReportSectionWorkoutCondition"
            }, {
                name: "reportSectionMonthlySummaries",
                type: "ReportSectionMonthlySummary",
                relationName: "ReportSectionToReportSectionMonthlySummary"
            }, {
                name: "reportSectionMonthlyTimelines",
                type: "ReportSectionMonthlyTimeline",
                relationName: "ReportSectionToReportSectionMonthlyTimeline"
            }, {
                name: "reportSectionMedicalTreatments",
                type: "ReportSectionMedicalTreatment",
                relationName: "ReportSectionToReportSectionMedicalTreatment"
            }]
    }, {
        name: "ReportSectionPlainText",
        fields: [{
                name: "reportSection",
                type: "ReportSection",
                relationName: "ReportSectionToReportSectionPlainText"
            }]
    }, {
        name: "ReportSectionImage",
        fields: [{
                name: "reportSection",
                type: "ReportSection",
                relationName: "ReportSectionToReportSectionImage"
            }]
    }, {
        name: "ReportSectionHorseCondition",
        fields: [{
                name: "reportSection",
                type: "ReportSection",
                relationName: "ReportSectionToReportSectionHorseCondition"
            }]
    }, {
        name: "ReportSectionWorkoutCondition",
        fields: [{
                name: "reportSection",
                type: "ReportSection",
                relationName: "ReportSectionToReportSectionWorkoutCondition"
            }, {
                name: "facility",
                type: "Facility",
                relationName: "FacilityToReportSectionWorkoutCondition"
            }, {
                name: "trainingCourse",
                type: "TrainingCourse",
                relationName: "ReportSectionWorkoutConditionToTrainingCourse"
            }]
    }, {
        name: "ReportSectionMonthlySummary",
        fields: [{
                name: "reportSection",
                type: "ReportSection",
                relationName: "ReportSectionToReportSectionMonthlySummary"
            }, {
                name: "reportSectionMonthlySummaryRaceRecords",
                type: "ReportSectionMonthlySummaryRaceRecord",
                relationName: "ReportSectionMonthlySummaryToReportSectionMonthlySummaryRaceRecord"
            }]
    }, {
        name: "ReportSectionMonthlySummaryRaceRecord",
        fields: [{
                name: "reportSectionMonthlySummary",
                type: "ReportSectionMonthlySummary",
                relationName: "ReportSectionMonthlySummaryToReportSectionMonthlySummaryRaceRecord"
            }]
    }, {
        name: "ReportSectionMonthlyTimeline",
        fields: [{
                name: "reportSection",
                type: "ReportSection",
                relationName: "ReportSectionToReportSectionMonthlyTimeline"
            }, {
                name: "reportSectionMonthlyTimelineRecords",
                type: "ReportSectionMonthlyTimelineRecord",
                relationName: "ReportSectionMonthlyTimelineToReportSectionMonthlyTimelineRecord"
            }]
    }, {
        name: "ReportSectionMonthlyTimelineRecord",
        fields: [{
                name: "reportSectionMonthlyTimeline",
                type: "ReportSectionMonthlyTimeline",
                relationName: "ReportSectionMonthlyTimelineToReportSectionMonthlyTimelineRecord"
            }]
    }, {
        name: "ReportSectionMedicalTreatment",
        fields: [{
                name: "reportSection",
                type: "ReportSection",
                relationName: "ReportSectionToReportSectionMedicalTreatment"
            }, {
                name: "reportSectionMedicalTreatmentRecords",
                type: "ReportSectionMedicalTreatmentRecord",
                relationName: "ReportSectionMedicalTreatmentToReportSectionMedicalTreatmentRecord"
            }]
    }, {
        name: "ReportSectionMedicalTreatmentRecord",
        fields: [{
                name: "reportSectionMedicalTreatment",
                type: "ReportSectionMedicalTreatment",
                relationName: "ReportSectionMedicalTreatmentToReportSectionMedicalTreatmentRecord"
            }, {
                name: "reportSectionMedicalTreatmentAffectedAreaPhotos",
                type: "ReportSectionMedicalTreatmentAffectedAreaPhoto",
                relationName: "ReportSectionMedicalTreatmentAffectedAreaPhotoToReportSectionMedicalTreatmentRecord"
            }]
    }, {
        name: "ReportSectionMedicalTreatmentAffectedAreaPhoto",
        fields: [{
                name: "reportSectionMedicalTreatmentRecord",
                type: "ReportSectionMedicalTreatmentRecord",
                relationName: "ReportSectionMedicalTreatmentAffectedAreaPhotoToReportSectionMedicalTreatmentRecord"
            }]
    }, {
        name: "SentReport",
        fields: [{
                name: "report",
                type: "Report",
                relationName: "ReportToSentReport"
            }, {
                name: "organizationOwner",
                type: "OrganizationOwner",
                relationName: "OrganizationOwnerToSentReport"
            }, {
                name: "ownerHorse",
                type: "OwnerHorse",
                relationName: "OwnerHorseToSentReport"
            }]
    }, {
        name: "ShareReport",
        fields: [{
                name: "report",
                type: "Report",
                relationName: "ReportToShareReport"
            }]
    }, {
        name: "PendingSendReport",
        fields: [{
                name: "report",
                type: "Report",
                relationName: "PendingSendReportToReport"
            }, {
                name: "organizationOwner",
                type: "OrganizationOwner",
                relationName: "OrganizationOwnerToPendingSendReport"
            }, {
                name: "horse",
                type: "Horse",
                relationName: "HorseToPendingSendReport"
            }]
    }, {
        name: "ReportGenerateRequest",
        fields: [{
                name: "horse",
                type: "Horse",
                relationName: "HorseToReportGenerateRequest"
            }, {
                name: "report",
                type: "Report",
                relationName: "ReportToReportGenerateRequest"
            }, {
                name: "aiGenerateLog",
                type: "AiGenerateLog",
                relationName: "AiGenerateLogToReportGenerateRequest"
            }]
    }, {
        name: "StableTmOutsideFarm",
        fields: [{
                name: "organization",
                type: "Organization",
                relationName: "OrganizationToStableTmOutsideFarm"
            }, {
                name: "farmArea",
                type: "FarmArea",
                relationName: "FarmAreaToStableTmOutsideFarm"
            }, {
                name: "masterFarm",
                type: "MasterFarm",
                relationName: "MasterFarmToStableTmOutsideFarm"
            }, {
                name: "StableTmTransportOutStatus",
                type: "StableTmTransportOutStatus",
                relationName: "StableTmOutsideFarmToStableTmTransportOutStatus"
            }, {
                name: "HorseStatus",
                type: "HorseStatus",
                relationName: "HorseStatusToStableTmOutsideFarm"
            }]
    }, {
        name: "StableTmSection",
        fields: [{
                name: "stable",
                type: "Stable",
                relationName: "StableToStableTmSection"
            }, {
                name: "transportQueueTickets",
                type: "StableTmTransportQueueTicket",
                relationName: "StableTmSectionToStableTmTransportQueueTicket"
            }, {
                name: "fixedSlots",
                type: "StableTmFixedSlot",
                relationName: "StableTmFixedSlotToStableTmSection"
            }, {
                name: "transportDailyRecords",
                type: "StableTmTransportDailyRecord",
                relationName: "StableTmSectionToStableTmTransportDailyRecord"
            }]
    }, {
        name: "StableTmFixedSlot",
        fields: [{
                name: "section",
                type: "StableTmSection",
                relationName: "StableTmFixedSlotToStableTmSection"
            }]
    }, {
        name: "StableTmTransportQueueTicket",
        fields: [{
                name: "section",
                type: "StableTmSection",
                relationName: "StableTmSectionToStableTmTransportQueueTicket"
            }]
    }, {
        name: "StableTmTransportDailyRecord",
        fields: [{
                name: "section",
                type: "StableTmSection",
                relationName: "StableTmSectionToStableTmTransportDailyRecord"
            }, {
                name: "stable",
                type: "Stable",
                relationName: "StableToStableTmTransportDailyRecord"
            }, {
                name: "transportRecords",
                type: "StableTmTransportRecord",
                relationName: "StableTmTransportDailyRecordToStableTmTransportRecord"
            }]
    }, {
        name: "StableTmTransportRecord",
        fields: [{
                name: "transportDailyRecord",
                type: "StableTmTransportDailyRecord",
                relationName: "StableTmTransportDailyRecordToStableTmTransportRecord"
            }, {
                name: "horse",
                type: "Horse",
                relationName: "HorseToStableTmTransportRecord"
            }, {
                name: "transportInStatus",
                type: "StableTmTransportInStatus",
                relationName: "StableTmTransportInStatusToStableTmTransportRecord"
            }, {
                name: "transportOutStatus",
                type: "StableTmTransportOutStatus",
                relationName: "StableTmTransportOutStatusToStableTmTransportRecord"
            }]
    }, {
        name: "StableTmTransportInStatus",
        fields: [{
                name: "transportRecord",
                type: "StableTmTransportRecord",
                relationName: "StableTmTransportInStatusToStableTmTransportRecord"
            }, {
                name: "staff",
                type: "Staff",
                relationName: "StableTmTransportInStatusToStaff"
            }]
    }, {
        name: "StableTmTransportOutStatus",
        fields: [{
                name: "transportRecord",
                type: "StableTmTransportRecord",
                relationName: "StableTmTransportOutStatusToStableTmTransportRecord"
            }, {
                name: "staff",
                type: "Staff",
                relationName: "StableTmTransportOutStatusToStaff"
            }, {
                name: "farm",
                type: "StableTmOutsideFarm",
                relationName: "StableTmOutsideFarmToStableTmTransportOutStatus"
            }, {
                name: "transportOutHandoverNotes",
                type: "StableTmTransportOutHandoverNote",
                relationName: "StableTmTransportOutHandoverNoteToStableTmTransportOutStatus"
            }]
    }, {
        name: "StableTmTransportOutHandoverNote",
        fields: [{
                name: "transportOutStatus",
                type: "StableTmTransportOutStatus",
                relationName: "StableTmTransportOutHandoverNoteToStableTmTransportOutStatus"
            }]
    }, {
        name: "OnetimeCode",
        fields: []
    }, {
        name: "UserTermsAcceptances",
        fields: [{
                name: "owner",
                type: "Owner",
                relationName: "OwnerToUserTermsAcceptances"
            }]
    }, {
        name: "HorseNoteUserDevice",
        fields: [{
                name: "user",
                type: "User",
                relationName: "HorseNoteUserDeviceToUser"
            }]
    }, {
        name: "FeatureFlag",
        fields: [{
                name: "organization",
                type: "Organization",
                relationName: "FeatureFlagToOrganization"
            }]
    }, {
        name: "BatchJob",
        fields: [{
                name: "BatchLog",
                type: "BatchLog",
                relationName: "BatchJobToBatchLog"
            }]
    }, {
        name: "BatchLog",
        fields: [{
                name: "batchJob",
                type: "BatchJob",
                relationName: "BatchJobToBatchLog"
            }]
    }, {
        name: "Training",
        fields: [{
                name: "timeSeriesHeartBeatResults",
                type: "TimeSeriesHeartBeatResult",
                relationName: "TimeSeriesHeartBeatResultToTraining"
            }, {
                name: "trainingIndicators",
                type: "TrainingIndicator",
                relationName: "TrainingToTrainingIndicator"
            }, {
                name: "trainingPeriods",
                type: "TrainingPeriod",
                relationName: "TrainingToTrainingPeriod"
            }, {
                name: "gaitAnalysisResult",
                type: "GaitAnalysisResult",
                relationName: "GaitAnalysisResultToTraining"
            }, {
                name: "horse",
                type: "Horse",
                relationName: "HorseToTraining"
            }, {
                name: "stable",
                type: "Stable",
                relationName: "StableToTraining"
            }]
    }, {
        name: "TrainingIndicator",
        fields: [{
                name: "training",
                type: "Training",
                relationName: "TrainingToTrainingIndicator"
            }, {
                name: "TrainingIndicatorLabel",
                type: "TrainingIndicatorLabel",
                relationName: "TrainingIndicatorToTrainingIndicatorLabel"
            }]
    }, {
        name: "TrainingPeriod",
        fields: [{
                name: "training",
                type: "Training",
                relationName: "TrainingToTrainingPeriod"
            }, {
                name: "trainingCourse",
                type: "TrainingCourse",
                relationName: "TrainingCourseToTrainingPeriod"
            }]
    }, {
        name: "TrainingIndicatorLabel",
        fields: [{
                name: "trainingIndicator",
                type: "TrainingIndicator",
                relationName: "TrainingIndicatorToTrainingIndicatorLabel"
            }]
    }, {
        name: "TimeSeriesHeartBeatResult",
        fields: [{
                name: "training",
                type: "Training",
                relationName: "TimeSeriesHeartBeatResultToTraining"
            }]
    }, {
        name: "GaitAnalysisResult",
        fields: [{
                name: "training",
                type: "Training",
                relationName: "GaitAnalysisResultToTraining"
            }]
    }, {
        name: "HorseCoursePitchAverage",
        fields: [{
                name: "masterHorse",
                type: "MasterHorse",
                relationName: "HorseCoursePitchAverageToMasterHorse"
            }, {
                name: "course",
                type: "TrainingCourse",
                relationName: "HorseCoursePitchAverageToTrainingCourse"
            }]
    }, {
        name: "TrainingMenu",
        fields: [{
                name: "stable",
                type: "Stable",
                relationName: "StableToTrainingMenu"
            }, {
                name: "horseTrainingRecords",
                type: "HorseTrainingRecord",
                relationName: "HorseTrainingRecordToTrainingMenu"
            }]
    }, {
        name: "TrainersUserSettings",
        fields: [{
                name: "user",
                type: "User",
                relationName: "TrainersUserSettingsToUser"
            }]
    }, {
        name: "UserLangSetting",
        fields: [{
                name: "user",
                type: "User",
                relationName: "UserToUserLangSetting"
            }]
    }];
function autoGenerateAssociationScalarsOrEnums({ seq }) {
    return {
        associationId: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "Association", fieldName: "associationId", isId: true, isUnique: false, seq }),
        associationName: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "Association", fieldName: "associationName", isId: false, isUnique: false, seq })
    };
}
function defineAssociationFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("Association", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateAssociationScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver ?? {});
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {};
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            associationId: inputData.associationId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().association.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "Association",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link Association} model.
 *
 * @param options
 * @returns factory {@link AssociationFactoryInterface}
 */
exports.defineAssociationFactory = ((options) => {
    return defineAssociationFactoryInternal(options ?? {}, {});
});
exports.defineAssociationFactory.withTransientFields = defaultTransientFieldValues => options => defineAssociationFactoryInternal(options ?? {}, defaultTransientFieldValues);
function isOrganizationfacilityFactory(x) {
    return x?._factoryFor === "Facility";
}
function isOrganizationassociationFactory(x) {
    return x?._factoryFor === "Association";
}
function isOrganizationfeatureFlagFactory(x) {
    return x?._factoryFor === "FeatureFlag";
}
function autoGenerateOrganizationScalarsOrEnums({ seq }) {
    return {
        organizationUuid: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "Organization", fieldName: "organizationUuid", isId: true, isUnique: false, seq }),
        name: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "Organization", fieldName: "name", isId: false, isUnique: false, seq })
    };
}
function defineOrganizationFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("Organization", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateOrganizationScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver ?? {});
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                facility: isOrganizationfacilityFactory(defaultData.facility) ? {
                    create: await defaultData.facility.build()
                } : defaultData.facility,
                association: isOrganizationassociationFactory(defaultData.association) ? {
                    create: await defaultData.association.build()
                } : defaultData.association,
                featureFlag: isOrganizationfeatureFlagFactory(defaultData.featureFlag) ? {
                    create: await defaultData.featureFlag.build()
                } : defaultData.featureFlag
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            organizationUuid: inputData.organizationUuid
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().organization.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "Organization",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link Organization} model.
 *
 * @param options
 * @returns factory {@link OrganizationFactoryInterface}
 */
exports.defineOrganizationFactory = ((options) => {
    return defineOrganizationFactoryInternal(options ?? {}, {});
});
exports.defineOrganizationFactory.withTransientFields = defaultTransientFieldValues => options => defineOrganizationFactoryInternal(options ?? {}, defaultTransientFieldValues);
function isStableorganizationFactory(x) {
    return x?._factoryFor === "Organization";
}
function isStablestableStatusFactory(x) {
    return x?._factoryFor === "StableStatus";
}
function autoGenerateStableScalarsOrEnums({ seq }) {
    return {
        stableUuid: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "Stable", fieldName: "stableUuid", isId: true, isUnique: false, seq }),
        name: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "Stable", fieldName: "name", isId: false, isUnique: false, seq })
    };
}
function defineStableFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("Stable", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateStableScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                organization: isStableorganizationFactory(defaultData.organization) ? {
                    create: await defaultData.organization.build()
                } : defaultData.organization,
                stableStatus: isStablestableStatusFactory(defaultData.stableStatus) ? {
                    create: await defaultData.stableStatus.build()
                } : defaultData.stableStatus
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            stableUuid: inputData.stableUuid
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().stable.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "Stable",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link Stable} model.
 *
 * @param options
 * @returns factory {@link StableFactoryInterface}
 */
exports.defineStableFactory = ((options) => {
    return defineStableFactoryInternal(options, {});
});
exports.defineStableFactory.withTransientFields = defaultTransientFieldValues => options => defineStableFactoryInternal(options, defaultTransientFieldValues);
function isStableStatusstableFactory(x) {
    return x?._factoryFor === "Stable";
}
function autoGenerateStableStatusScalarsOrEnums({ seq }) {
    return {
        stallNum: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "StableStatus", fieldName: "stallNum", isId: false, isUnique: false, seq })
    };
}
function defineStableStatusFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("StableStatus", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateStableStatusScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver ?? {});
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                stable: isStableStatusstableFactory(defaultData.stable) ? {
                    create: await defaultData.stable.build()
                } : defaultData.stable
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            stableStatusId: inputData.stableStatusId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().stableStatus.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "StableStatus",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link StableStatus} model.
 *
 * @param options
 * @returns factory {@link StableStatusFactoryInterface}
 */
exports.defineStableStatusFactory = ((options) => {
    return defineStableStatusFactoryInternal(options ?? {}, {});
});
exports.defineStableStatusFactory.withTransientFields = defaultTransientFieldValues => options => defineStableStatusFactoryInternal(options ?? {}, defaultTransientFieldValues);
function autoGenerateUserScalarsOrEnums({ seq }) {
    return {
        userUuid: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "User", fieldName: "userUuid", isId: true, isUnique: false, seq }),
        firebaseUid: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "User", fieldName: "firebaseUid", isId: false, isUnique: true, seq }),
        firstName: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "User", fieldName: "firstName", isId: false, isUnique: false, seq }),
        lastName: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "User", fieldName: "lastName", isId: false, isUnique: false, seq })
    };
}
function defineUserFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("User", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateUserScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver ?? {});
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {};
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            userUuid: inputData.userUuid
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().user.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "User",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link User} model.
 *
 * @param options
 * @returns factory {@link UserFactoryInterface}
 */
exports.defineUserFactory = ((options) => {
    return defineUserFactoryInternal(options ?? {}, {});
});
exports.defineUserFactory.withTransientFields = defaultTransientFieldValues => options => defineUserFactoryInternal(options ?? {}, defaultTransientFieldValues);
function isUserRoleorganizationFactory(x) {
    return x?._factoryFor === "Organization";
}
function isUserRolestableFactory(x) {
    return x?._factoryFor === "Stable";
}
function isUserRoleuserFactory(x) {
    return x?._factoryFor === "User";
}
function autoGenerateUserRoleScalarsOrEnums({ seq }) {
    return {
        roleUuid: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "UserRole", fieldName: "roleUuid", isId: true, isUnique: false, seq }),
        role: "admin"
    };
}
function defineUserRoleFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("UserRole", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateUserRoleScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver ?? {});
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                organization: isUserRoleorganizationFactory(defaultData.organization) ? {
                    create: await defaultData.organization.build()
                } : defaultData.organization,
                stable: isUserRolestableFactory(defaultData.stable) ? {
                    create: await defaultData.stable.build()
                } : defaultData.stable,
                user: isUserRoleuserFactory(defaultData.user) ? {
                    create: await defaultData.user.build()
                } : defaultData.user
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            roleUuid: inputData.roleUuid
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().userRole.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "UserRole",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link UserRole} model.
 *
 * @param options
 * @returns factory {@link UserRoleFactoryInterface}
 */
exports.defineUserRoleFactory = ((options) => {
    return defineUserRoleFactoryInternal(options ?? {}, {});
});
exports.defineUserRoleFactory.withTransientFields = defaultTransientFieldValues => options => defineUserRoleFactoryInternal(options ?? {}, defaultTransientFieldValues);
function isStaffstableFactory(x) {
    return x?._factoryFor === "Stable";
}
function isStafforganizationFactory(x) {
    return x?._factoryFor === "Organization";
}
function isStaffuserFactory(x) {
    return x?._factoryFor === "User";
}
function autoGenerateStaffScalarsOrEnums({ seq }) {
    return {
        name: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "Staff", fieldName: "name", isId: false, isUnique: false, seq })
    };
}
function defineStaffFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("Staff", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateStaffScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver ?? {});
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                stable: isStaffstableFactory(defaultData.stable) ? {
                    create: await defaultData.stable.build()
                } : defaultData.stable,
                organization: isStafforganizationFactory(defaultData.organization) ? {
                    create: await defaultData.organization.build()
                } : defaultData.organization,
                user: isStaffuserFactory(defaultData.user) ? {
                    create: await defaultData.user.build()
                } : defaultData.user
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            staffUuid: inputData.staffUuid
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().staff.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "Staff",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link Staff} model.
 *
 * @param options
 * @returns factory {@link StaffFactoryInterface}
 */
exports.defineStaffFactory = ((options) => {
    return defineStaffFactoryInternal(options ?? {}, {});
});
exports.defineStaffFactory.withTransientFields = defaultTransientFieldValues => options => defineStaffFactoryInternal(options ?? {}, defaultTransientFieldValues);
function isContractorganizationFactory(x) {
    return x?._factoryFor === "Organization";
}
function autoGenerateContractScalarsOrEnums({ seq }) {
    return {};
}
function defineContractFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("Contract", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateContractScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                organization: isContractorganizationFactory(defaultData.organization) ? {
                    create: await defaultData.organization.build()
                } : defaultData.organization
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            contractId: inputData.contractId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().contract.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "Contract",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link Contract} model.
 *
 * @param options
 * @returns factory {@link ContractFactoryInterface}
 */
exports.defineContractFactory = ((options) => {
    return defineContractFactoryInternal(options, {});
});
exports.defineContractFactory.withTransientFields = defaultTransientFieldValues => options => defineContractFactoryInternal(options, defaultTransientFieldValues);
function autoGenerateFarmAreaScalarsOrEnums({ seq }) {
    return {
        farmAreaId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "FarmArea", fieldName: "farmAreaId", isId: true, isUnique: false, seq }),
        name: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "FarmArea", fieldName: "name", isId: false, isUnique: false, seq }),
        order: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "FarmArea", fieldName: "order", isId: false, isUnique: false, seq })
    };
}
function defineFarmAreaFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("FarmArea", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateFarmAreaScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver ?? {});
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {};
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            farmAreaId: inputData.farmAreaId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().farmArea.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "FarmArea",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link FarmArea} model.
 *
 * @param options
 * @returns factory {@link FarmAreaFactoryInterface}
 */
exports.defineFarmAreaFactory = ((options) => {
    return defineFarmAreaFactoryInternal(options ?? {}, {});
});
exports.defineFarmAreaFactory.withTransientFields = defaultTransientFieldValues => options => defineFarmAreaFactoryInternal(options ?? {}, defaultTransientFieldValues);
function isMasterFarmfarmAreaFactory(x) {
    return x?._factoryFor === "FarmArea";
}
function autoGenerateMasterFarmScalarsOrEnums({ seq }) {
    return {
        masterFarmId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "MasterFarm", fieldName: "masterFarmId", isId: true, isUnique: false, seq }),
        name: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "MasterFarm", fieldName: "name", isId: false, isUnique: false, seq })
    };
}
function defineMasterFarmFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("MasterFarm", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateMasterFarmScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                farmArea: isMasterFarmfarmAreaFactory(defaultData.farmArea) ? {
                    create: await defaultData.farmArea.build()
                } : defaultData.farmArea
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            masterFarmId: inputData.masterFarmId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().masterFarm.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "MasterFarm",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link MasterFarm} model.
 *
 * @param options
 * @returns factory {@link MasterFarmFactoryInterface}
 */
exports.defineMasterFarmFactory = ((options) => {
    return defineMasterFarmFactoryInternal(options, {});
});
exports.defineMasterFarmFactory.withTransientFields = defaultTransientFieldValues => options => defineMasterFarmFactoryInternal(options, defaultTransientFieldValues);
function isHorseDailyRecordorganizationFactory(x) {
    return x?._factoryFor === "Organization";
}
function isHorseDailyRecordhorseFactory(x) {
    return x?._factoryFor === "Horse";
}
function isHorseDailyRecordcreatedUserFactory(x) {
    return x?._factoryFor === "User";
}
function isHorseDailyRecordhorseBodyRecordFactory(x) {
    return x?._factoryFor === "HorseBodyRecord";
}
function isHorseDailyRecordhorseRaceResultRecordFactory(x) {
    return x?._factoryFor === "HorseRaceResultRecord";
}
function isHorseDailyRecordhorseRaceRecapRecordFactory(x) {
    return x?._factoryFor === "HorseRaceRecapRecord";
}
function autoGenerateHorseDailyRecordScalarsOrEnums({ seq }) {
    return {
        horseDailyRecordId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "HorseDailyRecord", fieldName: "horseDailyRecordId", isId: true, isUnique: false, seq }),
        year: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "HorseDailyRecord", fieldName: "year", isId: false, isUnique: true, seq }),
        month: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "HorseDailyRecord", fieldName: "month", isId: false, isUnique: true, seq }),
        day: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "HorseDailyRecord", fieldName: "day", isId: false, isUnique: true, seq })
    };
}
function defineHorseDailyRecordFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("HorseDailyRecord", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateHorseDailyRecordScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                organization: isHorseDailyRecordorganizationFactory(defaultData.organization) ? {
                    create: await defaultData.organization.build()
                } : defaultData.organization,
                horse: isHorseDailyRecordhorseFactory(defaultData.horse) ? {
                    create: await defaultData.horse.build()
                } : defaultData.horse,
                createdUser: isHorseDailyRecordcreatedUserFactory(defaultData.createdUser) ? {
                    create: await defaultData.createdUser.build()
                } : defaultData.createdUser,
                horseBodyRecord: isHorseDailyRecordhorseBodyRecordFactory(defaultData.horseBodyRecord) ? {
                    create: await defaultData.horseBodyRecord.build()
                } : defaultData.horseBodyRecord,
                horseRaceResultRecord: isHorseDailyRecordhorseRaceResultRecordFactory(defaultData.horseRaceResultRecord) ? {
                    create: await defaultData.horseRaceResultRecord.build()
                } : defaultData.horseRaceResultRecord,
                horseRaceRecapRecord: isHorseDailyRecordhorseRaceRecapRecordFactory(defaultData.horseRaceRecapRecord) ? {
                    create: await defaultData.horseRaceRecapRecord.build()
                } : defaultData.horseRaceRecapRecord
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            horseDailyRecordId: inputData.horseDailyRecordId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().horseDailyRecord.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "HorseDailyRecord",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link HorseDailyRecord} model.
 *
 * @param options
 * @returns factory {@link HorseDailyRecordFactoryInterface}
 */
exports.defineHorseDailyRecordFactory = ((options) => {
    return defineHorseDailyRecordFactoryInternal(options, {});
});
exports.defineHorseDailyRecordFactory.withTransientFields = defaultTransientFieldValues => options => defineHorseDailyRecordFactoryInternal(options, defaultTransientFieldValues);
function isHorseBodyPhotohorseDailyRecordFactory(x) {
    return x?._factoryFor === "HorseDailyRecord";
}
function autoGenerateHorseBodyPhotoScalarsOrEnums({ seq }) {
    return {
        horseBodyPhotoId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "HorseBodyPhoto", fieldName: "horseBodyPhotoId", isId: true, isUnique: false, seq }),
        photoPath: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "HorseBodyPhoto", fieldName: "photoPath", isId: false, isUnique: false, seq })
    };
}
function defineHorseBodyPhotoFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("HorseBodyPhoto", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateHorseBodyPhotoScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                horseDailyRecord: isHorseBodyPhotohorseDailyRecordFactory(defaultData.horseDailyRecord) ? {
                    create: await defaultData.horseDailyRecord.build()
                } : defaultData.horseDailyRecord
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            horseBodyPhotoId: inputData.horseBodyPhotoId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().horseBodyPhoto.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "HorseBodyPhoto",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link HorseBodyPhoto} model.
 *
 * @param options
 * @returns factory {@link HorseBodyPhotoFactoryInterface}
 */
exports.defineHorseBodyPhotoFactory = ((options) => {
    return defineHorseBodyPhotoFactoryInternal(options, {});
});
exports.defineHorseBodyPhotoFactory.withTransientFields = defaultTransientFieldValues => options => defineHorseBodyPhotoFactoryInternal(options, defaultTransientFieldValues);
function isHorseBodyAffectedAreaPhotohorseDailyRecordFactory(x) {
    return x?._factoryFor === "HorseDailyRecord";
}
function autoGenerateHorseBodyAffectedAreaPhotoScalarsOrEnums({ seq }) {
    return {
        horseBodyAffectedAreaPhotoId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "HorseBodyAffectedAreaPhoto", fieldName: "horseBodyAffectedAreaPhotoId", isId: true, isUnique: false, seq }),
        daypart: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "HorseBodyAffectedAreaPhoto", fieldName: "daypart", isId: false, isUnique: false, seq }),
        photoPath: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "HorseBodyAffectedAreaPhoto", fieldName: "photoPath", isId: false, isUnique: false, seq })
    };
}
function defineHorseBodyAffectedAreaPhotoFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("HorseBodyAffectedAreaPhoto", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateHorseBodyAffectedAreaPhotoScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                horseDailyRecord: isHorseBodyAffectedAreaPhotohorseDailyRecordFactory(defaultData.horseDailyRecord) ? {
                    create: await defaultData.horseDailyRecord.build()
                } : defaultData.horseDailyRecord
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            horseBodyAffectedAreaPhotoId: inputData.horseBodyAffectedAreaPhotoId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().horseBodyAffectedAreaPhoto.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "HorseBodyAffectedAreaPhoto",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link HorseBodyAffectedAreaPhoto} model.
 *
 * @param options
 * @returns factory {@link HorseBodyAffectedAreaPhotoFactoryInterface}
 */
exports.defineHorseBodyAffectedAreaPhotoFactory = ((options) => {
    return defineHorseBodyAffectedAreaPhotoFactoryInternal(options, {});
});
exports.defineHorseBodyAffectedAreaPhotoFactory.withTransientFields = defaultTransientFieldValues => options => defineHorseBodyAffectedAreaPhotoFactoryInternal(options, defaultTransientFieldValues);
function isHorseBodyRecordhorseDailyRecordFactory(x) {
    return x?._factoryFor === "HorseDailyRecord";
}
function autoGenerateHorseBodyRecordScalarsOrEnums({ seq }) {
    return {
        horseBodyRecordId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "HorseBodyRecord", fieldName: "horseBodyRecordId", isId: true, isUnique: false, seq })
    };
}
function defineHorseBodyRecordFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("HorseBodyRecord", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateHorseBodyRecordScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                horseDailyRecord: isHorseBodyRecordhorseDailyRecordFactory(defaultData.horseDailyRecord) ? {
                    create: await defaultData.horseDailyRecord.build()
                } : defaultData.horseDailyRecord
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            horseBodyRecordId: inputData.horseBodyRecordId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().horseBodyRecord.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "HorseBodyRecord",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link HorseBodyRecord} model.
 *
 * @param options
 * @returns factory {@link HorseBodyRecordFactoryInterface}
 */
exports.defineHorseBodyRecordFactory = ((options) => {
    return defineHorseBodyRecordFactoryInternal(options, {});
});
exports.defineHorseBodyRecordFactory.withTransientFields = defaultTransientFieldValues => options => defineHorseBodyRecordFactoryInternal(options, defaultTransientFieldValues);
function isHorseTrainingRecordhorseDailyRecordFactory(x) {
    return x?._factoryFor === "HorseDailyRecord";
}
function isHorseTrainingRecordriderFactory(x) {
    return x?._factoryFor === "Staff";
}
function isHorseTrainingRecordtrainingMenuFactory(x) {
    return x?._factoryFor === "TrainingMenu";
}
function isHorseTrainingRecordfacilityFactory(x) {
    return x?._factoryFor === "Facility";
}
function isHorseTrainingRecordtrainingCourseFactory(x) {
    return x?._factoryFor === "TrainingCourse";
}
function autoGenerateHorseTrainingRecordScalarsOrEnums({ seq }) {
    return {
        trainingRecordUuid: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "HorseTrainingRecord", fieldName: "trainingRecordUuid", isId: true, isUnique: false, seq })
    };
}
function defineHorseTrainingRecordFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("HorseTrainingRecord", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateHorseTrainingRecordScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                horseDailyRecord: isHorseTrainingRecordhorseDailyRecordFactory(defaultData.horseDailyRecord) ? {
                    create: await defaultData.horseDailyRecord.build()
                } : defaultData.horseDailyRecord,
                rider: isHorseTrainingRecordriderFactory(defaultData.rider) ? {
                    create: await defaultData.rider.build()
                } : defaultData.rider,
                trainingMenu: isHorseTrainingRecordtrainingMenuFactory(defaultData.trainingMenu) ? {
                    create: await defaultData.trainingMenu.build()
                } : defaultData.trainingMenu,
                facility: isHorseTrainingRecordfacilityFactory(defaultData.facility) ? {
                    create: await defaultData.facility.build()
                } : defaultData.facility,
                trainingCourse: isHorseTrainingRecordtrainingCourseFactory(defaultData.trainingCourse) ? {
                    create: await defaultData.trainingCourse.build()
                } : defaultData.trainingCourse
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            trainingRecordUuid: inputData.trainingRecordUuid
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().horseTrainingRecord.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "HorseTrainingRecord",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link HorseTrainingRecord} model.
 *
 * @param options
 * @returns factory {@link HorseTrainingRecordFactoryInterface}
 */
exports.defineHorseTrainingRecordFactory = ((options) => {
    return defineHorseTrainingRecordFactoryInternal(options, {});
});
exports.defineHorseTrainingRecordFactory.withTransientFields = defaultTransientFieldValues => options => defineHorseTrainingRecordFactoryInternal(options, defaultTransientFieldValues);
function isTrainingPartnerhorseTrainingRecordFactory(x) {
    return x?._factoryFor === "HorseTrainingRecord";
}
function isTrainingPartnerhorseFactory(x) {
    return x?._factoryFor === "Horse";
}
function autoGenerateTrainingPartnerScalarsOrEnums({ seq }) {
    return {
        trainingPartnerId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "TrainingPartner", fieldName: "trainingPartnerId", isId: true, isUnique: false, seq }),
        horseName: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "TrainingPartner", fieldName: "horseName", isId: false, isUnique: false, seq })
    };
}
function defineTrainingPartnerFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("TrainingPartner", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateTrainingPartnerScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                horseTrainingRecord: isTrainingPartnerhorseTrainingRecordFactory(defaultData.horseTrainingRecord) ? {
                    create: await defaultData.horseTrainingRecord.build()
                } : defaultData.horseTrainingRecord,
                horse: isTrainingPartnerhorseFactory(defaultData.horse) ? {
                    create: await defaultData.horse.build()
                } : defaultData.horse
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            trainingPartnerId: inputData.trainingPartnerId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().trainingPartner.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "TrainingPartner",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link TrainingPartner} model.
 *
 * @param options
 * @returns factory {@link TrainingPartnerFactoryInterface}
 */
exports.defineTrainingPartnerFactory = ((options) => {
    return defineTrainingPartnerFactoryInternal(options, {});
});
exports.defineTrainingPartnerFactory.withTransientFields = defaultTransientFieldValues => options => defineTrainingPartnerFactoryInternal(options, defaultTransientFieldValues);
function isOrganizationVeterinarianorganizationFactory(x) {
    return x?._factoryFor === "Organization";
}
function autoGenerateOrganizationVeterinarianScalarsOrEnums({ seq }) {
    return {
        organizationVeterinarianId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "OrganizationVeterinarian", fieldName: "organizationVeterinarianId", isId: true, isUnique: false, seq }),
        veterinarianName: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "OrganizationVeterinarian", fieldName: "veterinarianName", isId: false, isUnique: false, seq })
    };
}
function defineOrganizationVeterinarianFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("OrganizationVeterinarian", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateOrganizationVeterinarianScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                organization: isOrganizationVeterinarianorganizationFactory(defaultData.organization) ? {
                    create: await defaultData.organization.build()
                } : defaultData.organization
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            organizationVeterinarianId: inputData.organizationVeterinarianId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().organizationVeterinarian.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "OrganizationVeterinarian",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link OrganizationVeterinarian} model.
 *
 * @param options
 * @returns factory {@link OrganizationVeterinarianFactoryInterface}
 */
exports.defineOrganizationVeterinarianFactory = ((options) => {
    return defineOrganizationVeterinarianFactoryInternal(options, {});
});
exports.defineOrganizationVeterinarianFactory.withTransientFields = defaultTransientFieldValues => options => defineOrganizationVeterinarianFactoryInternal(options, defaultTransientFieldValues);
function isOrganizationFarrierorganizationFactory(x) {
    return x?._factoryFor === "Organization";
}
function autoGenerateOrganizationFarrierScalarsOrEnums({ seq }) {
    return {
        organizationFarrierId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "OrganizationFarrier", fieldName: "organizationFarrierId", isId: true, isUnique: false, seq }),
        farrierName: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "OrganizationFarrier", fieldName: "farrierName", isId: false, isUnique: false, seq })
    };
}
function defineOrganizationFarrierFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("OrganizationFarrier", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateOrganizationFarrierScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                organization: isOrganizationFarrierorganizationFactory(defaultData.organization) ? {
                    create: await defaultData.organization.build()
                } : defaultData.organization
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            organizationFarrierId: inputData.organizationFarrierId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().organizationFarrier.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "OrganizationFarrier",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link OrganizationFarrier} model.
 *
 * @param options
 * @returns factory {@link OrganizationFarrierFactoryInterface}
 */
exports.defineOrganizationFarrierFactory = ((options) => {
    return defineOrganizationFarrierFactoryInternal(options, {});
});
exports.defineOrganizationFarrierFactory.withTransientFields = defaultTransientFieldValues => options => defineOrganizationFarrierFactoryInternal(options, defaultTransientFieldValues);
function isHorseMedicalTreatmentRecordhorseDailyRecordFactory(x) {
    return x?._factoryFor === "HorseDailyRecord";
}
function isHorseMedicalTreatmentRecordveterinarianFactory(x) {
    return x?._factoryFor === "OrganizationVeterinarian";
}
function autoGenerateHorseMedicalTreatmentRecordScalarsOrEnums({ seq }) {
    return {
        horseMedicalTreatmentRecordId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "HorseMedicalTreatmentRecord", fieldName: "horseMedicalTreatmentRecordId", isId: true, isUnique: false, seq })
    };
}
function defineHorseMedicalTreatmentRecordFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("HorseMedicalTreatmentRecord", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateHorseMedicalTreatmentRecordScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                horseDailyRecord: isHorseMedicalTreatmentRecordhorseDailyRecordFactory(defaultData.horseDailyRecord) ? {
                    create: await defaultData.horseDailyRecord.build()
                } : defaultData.horseDailyRecord,
                veterinarian: isHorseMedicalTreatmentRecordveterinarianFactory(defaultData.veterinarian) ? {
                    create: await defaultData.veterinarian.build()
                } : defaultData.veterinarian
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            horseMedicalTreatmentRecordId: inputData.horseMedicalTreatmentRecordId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().horseMedicalTreatmentRecord.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "HorseMedicalTreatmentRecord",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link HorseMedicalTreatmentRecord} model.
 *
 * @param options
 * @returns factory {@link HorseMedicalTreatmentRecordFactoryInterface}
 */
exports.defineHorseMedicalTreatmentRecordFactory = ((options) => {
    return defineHorseMedicalTreatmentRecordFactoryInternal(options, {});
});
exports.defineHorseMedicalTreatmentRecordFactory.withTransientFields = defaultTransientFieldValues => options => defineHorseMedicalTreatmentRecordFactoryInternal(options, defaultTransientFieldValues);
function isHorseMedicalTreatmentInvoicePhotohorseMedicalTreatmentRecordFactory(x) {
    return x?._factoryFor === "HorseMedicalTreatmentRecord";
}
function autoGenerateHorseMedicalTreatmentInvoicePhotoScalarsOrEnums({ seq }) {
    return {
        horseMedicalTreatmentRecordInvoicePhotoId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "HorseMedicalTreatmentInvoicePhoto", fieldName: "horseMedicalTreatmentRecordInvoicePhotoId", isId: true, isUnique: false, seq }),
        photoPath: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "HorseMedicalTreatmentInvoicePhoto", fieldName: "photoPath", isId: false, isUnique: false, seq })
    };
}
function defineHorseMedicalTreatmentInvoicePhotoFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("HorseMedicalTreatmentInvoicePhoto", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateHorseMedicalTreatmentInvoicePhotoScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                horseMedicalTreatmentRecord: isHorseMedicalTreatmentInvoicePhotohorseMedicalTreatmentRecordFactory(defaultData.horseMedicalTreatmentRecord) ? {
                    create: await defaultData.horseMedicalTreatmentRecord.build()
                } : defaultData.horseMedicalTreatmentRecord
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            horseMedicalTreatmentRecordInvoicePhotoId: inputData.horseMedicalTreatmentRecordInvoicePhotoId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().horseMedicalTreatmentInvoicePhoto.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "HorseMedicalTreatmentInvoicePhoto",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link HorseMedicalTreatmentInvoicePhoto} model.
 *
 * @param options
 * @returns factory {@link HorseMedicalTreatmentInvoicePhotoFactoryInterface}
 */
exports.defineHorseMedicalTreatmentInvoicePhotoFactory = ((options) => {
    return defineHorseMedicalTreatmentInvoicePhotoFactoryInternal(options, {});
});
exports.defineHorseMedicalTreatmentInvoicePhotoFactory.withTransientFields = defaultTransientFieldValues => options => defineHorseMedicalTreatmentInvoicePhotoFactoryInternal(options, defaultTransientFieldValues);
function isHorseMedicalTreatmentAffectedAreaPhotohorseMedicalTreatmentRecordFactory(x) {
    return x?._factoryFor === "HorseMedicalTreatmentRecord";
}
function autoGenerateHorseMedicalTreatmentAffectedAreaPhotoScalarsOrEnums({ seq }) {
    return {
        photoPath: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "HorseMedicalTreatmentAffectedAreaPhoto", fieldName: "photoPath", isId: false, isUnique: false, seq })
    };
}
function defineHorseMedicalTreatmentAffectedAreaPhotoFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("HorseMedicalTreatmentAffectedAreaPhoto", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateHorseMedicalTreatmentAffectedAreaPhotoScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                horseMedicalTreatmentRecord: isHorseMedicalTreatmentAffectedAreaPhotohorseMedicalTreatmentRecordFactory(defaultData.horseMedicalTreatmentRecord) ? {
                    create: await defaultData.horseMedicalTreatmentRecord.build()
                } : defaultData.horseMedicalTreatmentRecord
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            horseMedicalTreatmentAffectedAreaPhotoId: inputData.horseMedicalTreatmentAffectedAreaPhotoId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().horseMedicalTreatmentAffectedAreaPhoto.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "HorseMedicalTreatmentAffectedAreaPhoto",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link HorseMedicalTreatmentAffectedAreaPhoto} model.
 *
 * @param options
 * @returns factory {@link HorseMedicalTreatmentAffectedAreaPhotoFactoryInterface}
 */
exports.defineHorseMedicalTreatmentAffectedAreaPhotoFactory = ((options) => {
    return defineHorseMedicalTreatmentAffectedAreaPhotoFactoryInternal(options, {});
});
exports.defineHorseMedicalTreatmentAffectedAreaPhotoFactory.withTransientFields = defaultTransientFieldValues => options => defineHorseMedicalTreatmentAffectedAreaPhotoFactoryInternal(options, defaultTransientFieldValues);
function isHorseShoeingRecordhorseDailyRecordFactory(x) {
    return x?._factoryFor === "HorseDailyRecord";
}
function isHorseShoeingRecordfarrierFactory(x) {
    return x?._factoryFor === "OrganizationFarrier";
}
function autoGenerateHorseShoeingRecordScalarsOrEnums({ seq }) {
    return {
        horseShoeingRecordId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "HorseShoeingRecord", fieldName: "horseShoeingRecordId", isId: true, isUnique: false, seq })
    };
}
function defineHorseShoeingRecordFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("HorseShoeingRecord", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateHorseShoeingRecordScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                horseDailyRecord: isHorseShoeingRecordhorseDailyRecordFactory(defaultData.horseDailyRecord) ? {
                    create: await defaultData.horseDailyRecord.build()
                } : defaultData.horseDailyRecord,
                farrier: isHorseShoeingRecordfarrierFactory(defaultData.farrier) ? {
                    create: await defaultData.farrier.build()
                } : defaultData.farrier
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            horseShoeingRecordId: inputData.horseShoeingRecordId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().horseShoeingRecord.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "HorseShoeingRecord",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link HorseShoeingRecord} model.
 *
 * @param options
 * @returns factory {@link HorseShoeingRecordFactoryInterface}
 */
exports.defineHorseShoeingRecordFactory = ((options) => {
    return defineHorseShoeingRecordFactoryInternal(options, {});
});
exports.defineHorseShoeingRecordFactory.withTransientFields = defaultTransientFieldValues => options => defineHorseShoeingRecordFactoryInternal(options, defaultTransientFieldValues);
function isHorseShoeingInvoicePhotohorseShoeingRecordFactory(x) {
    return x?._factoryFor === "HorseShoeingRecord";
}
function autoGenerateHorseShoeingInvoicePhotoScalarsOrEnums({ seq }) {
    return {
        horseShoeingInvoicePhotoId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "HorseShoeingInvoicePhoto", fieldName: "horseShoeingInvoicePhotoId", isId: true, isUnique: false, seq }),
        photoPath: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "HorseShoeingInvoicePhoto", fieldName: "photoPath", isId: false, isUnique: false, seq })
    };
}
function defineHorseShoeingInvoicePhotoFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("HorseShoeingInvoicePhoto", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateHorseShoeingInvoicePhotoScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                horseShoeingRecord: isHorseShoeingInvoicePhotohorseShoeingRecordFactory(defaultData.horseShoeingRecord) ? {
                    create: await defaultData.horseShoeingRecord.build()
                } : defaultData.horseShoeingRecord
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            horseShoeingInvoicePhotoId: inputData.horseShoeingInvoicePhotoId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().horseShoeingInvoicePhoto.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "HorseShoeingInvoicePhoto",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link HorseShoeingInvoicePhoto} model.
 *
 * @param options
 * @returns factory {@link HorseShoeingInvoicePhotoFactoryInterface}
 */
exports.defineHorseShoeingInvoicePhotoFactory = ((options) => {
    return defineHorseShoeingInvoicePhotoFactoryInternal(options, {});
});
exports.defineHorseShoeingInvoicePhotoFactory.withTransientFields = defaultTransientFieldValues => options => defineHorseShoeingInvoicePhotoFactoryInternal(options, defaultTransientFieldValues);
function isHorseRaceResultRecordhorseDailyRecordFactory(x) {
    return x?._factoryFor === "HorseDailyRecord";
}
function isHorseRaceResultRecordracePlaceFactory(x) {
    return x?._factoryFor === "RacePlace";
}
function autoGenerateHorseRaceResultRecordScalarsOrEnums({ seq }) {
    return {
        raceResultId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "HorseRaceResultRecord", fieldName: "raceResultId", isId: true, isUnique: false, seq })
    };
}
function defineHorseRaceResultRecordFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("HorseRaceResultRecord", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateHorseRaceResultRecordScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                horseDailyRecord: isHorseRaceResultRecordhorseDailyRecordFactory(defaultData.horseDailyRecord) ? {
                    create: await defaultData.horseDailyRecord.build()
                } : defaultData.horseDailyRecord,
                racePlace: isHorseRaceResultRecordracePlaceFactory(defaultData.racePlace) ? {
                    create: await defaultData.racePlace.build()
                } : defaultData.racePlace
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            raceResultId: inputData.raceResultId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().horseRaceResultRecord.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "HorseRaceResultRecord",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link HorseRaceResultRecord} model.
 *
 * @param options
 * @returns factory {@link HorseRaceResultRecordFactoryInterface}
 */
exports.defineHorseRaceResultRecordFactory = ((options) => {
    return defineHorseRaceResultRecordFactoryInternal(options, {});
});
exports.defineHorseRaceResultRecordFactory.withTransientFields = defaultTransientFieldValues => options => defineHorseRaceResultRecordFactoryInternal(options, defaultTransientFieldValues);
function isHorseRaceRecapRecordhorseDailyRecordFactory(x) {
    return x?._factoryFor === "HorseDailyRecord";
}
function isHorseRaceRecapRecordstaffFactory(x) {
    return x?._factoryFor === "Staff";
}
function autoGenerateHorseRaceRecapRecordScalarsOrEnums({ seq }) {
    return {
        raceRecapId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "HorseRaceRecapRecord", fieldName: "raceRecapId", isId: true, isUnique: false, seq })
    };
}
function defineHorseRaceRecapRecordFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("HorseRaceRecapRecord", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateHorseRaceRecapRecordScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                horseDailyRecord: isHorseRaceRecapRecordhorseDailyRecordFactory(defaultData.horseDailyRecord) ? {
                    create: await defaultData.horseDailyRecord.build()
                } : defaultData.horseDailyRecord,
                staff: isHorseRaceRecapRecordstaffFactory(defaultData.staff) ? {
                    create: await defaultData.staff.build()
                } : defaultData.staff
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            raceRecapId: inputData.raceRecapId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().horseRaceRecapRecord.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "HorseRaceRecapRecord",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link HorseRaceRecapRecord} model.
 *
 * @param options
 * @returns factory {@link HorseRaceRecapRecordFactoryInterface}
 */
exports.defineHorseRaceRecapRecordFactory = ((options) => {
    return defineHorseRaceRecapRecordFactoryInternal(options, {});
});
exports.defineHorseRaceRecapRecordFactory.withTransientFields = defaultTransientFieldValues => options => defineHorseRaceRecapRecordFactoryInternal(options, defaultTransientFieldValues);
function autoGenerateRacePlaceScalarsOrEnums({ seq }) {
    return {
        name: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "RacePlace", fieldName: "name", isId: false, isUnique: false, seq })
    };
}
function defineRacePlaceFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("RacePlace", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateRacePlaceScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver ?? {});
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {};
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            racePlaceId: inputData.racePlaceId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().racePlace.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "RacePlace",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link RacePlace} model.
 *
 * @param options
 * @returns factory {@link RacePlaceFactoryInterface}
 */
exports.defineRacePlaceFactory = ((options) => {
    return defineRacePlaceFactoryInternal(options ?? {}, {});
});
exports.defineRacePlaceFactory.withTransientFields = defaultTransientFieldValues => options => defineRacePlaceFactoryInternal(options ?? {}, defaultTransientFieldValues);
function isBusinessTripHistoryorganizationFactory(x) {
    return x?._factoryFor === "Organization";
}
function autoGenerateBusinessTripHistoryScalarsOrEnums({ seq }) {
    return {};
}
function defineBusinessTripHistoryFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("BusinessTripHistory", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateBusinessTripHistoryScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                organization: isBusinessTripHistoryorganizationFactory(defaultData.organization) ? {
                    create: await defaultData.organization.build()
                } : defaultData.organization
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            businessTripHistoryId: inputData.businessTripHistoryId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().businessTripHistory.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "BusinessTripHistory",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link BusinessTripHistory} model.
 *
 * @param options
 * @returns factory {@link BusinessTripHistoryFactoryInterface}
 */
exports.defineBusinessTripHistoryFactory = ((options) => {
    return defineBusinessTripHistoryFactoryInternal(options, {});
});
exports.defineBusinessTripHistoryFactory.withTransientFields = defaultTransientFieldValues => options => defineBusinessTripHistoryFactoryInternal(options, defaultTransientFieldValues);
function isBusinessTripHistoryHorsebusinessTripHistoryFactory(x) {
    return x?._factoryFor === "BusinessTripHistory";
}
function isBusinessTripHistoryHorsehorseFactory(x) {
    return x?._factoryFor === "Horse";
}
function autoGenerateBusinessTripHistoryHorseScalarsOrEnums({ seq }) {
    return {};
}
function defineBusinessTripHistoryHorseFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("BusinessTripHistoryHorse", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateBusinessTripHistoryHorseScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                businessTripHistory: isBusinessTripHistoryHorsebusinessTripHistoryFactory(defaultData.businessTripHistory) ? {
                    create: await defaultData.businessTripHistory.build()
                } : defaultData.businessTripHistory,
                horse: isBusinessTripHistoryHorsehorseFactory(defaultData.horse) ? {
                    create: await defaultData.horse.build()
                } : defaultData.horse
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            businessTripHistoryHorseId: inputData.businessTripHistoryHorseId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().businessTripHistoryHorse.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "BusinessTripHistoryHorse",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link BusinessTripHistoryHorse} model.
 *
 * @param options
 * @returns factory {@link BusinessTripHistoryHorseFactoryInterface}
 */
exports.defineBusinessTripHistoryHorseFactory = ((options) => {
    return defineBusinessTripHistoryHorseFactoryInternal(options, {});
});
exports.defineBusinessTripHistoryHorseFactory.withTransientFields = defaultTransientFieldValues => options => defineBusinessTripHistoryHorseFactoryInternal(options, defaultTransientFieldValues);
function isFacilityassociationFactory(x) {
    return x?._factoryFor === "Association";
}
function autoGenerateFacilityScalarsOrEnums({ seq }) {
    return {
        facilityId: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "Facility", fieldName: "facilityId", isId: true, isUnique: false, seq }),
        facilityName: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "Facility", fieldName: "facilityName", isId: false, isUnique: false, seq })
    };
}
function defineFacilityFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("Facility", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateFacilityScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                association: isFacilityassociationFactory(defaultData.association) ? {
                    create: await defaultData.association.build()
                } : defaultData.association
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            facilityId: inputData.facilityId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().facility.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "Facility",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link Facility} model.
 *
 * @param options
 * @returns factory {@link FacilityFactoryInterface}
 */
exports.defineFacilityFactory = ((options) => {
    return defineFacilityFactoryInternal(options, {});
});
exports.defineFacilityFactory.withTransientFields = defaultTransientFieldValues => options => defineFacilityFactoryInternal(options, defaultTransientFieldValues);
function isTrainingCourseMasterfacilityFactory(x) {
    return x?._factoryFor === "Facility";
}
function autoGenerateTrainingCourseMasterScalarsOrEnums({ seq }) {
    return {
        trainingCourseMasterId: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "TrainingCourseMaster", fieldName: "trainingCourseMasterId", isId: true, isUnique: false, seq }),
        courseName: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "TrainingCourseMaster", fieldName: "courseName", isId: false, isUnique: false, seq })
    };
}
function defineTrainingCourseMasterFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("TrainingCourseMaster", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateTrainingCourseMasterScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver ?? {});
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                facility: isTrainingCourseMasterfacilityFactory(defaultData.facility) ? {
                    create: await defaultData.facility.build()
                } : defaultData.facility
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            trainingCourseMasterId: inputData.trainingCourseMasterId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().trainingCourseMaster.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "TrainingCourseMaster",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link TrainingCourseMaster} model.
 *
 * @param options
 * @returns factory {@link TrainingCourseMasterFactoryInterface}
 */
exports.defineTrainingCourseMasterFactory = ((options) => {
    return defineTrainingCourseMasterFactoryInternal(options ?? {}, {});
});
exports.defineTrainingCourseMasterFactory.withTransientFields = defaultTransientFieldValues => options => defineTrainingCourseMasterFactoryInternal(options ?? {}, defaultTransientFieldValues);
function isTrainingCoursetrainingCourseMasterFactory(x) {
    return x?._factoryFor === "TrainingCourseMaster";
}
function autoGenerateTrainingCourseScalarsOrEnums({ seq }) {
    return {
        courseId: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "TrainingCourse", fieldName: "courseId", isId: true, isUnique: false, seq }),
        courseName: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "TrainingCourse", fieldName: "courseName", isId: false, isUnique: false, seq })
    };
}
function defineTrainingCourseFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("TrainingCourse", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateTrainingCourseScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver ?? {});
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                trainingCourseMaster: isTrainingCoursetrainingCourseMasterFactory(defaultData.trainingCourseMaster) ? {
                    create: await defaultData.trainingCourseMaster.build()
                } : defaultData.trainingCourseMaster
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            courseId: inputData.courseId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().trainingCourse.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "TrainingCourse",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link TrainingCourse} model.
 *
 * @param options
 * @returns factory {@link TrainingCourseFactoryInterface}
 */
exports.defineTrainingCourseFactory = ((options) => {
    return defineTrainingCourseFactoryInternal(options ?? {}, {});
});
exports.defineTrainingCourseFactory.withTransientFields = defaultTransientFieldValues => options => defineTrainingCourseFactoryInternal(options ?? {}, defaultTransientFieldValues);
function autoGenerateFurlongLineScalarsOrEnums({ seq }) {
    return {
        facilityId: (0, internal_1.getScalarFieldValueGenerator)().BigInt({ modelName: "FurlongLine", fieldName: "facilityId", isId: false, isUnique: true, seq }),
        furlong: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "FurlongLine", fieldName: "furlong", isId: false, isUnique: true, seq }),
        direction: "left",
        line: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "FurlongLine", fieldName: "line", isId: false, isUnique: false, seq })
    };
}
function defineFurlongLineFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("FurlongLine", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateFurlongLineScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver ?? {});
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {};
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            furlongLineInternalId: inputData.furlongLineInternalId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().furlongLine.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "FurlongLine",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link FurlongLine} model.
 *
 * @param options
 * @returns factory {@link FurlongLineFactoryInterface}
 */
exports.defineFurlongLineFactory = ((options) => {
    return defineFurlongLineFactoryInternal(options ?? {}, {});
});
exports.defineFurlongLineFactory.withTransientFields = defaultTransientFieldValues => options => defineFurlongLineFactoryInternal(options ?? {}, defaultTransientFieldValues);
function isHorsestableFactory(x) {
    return x?._factoryFor === "Stable";
}
function isHorseorganizationFactory(x) {
    return x?._factoryFor === "Organization";
}
function isHorsemasterHorseFactory(x) {
    return x?._factoryFor === "MasterHorse";
}
function isHorsehorseStatusFactory(x) {
    return x?._factoryFor === "HorseStatus";
}
function autoGenerateHorseScalarsOrEnums({ seq }) {
    return {
        name: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "Horse", fieldName: "name", isId: false, isUnique: false, seq })
    };
}
function defineHorseFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("Horse", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateHorseScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                stable: isHorsestableFactory(defaultData.stable) ? {
                    create: await defaultData.stable.build()
                } : defaultData.stable,
                organization: isHorseorganizationFactory(defaultData.organization) ? {
                    create: await defaultData.organization.build()
                } : defaultData.organization,
                masterHorse: isHorsemasterHorseFactory(defaultData.masterHorse) ? {
                    create: await defaultData.masterHorse.build()
                } : defaultData.masterHorse,
                horseStatus: isHorsehorseStatusFactory(defaultData.horseStatus) ? {
                    create: await defaultData.horseStatus.build()
                } : defaultData.horseStatus
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            horseId: inputData.horseId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().horse.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "Horse",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link Horse} model.
 *
 * @param options
 * @returns factory {@link HorseFactoryInterface}
 */
exports.defineHorseFactory = ((options) => {
    return defineHorseFactoryInternal(options, {});
});
exports.defineHorseFactory.withTransientFields = defaultTransientFieldValues => options => defineHorseFactoryInternal(options, defaultTransientFieldValues);
function isHorseStableHistoryhorseFactory(x) {
    return x?._factoryFor === "Horse";
}
function isHorseStableHistorystableFactory(x) {
    return x?._factoryFor === "Stable";
}
function autoGenerateHorseStableHistoryScalarsOrEnums({ seq }) {
    return {
        horseStableHistoryUuid: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "HorseStableHistory", fieldName: "horseStableHistoryUuid", isId: true, isUnique: false, seq })
    };
}
function defineHorseStableHistoryFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("HorseStableHistory", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateHorseStableHistoryScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                horse: isHorseStableHistoryhorseFactory(defaultData.horse) ? {
                    create: await defaultData.horse.build()
                } : defaultData.horse,
                stable: isHorseStableHistorystableFactory(defaultData.stable) ? {
                    create: await defaultData.stable.build()
                } : defaultData.stable
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            horseStableHistoryUuid: inputData.horseStableHistoryUuid
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().horseStableHistory.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "HorseStableHistory",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link HorseStableHistory} model.
 *
 * @param options
 * @returns factory {@link HorseStableHistoryFactoryInterface}
 */
exports.defineHorseStableHistoryFactory = ((options) => {
    return defineHorseStableHistoryFactoryInternal(options, {});
});
exports.defineHorseStableHistoryFactory.withTransientFields = defaultTransientFieldValues => options => defineHorseStableHistoryFactoryInternal(options, defaultTransientFieldValues);
function isHorseStatushorseFactory(x) {
    return x?._factoryFor === "Horse";
}
function isHorseStatusoutsideFarmFactory(x) {
    return x?._factoryFor === "StableTmOutsideFarm";
}
function autoGenerateHorseStatusScalarsOrEnums({ seq }) {
    return {
        horseStatusId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "HorseStatus", fieldName: "horseStatusId", isId: true, isUnique: false, seq })
    };
}
function defineHorseStatusFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("HorseStatus", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateHorseStatusScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                horse: isHorseStatushorseFactory(defaultData.horse) ? {
                    create: await defaultData.horse.build()
                } : defaultData.horse,
                outsideFarm: isHorseStatusoutsideFarmFactory(defaultData.outsideFarm) ? {
                    create: await defaultData.outsideFarm.build()
                } : defaultData.outsideFarm
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            horseStatusId: inputData.horseStatusId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().horseStatus.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "HorseStatus",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link HorseStatus} model.
 *
 * @param options
 * @returns factory {@link HorseStatusFactoryInterface}
 */
exports.defineHorseStatusFactory = ((options) => {
    return defineHorseStatusFactoryInternal(options, {});
});
exports.defineHorseStatusFactory.withTransientFields = defaultTransientFieldValues => options => defineHorseStatusFactoryInternal(options, defaultTransientFieldValues);
function autoGenerateMasterHorseScalarsOrEnums({ seq }) {
    return {
        masterHorseId: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "MasterHorse", fieldName: "masterHorseId", isId: true, isUnique: false, seq }),
        horseName: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "MasterHorse", fieldName: "horseName", isId: false, isUnique: false, seq }),
        motherName: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "MasterHorse", fieldName: "motherName", isId: false, isUnique: false, seq }),
        gender: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "MasterHorse", fieldName: "gender", isId: false, isUnique: false, seq }),
        birthYear: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "MasterHorse", fieldName: "birthYear", isId: false, isUnique: false, seq })
    };
}
function defineMasterHorseFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("MasterHorse", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateMasterHorseScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver ?? {});
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {};
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            masterHorseId: inputData.masterHorseId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().masterHorse.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "MasterHorse",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link MasterHorse} model.
 *
 * @param options
 * @returns factory {@link MasterHorseFactoryInterface}
 */
exports.defineMasterHorseFactory = ((options) => {
    return defineMasterHorseFactoryInternal(options ?? {}, {});
});
exports.defineMasterHorseFactory.withTransientFields = defaultTransientFieldValues => options => defineMasterHorseFactoryInternal(options ?? {}, defaultTransientFieldValues);
function isHorseHandoverNotehorseFactory(x) {
    return x?._factoryFor === "Horse";
}
function autoGenerateHorseHandoverNoteScalarsOrEnums({ seq }) {
    return {
        horseHandoverNoteId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "HorseHandoverNote", fieldName: "horseHandoverNoteId", isId: true, isUnique: false, seq })
    };
}
function defineHorseHandoverNoteFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("HorseHandoverNote", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateHorseHandoverNoteScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                horse: isHorseHandoverNotehorseFactory(defaultData.horse) ? {
                    create: await defaultData.horse.build()
                } : defaultData.horse
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            horseHandoverNoteId: inputData.horseHandoverNoteId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().horseHandoverNote.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "HorseHandoverNote",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link HorseHandoverNote} model.
 *
 * @param options
 * @returns factory {@link HorseHandoverNoteFactoryInterface}
 */
exports.defineHorseHandoverNoteFactory = ((options) => {
    return defineHorseHandoverNoteFactoryInternal(options, {});
});
exports.defineHorseHandoverNoteFactory.withTransientFields = defaultTransientFieldValues => options => defineHorseHandoverNoteFactoryInternal(options, defaultTransientFieldValues);
function autoGenerateEmailLogScalarsOrEnums({ seq }) {
    return {
        email: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "EmailLog", fieldName: "email", isId: false, isUnique: false, seq }),
        emailTemplateKey: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "EmailLog", fieldName: "emailTemplateKey", isId: false, isUnique: false, seq })
    };
}
function defineEmailLogFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("EmailLog", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateEmailLogScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver ?? {});
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {};
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            emailLogInternalId: inputData.emailLogInternalId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().emailLog.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "EmailLog",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link EmailLog} model.
 *
 * @param options
 * @returns factory {@link EmailLogFactoryInterface}
 */
exports.defineEmailLogFactory = ((options) => {
    return defineEmailLogFactoryInternal(options ?? {}, {});
});
exports.defineEmailLogFactory.withTransientFields = defaultTransientFieldValues => options => defineEmailLogFactoryInternal(options ?? {}, defaultTransientFieldValues);
function autoGenerateAiGenerateLogScalarsOrEnums({ seq }) {
    return {
        aiGenerateLogId: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "AiGenerateLog", fieldName: "aiGenerateLogId", isId: true, isUnique: false, seq })
    };
}
function defineAiGenerateLogFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("AiGenerateLog", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateAiGenerateLogScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver ?? {});
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {};
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            aiGenerateLogId: inputData.aiGenerateLogId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().aiGenerateLog.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "AiGenerateLog",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link AiGenerateLog} model.
 *
 * @param options
 * @returns factory {@link AiGenerateLogFactoryInterface}
 */
exports.defineAiGenerateLogFactory = ((options) => {
    return defineAiGenerateLogFactoryInternal(options ?? {}, {});
});
exports.defineAiGenerateLogFactory.withTransientFields = defaultTransientFieldValues => options => defineAiGenerateLogFactoryInternal(options ?? {}, defaultTransientFieldValues);
function isOrganizationOwnerorganizationFactory(x) {
    return x?._factoryFor === "Organization";
}
function isOrganizationOwnerownerFactory(x) {
    return x?._factoryFor === "Owner";
}
function autoGenerateOrganizationOwnerScalarsOrEnums({ seq }) {
    return {
        organizationOwnerId: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "OrganizationOwner", fieldName: "organizationOwnerId", isId: true, isUnique: false, seq }),
        organizationOwnerName: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "OrganizationOwner", fieldName: "organizationOwnerName", isId: false, isUnique: false, seq })
    };
}
function defineOrganizationOwnerFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("OrganizationOwner", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateOrganizationOwnerScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                organization: isOrganizationOwnerorganizationFactory(defaultData.organization) ? {
                    create: await defaultData.organization.build()
                } : defaultData.organization,
                owner: isOrganizationOwnerownerFactory(defaultData.owner) ? {
                    create: await defaultData.owner.build()
                } : defaultData.owner
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            organizationOwnerId: inputData.organizationOwnerId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().organizationOwner.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "OrganizationOwner",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link OrganizationOwner} model.
 *
 * @param options
 * @returns factory {@link OrganizationOwnerFactoryInterface}
 */
exports.defineOrganizationOwnerFactory = ((options) => {
    return defineOrganizationOwnerFactoryInternal(options, {});
});
exports.defineOrganizationOwnerFactory.withTransientFields = defaultTransientFieldValues => options => defineOrganizationOwnerFactoryInternal(options, defaultTransientFieldValues);
function isOrganizationOwnerHorseRelationorganizationOwnerFactory(x) {
    return x?._factoryFor === "OrganizationOwner";
}
function isOrganizationOwnerHorseRelationhorseFactory(x) {
    return x?._factoryFor === "Horse";
}
function autoGenerateOrganizationOwnerHorseRelationScalarsOrEnums({ seq }) {
    return {
        organizationOwnerHorseRelationId: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "OrganizationOwnerHorseRelation", fieldName: "organizationOwnerHorseRelationId", isId: true, isUnique: false, seq })
    };
}
function defineOrganizationOwnerHorseRelationFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("OrganizationOwnerHorseRelation", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateOrganizationOwnerHorseRelationScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                organizationOwner: isOrganizationOwnerHorseRelationorganizationOwnerFactory(defaultData.organizationOwner) ? {
                    create: await defaultData.organizationOwner.build()
                } : defaultData.organizationOwner,
                horse: isOrganizationOwnerHorseRelationhorseFactory(defaultData.horse) ? {
                    create: await defaultData.horse.build()
                } : defaultData.horse
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            organizationOwnerHorseRelationId: inputData.organizationOwnerHorseRelationId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().organizationOwnerHorseRelation.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "OrganizationOwnerHorseRelation",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link OrganizationOwnerHorseRelation} model.
 *
 * @param options
 * @returns factory {@link OrganizationOwnerHorseRelationFactoryInterface}
 */
exports.defineOrganizationOwnerHorseRelationFactory = ((options) => {
    return defineOrganizationOwnerHorseRelationFactoryInternal(options, {});
});
exports.defineOrganizationOwnerHorseRelationFactory.withTransientFields = defaultTransientFieldValues => options => defineOrganizationOwnerHorseRelationFactoryInternal(options, defaultTransientFieldValues);
function isInvitationorganizationOwnerFactory(x) {
    return x?._factoryFor === "OrganizationOwner";
}
function autoGenerateInvitationScalarsOrEnums({ seq }) {
    return {
        invitationId: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "Invitation", fieldName: "invitationId", isId: true, isUnique: false, seq }),
        token: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "Invitation", fieldName: "token", isId: false, isUnique: true, seq }),
        method: "email"
    };
}
function defineInvitationFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("Invitation", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateInvitationScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                organizationOwner: isInvitationorganizationOwnerFactory(defaultData.organizationOwner) ? {
                    create: await defaultData.organizationOwner.build()
                } : defaultData.organizationOwner
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            invitationId: inputData.invitationId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().invitation.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "Invitation",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link Invitation} model.
 *
 * @param options
 * @returns factory {@link InvitationFactoryInterface}
 */
exports.defineInvitationFactory = ((options) => {
    return defineInvitationFactoryInternal(options, {});
});
exports.defineInvitationFactory.withTransientFields = defaultTransientFieldValues => options => defineInvitationFactoryInternal(options, defaultTransientFieldValues);
function autoGenerateOwnerScalarsOrEnums({ seq }) {
    return {
        ownerId: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "Owner", fieldName: "ownerId", isId: true, isUnique: false, seq })
    };
}
function defineOwnerFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("Owner", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateOwnerScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver ?? {});
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {};
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            ownerId: inputData.ownerId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().owner.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "Owner",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link Owner} model.
 *
 * @param options
 * @returns factory {@link OwnerFactoryInterface}
 */
exports.defineOwnerFactory = ((options) => {
    return defineOwnerFactoryInternal(options ?? {}, {});
});
exports.defineOwnerFactory.withTransientFields = defaultTransientFieldValues => options => defineOwnerFactoryInternal(options ?? {}, defaultTransientFieldValues);
function isOwnerHorseownerFactory(x) {
    return x?._factoryFor === "Owner";
}
function isOwnerHorsemasterHorseFactory(x) {
    return x?._factoryFor === "MasterHorse";
}
function autoGenerateOwnerHorseScalarsOrEnums({ seq }) {
    return {
        ownerHorseId: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "OwnerHorse", fieldName: "ownerHorseId", isId: true, isUnique: false, seq }),
        name: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "OwnerHorse", fieldName: "name", isId: false, isUnique: false, seq })
    };
}
function defineOwnerHorseFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("OwnerHorse", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateOwnerHorseScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                owner: isOwnerHorseownerFactory(defaultData.owner) ? {
                    create: await defaultData.owner.build()
                } : defaultData.owner,
                masterHorse: isOwnerHorsemasterHorseFactory(defaultData.masterHorse) ? {
                    create: await defaultData.masterHorse.build()
                } : defaultData.masterHorse
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            ownerHorseId: inputData.ownerHorseId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().ownerHorse.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "OwnerHorse",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link OwnerHorse} model.
 *
 * @param options
 * @returns factory {@link OwnerHorseFactoryInterface}
 */
exports.defineOwnerHorseFactory = ((options) => {
    return defineOwnerHorseFactoryInternal(options, {});
});
exports.defineOwnerHorseFactory.withTransientFields = defaultTransientFieldValues => options => defineOwnerHorseFactoryInternal(options, defaultTransientFieldValues);
function isReporthorseFactory(x) {
    return x?._factoryFor === "Horse";
}
function isReportorganizationFactory(x) {
    return x?._factoryFor === "Organization";
}
function autoGenerateReportScalarsOrEnums({ seq }) {
    return {
        reportId: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "Report", fieldName: "reportId", isId: true, isUnique: false, seq }),
        title: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "Report", fieldName: "title", isId: false, isUnique: false, seq }),
        templateId: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "Report", fieldName: "templateId", isId: false, isUnique: false, seq })
    };
}
function defineReportFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("Report", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateReportScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                horse: isReporthorseFactory(defaultData.horse) ? {
                    create: await defaultData.horse.build()
                } : defaultData.horse,
                organization: isReportorganizationFactory(defaultData.organization) ? {
                    create: await defaultData.organization.build()
                } : defaultData.organization
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            reportId: inputData.reportId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().report.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "Report",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link Report} model.
 *
 * @param options
 * @returns factory {@link ReportFactoryInterface}
 */
exports.defineReportFactory = ((options) => {
    return defineReportFactoryInternal(options, {});
});
exports.defineReportFactory.withTransientFields = defaultTransientFieldValues => options => defineReportFactoryInternal(options, defaultTransientFieldValues);
function isReportSectionreportFactory(x) {
    return x?._factoryFor === "Report";
}
function autoGenerateReportSectionScalarsOrEnums({ seq }) {
    return {
        type: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "ReportSection", fieldName: "type", isId: false, isUnique: false, seq }),
        templateInnerId: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "ReportSection", fieldName: "templateInnerId", isId: false, isUnique: true, seq })
    };
}
function defineReportSectionFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("ReportSection", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateReportSectionScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                report: isReportSectionreportFactory(defaultData.report) ? {
                    create: await defaultData.report.build()
                } : defaultData.report
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            reportSectionId: inputData.reportSectionId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().reportSection.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "ReportSection",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link ReportSection} model.
 *
 * @param options
 * @returns factory {@link ReportSectionFactoryInterface}
 */
exports.defineReportSectionFactory = ((options) => {
    return defineReportSectionFactoryInternal(options, {});
});
exports.defineReportSectionFactory.withTransientFields = defaultTransientFieldValues => options => defineReportSectionFactoryInternal(options, defaultTransientFieldValues);
function isReportSectionPlainTextreportSectionFactory(x) {
    return x?._factoryFor === "ReportSection";
}
function autoGenerateReportSectionPlainTextScalarsOrEnums({ seq }) {
    return {};
}
function defineReportSectionPlainTextFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("ReportSectionPlainText", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateReportSectionPlainTextScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                reportSection: isReportSectionPlainTextreportSectionFactory(defaultData.reportSection) ? {
                    create: await defaultData.reportSection.build()
                } : defaultData.reportSection
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            reportSectionPlainTextId: inputData.reportSectionPlainTextId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().reportSectionPlainText.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "ReportSectionPlainText",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link ReportSectionPlainText} model.
 *
 * @param options
 * @returns factory {@link ReportSectionPlainTextFactoryInterface}
 */
exports.defineReportSectionPlainTextFactory = ((options) => {
    return defineReportSectionPlainTextFactoryInternal(options, {});
});
exports.defineReportSectionPlainTextFactory.withTransientFields = defaultTransientFieldValues => options => defineReportSectionPlainTextFactoryInternal(options, defaultTransientFieldValues);
function isReportSectionImagereportSectionFactory(x) {
    return x?._factoryFor === "ReportSection";
}
function autoGenerateReportSectionImageScalarsOrEnums({ seq }) {
    return {};
}
function defineReportSectionImageFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("ReportSectionImage", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateReportSectionImageScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                reportSection: isReportSectionImagereportSectionFactory(defaultData.reportSection) ? {
                    create: await defaultData.reportSection.build()
                } : defaultData.reportSection
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            reportSectionImageId: inputData.reportSectionImageId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().reportSectionImage.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "ReportSectionImage",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link ReportSectionImage} model.
 *
 * @param options
 * @returns factory {@link ReportSectionImageFactoryInterface}
 */
exports.defineReportSectionImageFactory = ((options) => {
    return defineReportSectionImageFactoryInternal(options, {});
});
exports.defineReportSectionImageFactory.withTransientFields = defaultTransientFieldValues => options => defineReportSectionImageFactoryInternal(options, defaultTransientFieldValues);
function isReportSectionHorseConditionreportSectionFactory(x) {
    return x?._factoryFor === "ReportSection";
}
function autoGenerateReportSectionHorseConditionScalarsOrEnums({ seq }) {
    return {};
}
function defineReportSectionHorseConditionFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("ReportSectionHorseCondition", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateReportSectionHorseConditionScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                reportSection: isReportSectionHorseConditionreportSectionFactory(defaultData.reportSection) ? {
                    create: await defaultData.reportSection.build()
                } : defaultData.reportSection
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            reportSectionHorseConditionId: inputData.reportSectionHorseConditionId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().reportSectionHorseCondition.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "ReportSectionHorseCondition",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link ReportSectionHorseCondition} model.
 *
 * @param options
 * @returns factory {@link ReportSectionHorseConditionFactoryInterface}
 */
exports.defineReportSectionHorseConditionFactory = ((options) => {
    return defineReportSectionHorseConditionFactoryInternal(options, {});
});
exports.defineReportSectionHorseConditionFactory.withTransientFields = defaultTransientFieldValues => options => defineReportSectionHorseConditionFactoryInternal(options, defaultTransientFieldValues);
function isReportSectionWorkoutConditionreportSectionFactory(x) {
    return x?._factoryFor === "ReportSection";
}
function isReportSectionWorkoutConditionfacilityFactory(x) {
    return x?._factoryFor === "Facility";
}
function isReportSectionWorkoutConditiontrainingCourseFactory(x) {
    return x?._factoryFor === "TrainingCourse";
}
function autoGenerateReportSectionWorkoutConditionScalarsOrEnums({ seq }) {
    return {};
}
function defineReportSectionWorkoutConditionFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("ReportSectionWorkoutCondition", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateReportSectionWorkoutConditionScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                reportSection: isReportSectionWorkoutConditionreportSectionFactory(defaultData.reportSection) ? {
                    create: await defaultData.reportSection.build()
                } : defaultData.reportSection,
                facility: isReportSectionWorkoutConditionfacilityFactory(defaultData.facility) ? {
                    create: await defaultData.facility.build()
                } : defaultData.facility,
                trainingCourse: isReportSectionWorkoutConditiontrainingCourseFactory(defaultData.trainingCourse) ? {
                    create: await defaultData.trainingCourse.build()
                } : defaultData.trainingCourse
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            reportSectionWorkoutConditionId: inputData.reportSectionWorkoutConditionId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().reportSectionWorkoutCondition.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "ReportSectionWorkoutCondition",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link ReportSectionWorkoutCondition} model.
 *
 * @param options
 * @returns factory {@link ReportSectionWorkoutConditionFactoryInterface}
 */
exports.defineReportSectionWorkoutConditionFactory = ((options) => {
    return defineReportSectionWorkoutConditionFactoryInternal(options, {});
});
exports.defineReportSectionWorkoutConditionFactory.withTransientFields = defaultTransientFieldValues => options => defineReportSectionWorkoutConditionFactoryInternal(options, defaultTransientFieldValues);
function isReportSectionMonthlySummaryreportSectionFactory(x) {
    return x?._factoryFor === "ReportSection";
}
function autoGenerateReportSectionMonthlySummaryScalarsOrEnums({ seq }) {
    return {
        startYear: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "ReportSectionMonthlySummary", fieldName: "startYear", isId: false, isUnique: false, seq }),
        startMonth: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "ReportSectionMonthlySummary", fieldName: "startMonth", isId: false, isUnique: false, seq }),
        startDay: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "ReportSectionMonthlySummary", fieldName: "startDay", isId: false, isUnique: false, seq }),
        endYear: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "ReportSectionMonthlySummary", fieldName: "endYear", isId: false, isUnique: false, seq }),
        endMonth: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "ReportSectionMonthlySummary", fieldName: "endMonth", isId: false, isUnique: false, seq }),
        endDay: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "ReportSectionMonthlySummary", fieldName: "endDay", isId: false, isUnique: false, seq })
    };
}
function defineReportSectionMonthlySummaryFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("ReportSectionMonthlySummary", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateReportSectionMonthlySummaryScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                reportSection: isReportSectionMonthlySummaryreportSectionFactory(defaultData.reportSection) ? {
                    create: await defaultData.reportSection.build()
                } : defaultData.reportSection
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            reportSectionMonthlySummaryId: inputData.reportSectionMonthlySummaryId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().reportSectionMonthlySummary.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "ReportSectionMonthlySummary",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link ReportSectionMonthlySummary} model.
 *
 * @param options
 * @returns factory {@link ReportSectionMonthlySummaryFactoryInterface}
 */
exports.defineReportSectionMonthlySummaryFactory = ((options) => {
    return defineReportSectionMonthlySummaryFactoryInternal(options, {});
});
exports.defineReportSectionMonthlySummaryFactory.withTransientFields = defaultTransientFieldValues => options => defineReportSectionMonthlySummaryFactoryInternal(options, defaultTransientFieldValues);
function isReportSectionMonthlySummaryRaceRecordreportSectionMonthlySummaryFactory(x) {
    return x?._factoryFor === "ReportSectionMonthlySummary";
}
function autoGenerateReportSectionMonthlySummaryRaceRecordScalarsOrEnums({ seq }) {
    return {
        year: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "ReportSectionMonthlySummaryRaceRecord", fieldName: "year", isId: false, isUnique: false, seq }),
        month: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "ReportSectionMonthlySummaryRaceRecord", fieldName: "month", isId: false, isUnique: false, seq }),
        day: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "ReportSectionMonthlySummaryRaceRecord", fieldName: "day", isId: false, isUnique: false, seq })
    };
}
function defineReportSectionMonthlySummaryRaceRecordFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("ReportSectionMonthlySummaryRaceRecord", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateReportSectionMonthlySummaryRaceRecordScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                reportSectionMonthlySummary: isReportSectionMonthlySummaryRaceRecordreportSectionMonthlySummaryFactory(defaultData.reportSectionMonthlySummary) ? {
                    create: await defaultData.reportSectionMonthlySummary.build()
                } : defaultData.reportSectionMonthlySummary
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            reportSectionMonthlySummaryRaceRecordId: inputData.reportSectionMonthlySummaryRaceRecordId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().reportSectionMonthlySummaryRaceRecord.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "ReportSectionMonthlySummaryRaceRecord",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link ReportSectionMonthlySummaryRaceRecord} model.
 *
 * @param options
 * @returns factory {@link ReportSectionMonthlySummaryRaceRecordFactoryInterface}
 */
exports.defineReportSectionMonthlySummaryRaceRecordFactory = ((options) => {
    return defineReportSectionMonthlySummaryRaceRecordFactoryInternal(options, {});
});
exports.defineReportSectionMonthlySummaryRaceRecordFactory.withTransientFields = defaultTransientFieldValues => options => defineReportSectionMonthlySummaryRaceRecordFactoryInternal(options, defaultTransientFieldValues);
function isReportSectionMonthlyTimelinereportSectionFactory(x) {
    return x?._factoryFor === "ReportSection";
}
function autoGenerateReportSectionMonthlyTimelineScalarsOrEnums({ seq }) {
    return {};
}
function defineReportSectionMonthlyTimelineFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("ReportSectionMonthlyTimeline", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateReportSectionMonthlyTimelineScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                reportSection: isReportSectionMonthlyTimelinereportSectionFactory(defaultData.reportSection) ? {
                    create: await defaultData.reportSection.build()
                } : defaultData.reportSection
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            reportSectionMonthlyTimelineId: inputData.reportSectionMonthlyTimelineId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().reportSectionMonthlyTimeline.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "ReportSectionMonthlyTimeline",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link ReportSectionMonthlyTimeline} model.
 *
 * @param options
 * @returns factory {@link ReportSectionMonthlyTimelineFactoryInterface}
 */
exports.defineReportSectionMonthlyTimelineFactory = ((options) => {
    return defineReportSectionMonthlyTimelineFactoryInternal(options, {});
});
exports.defineReportSectionMonthlyTimelineFactory.withTransientFields = defaultTransientFieldValues => options => defineReportSectionMonthlyTimelineFactoryInternal(options, defaultTransientFieldValues);
function isReportSectionMonthlyTimelineRecordreportSectionMonthlyTimelineFactory(x) {
    return x?._factoryFor === "ReportSectionMonthlyTimeline";
}
function autoGenerateReportSectionMonthlyTimelineRecordScalarsOrEnums({ seq }) {
    return {
        year: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "ReportSectionMonthlyTimelineRecord", fieldName: "year", isId: false, isUnique: false, seq }),
        month: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "ReportSectionMonthlyTimelineRecord", fieldName: "month", isId: false, isUnique: false, seq }),
        day: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "ReportSectionMonthlyTimelineRecord", fieldName: "day", isId: false, isUnique: false, seq }),
        index: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "ReportSectionMonthlyTimelineRecord", fieldName: "index", isId: false, isUnique: false, seq })
    };
}
function defineReportSectionMonthlyTimelineRecordFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("ReportSectionMonthlyTimelineRecord", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateReportSectionMonthlyTimelineRecordScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                reportSectionMonthlyTimeline: isReportSectionMonthlyTimelineRecordreportSectionMonthlyTimelineFactory(defaultData.reportSectionMonthlyTimeline) ? {
                    create: await defaultData.reportSectionMonthlyTimeline.build()
                } : defaultData.reportSectionMonthlyTimeline
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            reportSectionMonthlyTimelineRecordId: inputData.reportSectionMonthlyTimelineRecordId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().reportSectionMonthlyTimelineRecord.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "ReportSectionMonthlyTimelineRecord",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link ReportSectionMonthlyTimelineRecord} model.
 *
 * @param options
 * @returns factory {@link ReportSectionMonthlyTimelineRecordFactoryInterface}
 */
exports.defineReportSectionMonthlyTimelineRecordFactory = ((options) => {
    return defineReportSectionMonthlyTimelineRecordFactoryInternal(options, {});
});
exports.defineReportSectionMonthlyTimelineRecordFactory.withTransientFields = defaultTransientFieldValues => options => defineReportSectionMonthlyTimelineRecordFactoryInternal(options, defaultTransientFieldValues);
function isReportSectionMedicalTreatmentreportSectionFactory(x) {
    return x?._factoryFor === "ReportSection";
}
function autoGenerateReportSectionMedicalTreatmentScalarsOrEnums({ seq }) {
    return {};
}
function defineReportSectionMedicalTreatmentFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("ReportSectionMedicalTreatment", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateReportSectionMedicalTreatmentScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                reportSection: isReportSectionMedicalTreatmentreportSectionFactory(defaultData.reportSection) ? {
                    create: await defaultData.reportSection.build()
                } : defaultData.reportSection
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            reportSectionMedicalTreatmentId: inputData.reportSectionMedicalTreatmentId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().reportSectionMedicalTreatment.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "ReportSectionMedicalTreatment",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link ReportSectionMedicalTreatment} model.
 *
 * @param options
 * @returns factory {@link ReportSectionMedicalTreatmentFactoryInterface}
 */
exports.defineReportSectionMedicalTreatmentFactory = ((options) => {
    return defineReportSectionMedicalTreatmentFactoryInternal(options, {});
});
exports.defineReportSectionMedicalTreatmentFactory.withTransientFields = defaultTransientFieldValues => options => defineReportSectionMedicalTreatmentFactoryInternal(options, defaultTransientFieldValues);
function isReportSectionMedicalTreatmentRecordreportSectionMedicalTreatmentFactory(x) {
    return x?._factoryFor === "ReportSectionMedicalTreatment";
}
function autoGenerateReportSectionMedicalTreatmentRecordScalarsOrEnums({ seq }) {
    return {
        year: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "ReportSectionMedicalTreatmentRecord", fieldName: "year", isId: false, isUnique: false, seq }),
        month: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "ReportSectionMedicalTreatmentRecord", fieldName: "month", isId: false, isUnique: false, seq }),
        day: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "ReportSectionMedicalTreatmentRecord", fieldName: "day", isId: false, isUnique: false, seq })
    };
}
function defineReportSectionMedicalTreatmentRecordFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("ReportSectionMedicalTreatmentRecord", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateReportSectionMedicalTreatmentRecordScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                reportSectionMedicalTreatment: isReportSectionMedicalTreatmentRecordreportSectionMedicalTreatmentFactory(defaultData.reportSectionMedicalTreatment) ? {
                    create: await defaultData.reportSectionMedicalTreatment.build()
                } : defaultData.reportSectionMedicalTreatment
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            reportSectionMedicalTreatmentRecordId: inputData.reportSectionMedicalTreatmentRecordId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().reportSectionMedicalTreatmentRecord.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "ReportSectionMedicalTreatmentRecord",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link ReportSectionMedicalTreatmentRecord} model.
 *
 * @param options
 * @returns factory {@link ReportSectionMedicalTreatmentRecordFactoryInterface}
 */
exports.defineReportSectionMedicalTreatmentRecordFactory = ((options) => {
    return defineReportSectionMedicalTreatmentRecordFactoryInternal(options, {});
});
exports.defineReportSectionMedicalTreatmentRecordFactory.withTransientFields = defaultTransientFieldValues => options => defineReportSectionMedicalTreatmentRecordFactoryInternal(options, defaultTransientFieldValues);
function isReportSectionMedicalTreatmentAffectedAreaPhotoreportSectionMedicalTreatmentRecordFactory(x) {
    return x?._factoryFor === "ReportSectionMedicalTreatmentRecord";
}
function autoGenerateReportSectionMedicalTreatmentAffectedAreaPhotoScalarsOrEnums({ seq }) {
    return {
        photoPath: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "ReportSectionMedicalTreatmentAffectedAreaPhoto", fieldName: "photoPath", isId: false, isUnique: false, seq })
    };
}
function defineReportSectionMedicalTreatmentAffectedAreaPhotoFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("ReportSectionMedicalTreatmentAffectedAreaPhoto", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateReportSectionMedicalTreatmentAffectedAreaPhotoScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                reportSectionMedicalTreatmentRecord: isReportSectionMedicalTreatmentAffectedAreaPhotoreportSectionMedicalTreatmentRecordFactory(defaultData.reportSectionMedicalTreatmentRecord) ? {
                    create: await defaultData.reportSectionMedicalTreatmentRecord.build()
                } : defaultData.reportSectionMedicalTreatmentRecord
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            reportSectionMedicalTreatmentAffectedAreaPhotoId: inputData.reportSectionMedicalTreatmentAffectedAreaPhotoId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().reportSectionMedicalTreatmentAffectedAreaPhoto.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "ReportSectionMedicalTreatmentAffectedAreaPhoto",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link ReportSectionMedicalTreatmentAffectedAreaPhoto} model.
 *
 * @param options
 * @returns factory {@link ReportSectionMedicalTreatmentAffectedAreaPhotoFactoryInterface}
 */
exports.defineReportSectionMedicalTreatmentAffectedAreaPhotoFactory = ((options) => {
    return defineReportSectionMedicalTreatmentAffectedAreaPhotoFactoryInternal(options, {});
});
exports.defineReportSectionMedicalTreatmentAffectedAreaPhotoFactory.withTransientFields = defaultTransientFieldValues => options => defineReportSectionMedicalTreatmentAffectedAreaPhotoFactoryInternal(options, defaultTransientFieldValues);
function isSentReportreportFactory(x) {
    return x?._factoryFor === "Report";
}
function isSentReportorganizationOwnerFactory(x) {
    return x?._factoryFor === "OrganizationOwner";
}
function isSentReportownerHorseFactory(x) {
    return x?._factoryFor === "OwnerHorse";
}
function autoGenerateSentReportScalarsOrEnums({ seq }) {
    return {
        sentReportId: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "SentReport", fieldName: "sentReportId", isId: true, isUnique: false, seq })
    };
}
function defineSentReportFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("SentReport", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateSentReportScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                report: isSentReportreportFactory(defaultData.report) ? {
                    create: await defaultData.report.build()
                } : defaultData.report,
                organizationOwner: isSentReportorganizationOwnerFactory(defaultData.organizationOwner) ? {
                    create: await defaultData.organizationOwner.build()
                } : defaultData.organizationOwner,
                ownerHorse: isSentReportownerHorseFactory(defaultData.ownerHorse) ? {
                    create: await defaultData.ownerHorse.build()
                } : defaultData.ownerHorse
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            sentReportId: inputData.sentReportId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().sentReport.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "SentReport",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link SentReport} model.
 *
 * @param options
 * @returns factory {@link SentReportFactoryInterface}
 */
exports.defineSentReportFactory = ((options) => {
    return defineSentReportFactoryInternal(options, {});
});
exports.defineSentReportFactory.withTransientFields = defaultTransientFieldValues => options => defineSentReportFactoryInternal(options, defaultTransientFieldValues);
function isShareReportreportFactory(x) {
    return x?._factoryFor === "Report";
}
function autoGenerateShareReportScalarsOrEnums({ seq }) {
    return {
        shareReportId: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "ShareReport", fieldName: "shareReportId", isId: true, isUnique: false, seq })
    };
}
function defineShareReportFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("ShareReport", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateShareReportScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                report: isShareReportreportFactory(defaultData.report) ? {
                    create: await defaultData.report.build()
                } : defaultData.report
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            shareReportId: inputData.shareReportId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().shareReport.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "ShareReport",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link ShareReport} model.
 *
 * @param options
 * @returns factory {@link ShareReportFactoryInterface}
 */
exports.defineShareReportFactory = ((options) => {
    return defineShareReportFactoryInternal(options, {});
});
exports.defineShareReportFactory.withTransientFields = defaultTransientFieldValues => options => defineShareReportFactoryInternal(options, defaultTransientFieldValues);
function isPendingSendReportreportFactory(x) {
    return x?._factoryFor === "Report";
}
function isPendingSendReportorganizationOwnerFactory(x) {
    return x?._factoryFor === "OrganizationOwner";
}
function isPendingSendReporthorseFactory(x) {
    return x?._factoryFor === "Horse";
}
function autoGeneratePendingSendReportScalarsOrEnums({ seq }) {
    return {};
}
function definePendingSendReportFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("PendingSendReport", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGeneratePendingSendReportScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                report: isPendingSendReportreportFactory(defaultData.report) ? {
                    create: await defaultData.report.build()
                } : defaultData.report,
                organizationOwner: isPendingSendReportorganizationOwnerFactory(defaultData.organizationOwner) ? {
                    create: await defaultData.organizationOwner.build()
                } : defaultData.organizationOwner,
                horse: isPendingSendReporthorseFactory(defaultData.horse) ? {
                    create: await defaultData.horse.build()
                } : defaultData.horse
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            pendingSendReportInternalId: inputData.pendingSendReportInternalId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().pendingSendReport.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "PendingSendReport",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link PendingSendReport} model.
 *
 * @param options
 * @returns factory {@link PendingSendReportFactoryInterface}
 */
exports.definePendingSendReportFactory = ((options) => {
    return definePendingSendReportFactoryInternal(options, {});
});
exports.definePendingSendReportFactory.withTransientFields = defaultTransientFieldValues => options => definePendingSendReportFactoryInternal(options, defaultTransientFieldValues);
function isReportGenerateRequesthorseFactory(x) {
    return x?._factoryFor === "Horse";
}
function isReportGenerateRequestreportFactory(x) {
    return x?._factoryFor === "Report";
}
function isReportGenerateRequestaiGenerateLogFactory(x) {
    return x?._factoryFor === "AiGenerateLog";
}
function autoGenerateReportGenerateRequestScalarsOrEnums({ seq }) {
    return {
        reportGenerateRequestId: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "ReportGenerateRequest", fieldName: "reportGenerateRequestId", isId: true, isUnique: false, seq })
    };
}
function defineReportGenerateRequestFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("ReportGenerateRequest", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateReportGenerateRequestScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                horse: isReportGenerateRequesthorseFactory(defaultData.horse) ? {
                    create: await defaultData.horse.build()
                } : defaultData.horse,
                report: isReportGenerateRequestreportFactory(defaultData.report) ? {
                    create: await defaultData.report.build()
                } : defaultData.report,
                aiGenerateLog: isReportGenerateRequestaiGenerateLogFactory(defaultData.aiGenerateLog) ? {
                    create: await defaultData.aiGenerateLog.build()
                } : defaultData.aiGenerateLog
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            reportGenerateRequestId: inputData.reportGenerateRequestId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().reportGenerateRequest.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "ReportGenerateRequest",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link ReportGenerateRequest} model.
 *
 * @param options
 * @returns factory {@link ReportGenerateRequestFactoryInterface}
 */
exports.defineReportGenerateRequestFactory = ((options) => {
    return defineReportGenerateRequestFactoryInternal(options, {});
});
exports.defineReportGenerateRequestFactory.withTransientFields = defaultTransientFieldValues => options => defineReportGenerateRequestFactoryInternal(options, defaultTransientFieldValues);
function isStableTmOutsideFarmorganizationFactory(x) {
    return x?._factoryFor === "Organization";
}
function isStableTmOutsideFarmfarmAreaFactory(x) {
    return x?._factoryFor === "FarmArea";
}
function isStableTmOutsideFarmmasterFarmFactory(x) {
    return x?._factoryFor === "MasterFarm";
}
function autoGenerateStableTmOutsideFarmScalarsOrEnums({ seq }) {
    return {
        outsideFarmId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "StableTmOutsideFarm", fieldName: "outsideFarmId", isId: true, isUnique: false, seq }),
        name: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "StableTmOutsideFarm", fieldName: "name", isId: false, isUnique: false, seq })
    };
}
function defineStableTmOutsideFarmFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("StableTmOutsideFarm", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateStableTmOutsideFarmScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                organization: isStableTmOutsideFarmorganizationFactory(defaultData.organization) ? {
                    create: await defaultData.organization.build()
                } : defaultData.organization,
                farmArea: isStableTmOutsideFarmfarmAreaFactory(defaultData.farmArea) ? {
                    create: await defaultData.farmArea.build()
                } : defaultData.farmArea,
                masterFarm: isStableTmOutsideFarmmasterFarmFactory(defaultData.masterFarm) ? {
                    create: await defaultData.masterFarm.build()
                } : defaultData.masterFarm
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            outsideFarmId: inputData.outsideFarmId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().stableTmOutsideFarm.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "StableTmOutsideFarm",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link StableTmOutsideFarm} model.
 *
 * @param options
 * @returns factory {@link StableTmOutsideFarmFactoryInterface}
 */
exports.defineStableTmOutsideFarmFactory = ((options) => {
    return defineStableTmOutsideFarmFactoryInternal(options, {});
});
exports.defineStableTmOutsideFarmFactory.withTransientFields = defaultTransientFieldValues => options => defineStableTmOutsideFarmFactoryInternal(options, defaultTransientFieldValues);
function isStableTmSectionstableFactory(x) {
    return x?._factoryFor === "Stable";
}
function autoGenerateStableTmSectionScalarsOrEnums({ seq }) {
    return {
        sectionId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "StableTmSection", fieldName: "sectionId", isId: true, isUnique: false, seq }),
        startDate: (0, internal_1.getScalarFieldValueGenerator)().DateTime({ modelName: "StableTmSection", fieldName: "startDate", isId: false, isUnique: false, seq }),
        endDate: (0, internal_1.getScalarFieldValueGenerator)().DateTime({ modelName: "StableTmSection", fieldName: "endDate", isId: false, isUnique: false, seq })
    };
}
function defineStableTmSectionFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("StableTmSection", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateStableTmSectionScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                stable: isStableTmSectionstableFactory(defaultData.stable) ? {
                    create: await defaultData.stable.build()
                } : defaultData.stable
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            sectionId: inputData.sectionId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().stableTmSection.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "StableTmSection",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link StableTmSection} model.
 *
 * @param options
 * @returns factory {@link StableTmSectionFactoryInterface}
 */
exports.defineStableTmSectionFactory = ((options) => {
    return defineStableTmSectionFactoryInternal(options, {});
});
exports.defineStableTmSectionFactory.withTransientFields = defaultTransientFieldValues => options => defineStableTmSectionFactoryInternal(options, defaultTransientFieldValues);
function isStableTmFixedSlotsectionFactory(x) {
    return x?._factoryFor === "StableTmSection";
}
function autoGenerateStableTmFixedSlotScalarsOrEnums({ seq }) {
    return {
        fixedSlotId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "StableTmFixedSlot", fieldName: "fixedSlotId", isId: true, isUnique: false, seq }),
        numberOfSection: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "StableTmFixedSlot", fieldName: "numberOfSection", isId: false, isUnique: false, seq }),
        slotNum: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "StableTmFixedSlot", fieldName: "slotNum", isId: false, isUnique: false, seq })
    };
}
function defineStableTmFixedSlotFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("StableTmFixedSlot", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateStableTmFixedSlotScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                section: isStableTmFixedSlotsectionFactory(defaultData.section) ? {
                    create: await defaultData.section.build()
                } : defaultData.section
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            fixedSlotId: inputData.fixedSlotId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().stableTmFixedSlot.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "StableTmFixedSlot",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link StableTmFixedSlot} model.
 *
 * @param options
 * @returns factory {@link StableTmFixedSlotFactoryInterface}
 */
exports.defineStableTmFixedSlotFactory = ((options) => {
    return defineStableTmFixedSlotFactoryInternal(options, {});
});
exports.defineStableTmFixedSlotFactory.withTransientFields = defaultTransientFieldValues => options => defineStableTmFixedSlotFactoryInternal(options, defaultTransientFieldValues);
function isStableTmTransportQueueTicketsectionFactory(x) {
    return x?._factoryFor === "StableTmSection";
}
function autoGenerateStableTmTransportQueueTicketScalarsOrEnums({ seq }) {
    return {
        transportQueueTicketId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "StableTmTransportQueueTicket", fieldName: "transportQueueTicketId", isId: true, isUnique: false, seq }),
        ticketKey: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "StableTmTransportQueueTicket", fieldName: "ticketKey", isId: false, isUnique: false, seq })
    };
}
function defineStableTmTransportQueueTicketFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("StableTmTransportQueueTicket", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateStableTmTransportQueueTicketScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                section: isStableTmTransportQueueTicketsectionFactory(defaultData.section) ? {
                    create: await defaultData.section.build()
                } : defaultData.section
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            transportQueueTicketId: inputData.transportQueueTicketId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().stableTmTransportQueueTicket.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "StableTmTransportQueueTicket",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link StableTmTransportQueueTicket} model.
 *
 * @param options
 * @returns factory {@link StableTmTransportQueueTicketFactoryInterface}
 */
exports.defineStableTmTransportQueueTicketFactory = ((options) => {
    return defineStableTmTransportQueueTicketFactoryInternal(options, {});
});
exports.defineStableTmTransportQueueTicketFactory.withTransientFields = defaultTransientFieldValues => options => defineStableTmTransportQueueTicketFactoryInternal(options, defaultTransientFieldValues);
function isStableTmTransportDailyRecordsectionFactory(x) {
    return x?._factoryFor === "StableTmSection";
}
function isStableTmTransportDailyRecordstableFactory(x) {
    return x?._factoryFor === "Stable";
}
function autoGenerateStableTmTransportDailyRecordScalarsOrEnums({ seq }) {
    return {
        transportDailyRecordId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "StableTmTransportDailyRecord", fieldName: "transportDailyRecordId", isId: true, isUnique: false, seq }),
        year: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "StableTmTransportDailyRecord", fieldName: "year", isId: false, isUnique: false, seq }),
        month: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "StableTmTransportDailyRecord", fieldName: "month", isId: false, isUnique: false, seq }),
        day: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "StableTmTransportDailyRecord", fieldName: "day", isId: false, isUnique: false, seq })
    };
}
function defineStableTmTransportDailyRecordFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("StableTmTransportDailyRecord", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateStableTmTransportDailyRecordScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                section: isStableTmTransportDailyRecordsectionFactory(defaultData.section) ? {
                    create: await defaultData.section.build()
                } : defaultData.section,
                stable: isStableTmTransportDailyRecordstableFactory(defaultData.stable) ? {
                    create: await defaultData.stable.build()
                } : defaultData.stable
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            transportDailyRecordId: inputData.transportDailyRecordId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().stableTmTransportDailyRecord.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "StableTmTransportDailyRecord",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link StableTmTransportDailyRecord} model.
 *
 * @param options
 * @returns factory {@link StableTmTransportDailyRecordFactoryInterface}
 */
exports.defineStableTmTransportDailyRecordFactory = ((options) => {
    return defineStableTmTransportDailyRecordFactoryInternal(options, {});
});
exports.defineStableTmTransportDailyRecordFactory.withTransientFields = defaultTransientFieldValues => options => defineStableTmTransportDailyRecordFactoryInternal(options, defaultTransientFieldValues);
function isStableTmTransportRecordtransportDailyRecordFactory(x) {
    return x?._factoryFor === "StableTmTransportDailyRecord";
}
function isStableTmTransportRecordhorseFactory(x) {
    return x?._factoryFor === "Horse";
}
function isStableTmTransportRecordtransportInStatusFactory(x) {
    return x?._factoryFor === "StableTmTransportInStatus";
}
function isStableTmTransportRecordtransportOutStatusFactory(x) {
    return x?._factoryFor === "StableTmTransportOutStatus";
}
function autoGenerateStableTmTransportRecordScalarsOrEnums({ seq }) {
    return {
        transportRecordId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "StableTmTransportRecord", fieldName: "transportRecordId", isId: true, isUnique: false, seq }),
        type: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "StableTmTransportRecord", fieldName: "type", isId: false, isUnique: false, seq }),
        index: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "StableTmTransportRecord", fieldName: "index", isId: false, isUnique: false, seq })
    };
}
function defineStableTmTransportRecordFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("StableTmTransportRecord", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateStableTmTransportRecordScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                transportDailyRecord: isStableTmTransportRecordtransportDailyRecordFactory(defaultData.transportDailyRecord) ? {
                    create: await defaultData.transportDailyRecord.build()
                } : defaultData.transportDailyRecord,
                horse: isStableTmTransportRecordhorseFactory(defaultData.horse) ? {
                    create: await defaultData.horse.build()
                } : defaultData.horse,
                transportInStatus: isStableTmTransportRecordtransportInStatusFactory(defaultData.transportInStatus) ? {
                    create: await defaultData.transportInStatus.build()
                } : defaultData.transportInStatus,
                transportOutStatus: isStableTmTransportRecordtransportOutStatusFactory(defaultData.transportOutStatus) ? {
                    create: await defaultData.transportOutStatus.build()
                } : defaultData.transportOutStatus
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            transportRecordId: inputData.transportRecordId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().stableTmTransportRecord.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "StableTmTransportRecord",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link StableTmTransportRecord} model.
 *
 * @param options
 * @returns factory {@link StableTmTransportRecordFactoryInterface}
 */
exports.defineStableTmTransportRecordFactory = ((options) => {
    return defineStableTmTransportRecordFactoryInternal(options, {});
});
exports.defineStableTmTransportRecordFactory.withTransientFields = defaultTransientFieldValues => options => defineStableTmTransportRecordFactoryInternal(options, defaultTransientFieldValues);
function isStableTmTransportInStatustransportRecordFactory(x) {
    return x?._factoryFor === "StableTmTransportRecord";
}
function isStableTmTransportInStatusstaffFactory(x) {
    return x?._factoryFor === "Staff";
}
function autoGenerateStableTmTransportInStatusScalarsOrEnums({ seq }) {
    return {
        transportInStatusId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "StableTmTransportInStatus", fieldName: "transportInStatusId", isId: true, isUnique: false, seq })
    };
}
function defineStableTmTransportInStatusFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("StableTmTransportInStatus", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateStableTmTransportInStatusScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                transportRecord: isStableTmTransportInStatustransportRecordFactory(defaultData.transportRecord) ? {
                    create: await defaultData.transportRecord.build()
                } : defaultData.transportRecord,
                staff: isStableTmTransportInStatusstaffFactory(defaultData.staff) ? {
                    create: await defaultData.staff.build()
                } : defaultData.staff
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            transportInStatusId: inputData.transportInStatusId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().stableTmTransportInStatus.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "StableTmTransportInStatus",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link StableTmTransportInStatus} model.
 *
 * @param options
 * @returns factory {@link StableTmTransportInStatusFactoryInterface}
 */
exports.defineStableTmTransportInStatusFactory = ((options) => {
    return defineStableTmTransportInStatusFactoryInternal(options, {});
});
exports.defineStableTmTransportInStatusFactory.withTransientFields = defaultTransientFieldValues => options => defineStableTmTransportInStatusFactoryInternal(options, defaultTransientFieldValues);
function isStableTmTransportOutStatustransportRecordFactory(x) {
    return x?._factoryFor === "StableTmTransportRecord";
}
function isStableTmTransportOutStatusstaffFactory(x) {
    return x?._factoryFor === "Staff";
}
function isStableTmTransportOutStatusfarmFactory(x) {
    return x?._factoryFor === "StableTmOutsideFarm";
}
function autoGenerateStableTmTransportOutStatusScalarsOrEnums({ seq }) {
    return {
        transportOutStatusId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "StableTmTransportOutStatus", fieldName: "transportOutStatusId", isId: true, isUnique: false, seq })
    };
}
function defineStableTmTransportOutStatusFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("StableTmTransportOutStatus", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateStableTmTransportOutStatusScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                transportRecord: isStableTmTransportOutStatustransportRecordFactory(defaultData.transportRecord) ? {
                    create: await defaultData.transportRecord.build()
                } : defaultData.transportRecord,
                staff: isStableTmTransportOutStatusstaffFactory(defaultData.staff) ? {
                    create: await defaultData.staff.build()
                } : defaultData.staff,
                farm: isStableTmTransportOutStatusfarmFactory(defaultData.farm) ? {
                    create: await defaultData.farm.build()
                } : defaultData.farm
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            transportOutStatusId: inputData.transportOutStatusId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().stableTmTransportOutStatus.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "StableTmTransportOutStatus",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link StableTmTransportOutStatus} model.
 *
 * @param options
 * @returns factory {@link StableTmTransportOutStatusFactoryInterface}
 */
exports.defineStableTmTransportOutStatusFactory = ((options) => {
    return defineStableTmTransportOutStatusFactoryInternal(options, {});
});
exports.defineStableTmTransportOutStatusFactory.withTransientFields = defaultTransientFieldValues => options => defineStableTmTransportOutStatusFactoryInternal(options, defaultTransientFieldValues);
function isStableTmTransportOutHandoverNotetransportOutStatusFactory(x) {
    return x?._factoryFor === "StableTmTransportOutStatus";
}
function autoGenerateStableTmTransportOutHandoverNoteScalarsOrEnums({ seq }) {
    return {
        transportOutHandoverNoteId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "StableTmTransportOutHandoverNote", fieldName: "transportOutHandoverNoteId", isId: true, isUnique: false, seq })
    };
}
function defineStableTmTransportOutHandoverNoteFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("StableTmTransportOutHandoverNote", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateStableTmTransportOutHandoverNoteScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                transportOutStatus: isStableTmTransportOutHandoverNotetransportOutStatusFactory(defaultData.transportOutStatus) ? {
                    create: await defaultData.transportOutStatus.build()
                } : defaultData.transportOutStatus
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            transportOutHandoverNoteId: inputData.transportOutHandoverNoteId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().stableTmTransportOutHandoverNote.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "StableTmTransportOutHandoverNote",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link StableTmTransportOutHandoverNote} model.
 *
 * @param options
 * @returns factory {@link StableTmTransportOutHandoverNoteFactoryInterface}
 */
exports.defineStableTmTransportOutHandoverNoteFactory = ((options) => {
    return defineStableTmTransportOutHandoverNoteFactoryInternal(options, {});
});
exports.defineStableTmTransportOutHandoverNoteFactory.withTransientFields = defaultTransientFieldValues => options => defineStableTmTransportOutHandoverNoteFactoryInternal(options, defaultTransientFieldValues);
function autoGenerateOnetimeCodeScalarsOrEnums({ seq }) {
    return {
        code: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "OnetimeCode", fieldName: "code", isId: false, isUnique: false, seq }),
        sessionId: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "OnetimeCode", fieldName: "sessionId", isId: false, isUnique: true, seq }),
        expiresAt: (0, internal_1.getScalarFieldValueGenerator)().DateTime({ modelName: "OnetimeCode", fieldName: "expiresAt", isId: false, isUnique: false, seq })
    };
}
function defineOnetimeCodeFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("OnetimeCode", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateOnetimeCodeScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver ?? {});
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {};
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            onetimeCodeInternalId: inputData.onetimeCodeInternalId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().onetimeCode.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "OnetimeCode",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link OnetimeCode} model.
 *
 * @param options
 * @returns factory {@link OnetimeCodeFactoryInterface}
 */
exports.defineOnetimeCodeFactory = ((options) => {
    return defineOnetimeCodeFactoryInternal(options ?? {}, {});
});
exports.defineOnetimeCodeFactory.withTransientFields = defaultTransientFieldValues => options => defineOnetimeCodeFactoryInternal(options ?? {}, defaultTransientFieldValues);
function isUserTermsAcceptancesownerFactory(x) {
    return x?._factoryFor === "Owner";
}
function autoGenerateUserTermsAcceptancesScalarsOrEnums({ seq }) {
    return {
        acceptedAt: (0, internal_1.getScalarFieldValueGenerator)().DateTime({ modelName: "UserTermsAcceptances", fieldName: "acceptedAt", isId: false, isUnique: false, seq })
    };
}
function defineUserTermsAcceptancesFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("UserTermsAcceptances", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateUserTermsAcceptancesScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                owner: isUserTermsAcceptancesownerFactory(defaultData.owner) ? {
                    create: await defaultData.owner.build()
                } : defaultData.owner
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            userTermsAcceptanceId: inputData.userTermsAcceptanceId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().userTermsAcceptances.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "UserTermsAcceptances",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link UserTermsAcceptances} model.
 *
 * @param options
 * @returns factory {@link UserTermsAcceptancesFactoryInterface}
 */
exports.defineUserTermsAcceptancesFactory = ((options) => {
    return defineUserTermsAcceptancesFactoryInternal(options, {});
});
exports.defineUserTermsAcceptancesFactory.withTransientFields = defaultTransientFieldValues => options => defineUserTermsAcceptancesFactoryInternal(options, defaultTransientFieldValues);
function isHorseNoteUserDeviceuserFactory(x) {
    return x?._factoryFor === "User";
}
function autoGenerateHorseNoteUserDeviceScalarsOrEnums({ seq }) {
    return {
        deviceId: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "HorseNoteUserDevice", fieldName: "deviceId", isId: true, isUnique: false, seq })
    };
}
function defineHorseNoteUserDeviceFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("HorseNoteUserDevice", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateHorseNoteUserDeviceScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver ?? {});
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                user: isHorseNoteUserDeviceuserFactory(defaultData.user) ? {
                    create: await defaultData.user.build()
                } : defaultData.user
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            deviceId: inputData.deviceId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().horseNoteUserDevice.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "HorseNoteUserDevice",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link HorseNoteUserDevice} model.
 *
 * @param options
 * @returns factory {@link HorseNoteUserDeviceFactoryInterface}
 */
exports.defineHorseNoteUserDeviceFactory = ((options) => {
    return defineHorseNoteUserDeviceFactoryInternal(options ?? {}, {});
});
exports.defineHorseNoteUserDeviceFactory.withTransientFields = defaultTransientFieldValues => options => defineHorseNoteUserDeviceFactoryInternal(options ?? {}, defaultTransientFieldValues);
function isFeatureFlagorganizationFactory(x) {
    return x?._factoryFor === "Organization";
}
function autoGenerateFeatureFlagScalarsOrEnums({ seq }) {
    return {};
}
function defineFeatureFlagFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("FeatureFlag", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateFeatureFlagScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                organization: isFeatureFlagorganizationFactory(defaultData.organization) ? {
                    create: await defaultData.organization.build()
                } : defaultData.organization
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            featureFlagId: inputData.featureFlagId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().featureFlag.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "FeatureFlag",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link FeatureFlag} model.
 *
 * @param options
 * @returns factory {@link FeatureFlagFactoryInterface}
 */
exports.defineFeatureFlagFactory = ((options) => {
    return defineFeatureFlagFactoryInternal(options, {});
});
exports.defineFeatureFlagFactory.withTransientFields = defaultTransientFieldValues => options => defineFeatureFlagFactoryInternal(options, defaultTransientFieldValues);
function autoGenerateBatchJobScalarsOrEnums({ seq }) {
    return {
        batchJobId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "BatchJob", fieldName: "batchJobId", isId: true, isUnique: false, seq }),
        name: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "BatchJob", fieldName: "name", isId: false, isUnique: false, seq })
    };
}
function defineBatchJobFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("BatchJob", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateBatchJobScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver ?? {});
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {};
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            batchJobId: inputData.batchJobId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().batchJob.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "BatchJob",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link BatchJob} model.
 *
 * @param options
 * @returns factory {@link BatchJobFactoryInterface}
 */
exports.defineBatchJobFactory = ((options) => {
    return defineBatchJobFactoryInternal(options ?? {}, {});
});
exports.defineBatchJobFactory.withTransientFields = defaultTransientFieldValues => options => defineBatchJobFactoryInternal(options ?? {}, defaultTransientFieldValues);
function isBatchLogbatchJobFactory(x) {
    return x?._factoryFor === "BatchJob";
}
function autoGenerateBatchLogScalarsOrEnums({ seq }) {
    return {
        batchLogId: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "BatchLog", fieldName: "batchLogId", isId: true, isUnique: false, seq }),
        message: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "BatchLog", fieldName: "message", isId: false, isUnique: false, seq })
    };
}
function defineBatchLogFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("BatchLog", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateBatchLogScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver ?? {});
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                batchJob: isBatchLogbatchJobFactory(defaultData.batchJob) ? {
                    create: await defaultData.batchJob.build()
                } : defaultData.batchJob
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            batchLogId: inputData.batchLogId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().batchLog.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "BatchLog",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link BatchLog} model.
 *
 * @param options
 * @returns factory {@link BatchLogFactoryInterface}
 */
exports.defineBatchLogFactory = ((options) => {
    return defineBatchLogFactoryInternal(options ?? {}, {});
});
exports.defineBatchLogFactory.withTransientFields = defaultTransientFieldValues => options => defineBatchLogFactoryInternal(options ?? {}, defaultTransientFieldValues);
function isTraininghorseFactory(x) {
    return x?._factoryFor === "Horse";
}
function isTrainingstableFactory(x) {
    return x?._factoryFor === "Stable";
}
function autoGenerateTrainingScalarsOrEnums({ seq }) {
    return {};
}
function defineTrainingFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("Training", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateTrainingScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver ?? {});
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                horse: isTraininghorseFactory(defaultData.horse) ? {
                    create: await defaultData.horse.build()
                } : defaultData.horse,
                stable: isTrainingstableFactory(defaultData.stable) ? {
                    create: await defaultData.stable.build()
                } : defaultData.stable
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            trainingId: inputData.trainingId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().training.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "Training",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link Training} model.
 *
 * @param options
 * @returns factory {@link TrainingFactoryInterface}
 */
exports.defineTrainingFactory = ((options) => {
    return defineTrainingFactoryInternal(options ?? {}, {});
});
exports.defineTrainingFactory.withTransientFields = defaultTransientFieldValues => options => defineTrainingFactoryInternal(options ?? {}, defaultTransientFieldValues);
function isTrainingIndicatortrainingFactory(x) {
    return x?._factoryFor === "Training";
}
function autoGenerateTrainingIndicatorScalarsOrEnums({ seq }) {
    return {
        periodGroupId: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "TrainingIndicator", fieldName: "periodGroupId", isId: false, isUnique: false, seq })
    };
}
function defineTrainingIndicatorFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("TrainingIndicator", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateTrainingIndicatorScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                training: isTrainingIndicatortrainingFactory(defaultData.training) ? {
                    create: await defaultData.training.build()
                } : defaultData.training
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            trainingIndicatorId: inputData.trainingIndicatorId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().trainingIndicator.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "TrainingIndicator",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link TrainingIndicator} model.
 *
 * @param options
 * @returns factory {@link TrainingIndicatorFactoryInterface}
 */
exports.defineTrainingIndicatorFactory = ((options) => {
    return defineTrainingIndicatorFactoryInternal(options, {});
});
exports.defineTrainingIndicatorFactory.withTransientFields = defaultTransientFieldValues => options => defineTrainingIndicatorFactoryInternal(options, defaultTransientFieldValues);
function isTrainingPeriodtrainingFactory(x) {
    return x?._factoryFor === "Training";
}
function isTrainingPeriodtrainingCourseFactory(x) {
    return x?._factoryFor === "TrainingCourse";
}
function autoGenerateTrainingPeriodScalarsOrEnums({ seq }) {
    return {
        periodGroupId: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "TrainingPeriod", fieldName: "periodGroupId", isId: false, isUnique: true, seq }),
        periodType: "all",
        startTime: (0, internal_1.getScalarFieldValueGenerator)().Float({ modelName: "TrainingPeriod", fieldName: "startTime", isId: false, isUnique: false, seq }),
        endTime: (0, internal_1.getScalarFieldValueGenerator)().Float({ modelName: "TrainingPeriod", fieldName: "endTime", isId: false, isUnique: false, seq }),
        startDistance: (0, internal_1.getScalarFieldValueGenerator)().Float({ modelName: "TrainingPeriod", fieldName: "startDistance", isId: false, isUnique: false, seq }),
        endDistance: (0, internal_1.getScalarFieldValueGenerator)().Float({ modelName: "TrainingPeriod", fieldName: "endDistance", isId: false, isUnique: false, seq })
    };
}
function defineTrainingPeriodFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("TrainingPeriod", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateTrainingPeriodScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                training: isTrainingPeriodtrainingFactory(defaultData.training) ? {
                    create: await defaultData.training.build()
                } : defaultData.training,
                trainingCourse: isTrainingPeriodtrainingCourseFactory(defaultData.trainingCourse) ? {
                    create: await defaultData.trainingCourse.build()
                } : defaultData.trainingCourse
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            trainingPeriodUuid: inputData.trainingPeriodUuid
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().trainingPeriod.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "TrainingPeriod",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link TrainingPeriod} model.
 *
 * @param options
 * @returns factory {@link TrainingPeriodFactoryInterface}
 */
exports.defineTrainingPeriodFactory = ((options) => {
    return defineTrainingPeriodFactoryInternal(options, {});
});
exports.defineTrainingPeriodFactory.withTransientFields = defaultTransientFieldValues => options => defineTrainingPeriodFactoryInternal(options, defaultTransientFieldValues);
function isTrainingIndicatorLabeltrainingIndicatorFactory(x) {
    return x?._factoryFor === "TrainingIndicator";
}
function autoGenerateTrainingIndicatorLabelScalarsOrEnums({ seq }) {
    return {
        label: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "TrainingIndicatorLabel", fieldName: "label", isId: false, isUnique: false, seq }),
        time: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "TrainingIndicatorLabel", fieldName: "time", isId: false, isUnique: false, seq }),
        distance: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "TrainingIndicatorLabel", fieldName: "distance", isId: false, isUnique: false, seq })
    };
}
function defineTrainingIndicatorLabelFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("TrainingIndicatorLabel", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateTrainingIndicatorLabelScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                trainingIndicator: isTrainingIndicatorLabeltrainingIndicatorFactory(defaultData.trainingIndicator) ? {
                    create: await defaultData.trainingIndicator.build()
                } : defaultData.trainingIndicator
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            trainingIndicatorLabelId: inputData.trainingIndicatorLabelId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().trainingIndicatorLabel.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "TrainingIndicatorLabel",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link TrainingIndicatorLabel} model.
 *
 * @param options
 * @returns factory {@link TrainingIndicatorLabelFactoryInterface}
 */
exports.defineTrainingIndicatorLabelFactory = ((options) => {
    return defineTrainingIndicatorLabelFactoryInternal(options, {});
});
exports.defineTrainingIndicatorLabelFactory.withTransientFields = defaultTransientFieldValues => options => defineTrainingIndicatorLabelFactoryInternal(options, defaultTransientFieldValues);
function isTimeSeriesHeartBeatResulttrainingFactory(x) {
    return x?._factoryFor === "Training";
}
function autoGenerateTimeSeriesHeartBeatResultScalarsOrEnums({ seq }) {
    return {
        time: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "TimeSeriesHeartBeatResult", fieldName: "time", isId: false, isUnique: true, seq })
    };
}
function defineTimeSeriesHeartBeatResultFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("TimeSeriesHeartBeatResult", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateTimeSeriesHeartBeatResultScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                training: isTimeSeriesHeartBeatResulttrainingFactory(defaultData.training) ? {
                    create: await defaultData.training.build()
                } : defaultData.training
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            timeSeriesHeartBeatResultId: inputData.timeSeriesHeartBeatResultId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().timeSeriesHeartBeatResult.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "TimeSeriesHeartBeatResult",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link TimeSeriesHeartBeatResult} model.
 *
 * @param options
 * @returns factory {@link TimeSeriesHeartBeatResultFactoryInterface}
 */
exports.defineTimeSeriesHeartBeatResultFactory = ((options) => {
    return defineTimeSeriesHeartBeatResultFactoryInternal(options, {});
});
exports.defineTimeSeriesHeartBeatResultFactory.withTransientFields = defaultTransientFieldValues => options => defineTimeSeriesHeartBeatResultFactoryInternal(options, defaultTransientFieldValues);
function isGaitAnalysisResulttrainingFactory(x) {
    return x?._factoryFor === "Training";
}
function autoGenerateGaitAnalysisResultScalarsOrEnums({ seq }) {
    return {
        startAt: (0, internal_1.getScalarFieldValueGenerator)().DateTime({ modelName: "GaitAnalysisResult", fieldName: "startAt", isId: false, isUnique: false, seq }),
        endAt: (0, internal_1.getScalarFieldValueGenerator)().DateTime({ modelName: "GaitAnalysisResult", fieldName: "endAt", isId: false, isUnique: false, seq })
    };
}
function defineGaitAnalysisResultFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("GaitAnalysisResult", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateGaitAnalysisResultScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                training: isGaitAnalysisResulttrainingFactory(defaultData.training) ? {
                    create: await defaultData.training.build()
                } : defaultData.training
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            gaitAnalysisResultUuid: inputData.gaitAnalysisResultUuid
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().gaitAnalysisResult.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "GaitAnalysisResult",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link GaitAnalysisResult} model.
 *
 * @param options
 * @returns factory {@link GaitAnalysisResultFactoryInterface}
 */
exports.defineGaitAnalysisResultFactory = ((options) => {
    return defineGaitAnalysisResultFactoryInternal(options, {});
});
exports.defineGaitAnalysisResultFactory.withTransientFields = defaultTransientFieldValues => options => defineGaitAnalysisResultFactoryInternal(options, defaultTransientFieldValues);
function isHorseCoursePitchAveragemasterHorseFactory(x) {
    return x?._factoryFor === "MasterHorse";
}
function isHorseCoursePitchAveragecourseFactory(x) {
    return x?._factoryFor === "TrainingCourse";
}
function autoGenerateHorseCoursePitchAverageScalarsOrEnums({ seq }) {
    return {
        speed: (0, internal_1.getScalarFieldValueGenerator)().Int({ modelName: "HorseCoursePitchAverage", fieldName: "speed", isId: false, isUnique: true, seq })
    };
}
function defineHorseCoursePitchAverageFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("HorseCoursePitchAverage", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateHorseCoursePitchAverageScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                masterHorse: isHorseCoursePitchAveragemasterHorseFactory(defaultData.masterHorse) ? {
                    create: await defaultData.masterHorse.build()
                } : defaultData.masterHorse,
                course: isHorseCoursePitchAveragecourseFactory(defaultData.course) ? {
                    create: await defaultData.course.build()
                } : defaultData.course
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            horseCoursePitchAverageUuid: inputData.horseCoursePitchAverageUuid
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().horseCoursePitchAverage.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "HorseCoursePitchAverage",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link HorseCoursePitchAverage} model.
 *
 * @param options
 * @returns factory {@link HorseCoursePitchAverageFactoryInterface}
 */
exports.defineHorseCoursePitchAverageFactory = ((options) => {
    return defineHorseCoursePitchAverageFactoryInternal(options, {});
});
exports.defineHorseCoursePitchAverageFactory.withTransientFields = defaultTransientFieldValues => options => defineHorseCoursePitchAverageFactoryInternal(options, defaultTransientFieldValues);
function isTrainingMenustableFactory(x) {
    return x?._factoryFor === "Stable";
}
function autoGenerateTrainingMenuScalarsOrEnums({ seq }) {
    return {
        trainingMenuUuid: (0, internal_1.getScalarFieldValueGenerator)().Bytes({ modelName: "TrainingMenu", fieldName: "trainingMenuUuid", isId: true, isUnique: false, seq }),
        trainingMenuName: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "TrainingMenu", fieldName: "trainingMenuName", isId: false, isUnique: false, seq })
    };
}
function defineTrainingMenuFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("TrainingMenu", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateTrainingMenuScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver);
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                stable: isTrainingMenustableFactory(defaultData.stable) ? {
                    create: await defaultData.stable.build()
                } : defaultData.stable
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            trainingMenuUuid: inputData.trainingMenuUuid
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().trainingMenu.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "TrainingMenu",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link TrainingMenu} model.
 *
 * @param options
 * @returns factory {@link TrainingMenuFactoryInterface}
 */
exports.defineTrainingMenuFactory = ((options) => {
    return defineTrainingMenuFactoryInternal(options, {});
});
exports.defineTrainingMenuFactory.withTransientFields = defaultTransientFieldValues => options => defineTrainingMenuFactoryInternal(options, defaultTransientFieldValues);
function isTrainersUserSettingsuserFactory(x) {
    return x?._factoryFor === "User";
}
function autoGenerateTrainersUserSettingsScalarsOrEnums({ seq }) {
    return {};
}
function defineTrainersUserSettingsFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("TrainersUserSettings", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateTrainersUserSettingsScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver ?? {});
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                user: isTrainersUserSettingsuserFactory(defaultData.user) ? {
                    create: await defaultData.user.build()
                } : defaultData.user
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            trainersUserSettingsId: inputData.trainersUserSettingsId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().trainersUserSettings.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "TrainersUserSettings",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link TrainersUserSettings} model.
 *
 * @param options
 * @returns factory {@link TrainersUserSettingsFactoryInterface}
 */
exports.defineTrainersUserSettingsFactory = ((options) => {
    return defineTrainersUserSettingsFactoryInternal(options ?? {}, {});
});
exports.defineTrainersUserSettingsFactory.withTransientFields = defaultTransientFieldValues => options => defineTrainersUserSettingsFactoryInternal(options ?? {}, defaultTransientFieldValues);
function isUserLangSettinguserFactory(x) {
    return x?._factoryFor === "User";
}
function autoGenerateUserLangSettingScalarsOrEnums({ seq }) {
    return {
        lang: (0, internal_1.getScalarFieldValueGenerator)().String({ modelName: "UserLangSetting", fieldName: "lang", isId: false, isUnique: false, seq })
    };
}
function defineUserLangSettingFactoryInternal({ defaultData: defaultDataResolver, onAfterBuild, onBeforeCreate, onAfterCreate, traits: traitsDefs = {} }, defaultTransientFieldValues) {
    const getFactoryWithTraits = (traitKeys = []) => {
        const seqKey = {};
        const getSeq = () => (0, internal_1.getSequenceCounter)(seqKey);
        const screen = (0, internal_1.createScreener)("UserLangSetting", modelFieldDefinitions);
        const handleAfterBuild = (0, internal_1.createCallbackChain)([
            onAfterBuild,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterBuild),
        ]);
        const handleBeforeCreate = (0, internal_1.createCallbackChain)([
            ...traitKeys.slice().reverse().map(traitKey => traitsDefs[traitKey]?.onBeforeCreate),
            onBeforeCreate,
        ]);
        const handleAfterCreate = (0, internal_1.createCallbackChain)([
            onAfterCreate,
            ...traitKeys.map(traitKey => traitsDefs[traitKey]?.onAfterCreate),
        ]);
        const build = async (inputData = {}) => {
            const seq = getSeq();
            const requiredScalarData = autoGenerateUserLangSettingScalarsOrEnums({ seq });
            const resolveValue = (0, internal_1.normalizeResolver)(defaultDataResolver ?? {});
            const [transientFields, filteredInputData] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            const resolverInput = { seq, ...transientFields };
            const defaultData = await traitKeys.reduce(async (queue, traitKey) => {
                const acc = await queue;
                const resolveTraitValue = (0, internal_1.normalizeResolver)(traitsDefs[traitKey]?.data ?? {});
                const traitData = await resolveTraitValue(resolverInput);
                return {
                    ...acc,
                    ...traitData,
                };
            }, resolveValue(resolverInput));
            const defaultAssociations = {
                user: isUserLangSettinguserFactory(defaultData.user) ? {
                    create: await defaultData.user.build()
                } : defaultData.user
            };
            const data = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
            await handleAfterBuild(data, transientFields);
            return data;
        };
        const buildList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => build(data)));
        const pickForConnect = (inputData) => ({
            userLangSettingId: inputData.userLangSettingId
        });
        const create = async (inputData = {}) => {
            const data = await build({ ...inputData }).then(screen);
            const [transientFields] = (0, internal_1.destructure)(defaultTransientFieldValues, inputData);
            await handleBeforeCreate(data, transientFields);
            const createdData = await getClient().userLangSetting.create({ data });
            await handleAfterCreate(createdData, transientFields);
            return createdData;
        };
        const createList = (...args) => Promise.all((0, internal_1.normalizeList)(...args).map(data => create(data)));
        const createForConnect = (inputData = {}) => create(inputData).then(pickForConnect);
        return {
            _factoryFor: "UserLangSetting",
            build,
            buildList,
            buildCreateInput: build,
            pickForConnect,
            create,
            createList,
            createForConnect,
        };
    };
    const factory = getFactoryWithTraits();
    const useTraits = (name, ...names) => {
        return getFactoryWithTraits([name, ...names]);
    };
    return {
        ...factory,
        use: useTraits,
    };
}
/**
 * Define factory for {@link UserLangSetting} model.
 *
 * @param options
 * @returns factory {@link UserLangSettingFactoryInterface}
 */
exports.defineUserLangSettingFactory = ((options) => {
    return defineUserLangSettingFactoryInternal(options ?? {}, {});
});
exports.defineUserLangSettingFactory.withTransientFields = defaultTransientFieldValues => options => defineUserLangSettingFactoryInternal(options ?? {}, defaultTransientFieldValues);
