# Equtum Batch Processing System

TypeScript + Prisma を使った AWS バッチ処理システムです。

## 🚀 開発環境のセットアップ

### Dev Container (推奨)

VS Code + Dev Container を使用した開発環境：

1. VS Code でプロジェクトを開く
2. "Reopen in Container"を選択
3. 自動的に開発環境が構築されます

### ローカル開発

Node.js 18 以上が必要です。

```bash
# 依存関係のインストール
npm install

# 環境変数の設定
cp .env.example .env
# .envファイルを編集してください

# Prismaクライアントの生成
npm run prisma:generate

# データベースマイグレーション
npm run prisma:migrate

# 開発サーバーの起動
npm run dev
```

### Docker Compose

```bash
# 開発環境の起動（MySQL含む）
docker-compose -f docker/docker-compose.dev.yml up -d

# アプリケーションのみ起動
docker-compose -f docker/docker-compose.dev.yml up app
```

## 🗄️ データベース

### ローカル MySQL

Docker Compose を使用：

```bash
# MySQLコンテナの起動
docker-compose -f docker/docker-compose.dev.yml up -d mysql

# Prisma Studioでデータベースを確認
npm run prisma:studio
```

### データベース設定

開発環境では以下の接続情報を使用：

```bash
DATABASE_URL="mysql://batch_user:batch_password@localhost:3306/equtum_batch"
```

## 🔧 利用可能なスクリプト

```bash
# ビルド
npm run build

# 本番実行
npm start <job-name>

# 開発実行
npm run dev

# リンター・フォーマッター（Biome）
npm run lint           # リンターのみ実行
npm run lint:fix       # リンター + 自動修正
npm run format         # フォーマッターのみ実行
npm run check          # リンター + フォーマッター実行
npm run check:fix      # リンター + フォーマッター + 自動修正

# Prisma
npm run prisma:generate  # クライアント生成
npm run prisma:migrate   # マイグレーション
npm run prisma:studio    # GUI
```

## 📋 バッチジョブ

### 利用可能なジョブ

- `example-job`: サンプルジョブ
- `data-cleanup`: 古いログのクリーンアップ

### ジョブの実行

```bash
# 開発環境
npm run dev example-job

# 本番環境
npm start example-job
```

### 新しいジョブの追加

1. `src/batch/BatchProcessor.ts`にジョブメソッドを追加
2. `jobs`オブジェクトに登録
3. 必要に応じて Prisma スキーマを更新

## 🏗️ アーキテクチャ

```
src/
├── index.ts              # エントリーポイント
├── config/               # 設定管理
├── batch/                # バッチ処理ロジック
├── utils/                # ユーティリティ
└── types/                # 型定義

infra/                    # Terraformインフラ定義
├── dev/                  # 開発環境
└── prd/                  # 本番環境
```

## 🚀 デプロイ

### AWS ECS Fargate

```bash
# イメージのビルド
docker build -t equtum-batch .

# ECRにプッシュ
aws ecr get-login-password --region ap-northeast-1 | docker login --username AWS --password-stdin <account-id>.dkr.ecr.ap-northeast-1.amazonaws.com
docker tag equtum-batch:latest <account-id>.dkr.ecr.ap-northeast-1.amazonaws.com/equtum-batch:latest
docker push <account-id>.dkr.ecr.ap-northeast-1.amazonaws.com/equtum-batch:latest
```

### スケジュール実行

EventBridge Scheduler による定期実行：

```bash
# ECSタスクを手動実行
aws ecs run-task \
  --cluster equtum-batch \
  --task-definition equtum-batch \
  --overrides '{"containerOverrides": [{"name": "batch", "command": ["npm", "start", "example-job"]}]}'
```

## 📊 モニタリング

- CloudWatch Logs: `/ecs/equtum-batch`
- CloudWatch Metrics: ECS タスクメトリクス
- Prisma Studio: データベース管理

## 🔐 環境変数

| 変数名                       | 説明                             | デフォルト    |
| ---------------------------- | -------------------------------- | ------------- |
| `DATABASE_URL`               | MySQL 接続 URL                   | 必須          |
| `NODE_ENV`                   | 実行環境                         | `development` |
| `LOG_LEVEL`                  | ログレベル                       | `info`        |
| `PRISMA_TRANSACTION_TIMEOUT` | トランザクションタイムアウト(ms) | `10000`       |

## 🤝 開発ルール

### Issue 駆動開発

すべての作業は GitHub Issue ベースで進めます：

1. **作業前に Issue を作成**

   - 機能追加、バグ修正、改善提案などすべて Issue として管理
   - 適切なラベルを付与（`feature`, `bug`, `enhancement`, `documentation` など）
   - 作業内容と受け入れ条件を明確に記載

2. **ブランチ作成規約**

   ```bash
   # 機能追加の場合
   git checkout -b feature/issue-123-add-new-batch-job

   # バグ修正の場合
   git checkout -b fix/issue-456-database-connection-error

   # ドキュメント更新の場合
   git checkout -b docs/issue-789-update-api-documentation
   ```

3. **コミットメッセージ規約**

   ```
   <type>(scope): <description>

   例：
   feat(batch): add data cleanup job processor
   fix(db): resolve connection timeout issue
   docs(readme): update development setup guide
   refactor(logger): improve error handling
   ```

### コード品質管理

1. **Biome による静的解析**

   ```bash
   # コミット前に必ず実行
   npm run check:fix
   ```

2. **型安全性の確保**

   - TypeScript strict モードを使用
   - `any` 型の使用は最小限に
   - Prisma スキーマとの型整合性を維持

3. **テスト（今後実装予定）**
   - ユニットテスト
   - 統合テスト
   - E2E テスト

### DevContainer 開発環境

すべての開発者は DevContainer を使用して環境を統一：

1. **環境起動**

   ```bash
   # VS Code で "Reopen in Container" を選択
   # または
   code --remote containers-reopen-in-container .
   ```

2. **利用可能なコマンド**
   ```bash
   npm run dev              # 開発サーバー起動
   npm run check:fix        # コード品質チェック＋自動修正
   npm run prisma:studio    # データベース管理GUI
   npm run prisma:migrate   # マイグレーション実行
   ```

### プルリクエストフロー

1. **作成前チェック**

   - [ ] `npm run check:fix` でエラーなし
   - [ ] コミットメッセージが規約に準拠
   - [ ] 関連する Issue 番号を記載

2. **レビュー要件**

   - 最低 1 名のレビュー承認が必要
   - CI/CD パイプラインが正常完了
   - 競合(conflict)が解消済み

3. **マージ後**
   - ブランチを削除
   - Issue をクローズ

### ドキュメント管理

- **開発ルール**: README.md（このファイル）
- **技術仕様**: [docs/](./docs/) ディレクトリ
- **API 仕様**: 各エンドポイント近くのコメント
- **インフラ情報**: `infra/` ディレクトリ内の Terraform ファイル

## 📖 詳細ドキュメント

技術的な詳細情報は [docs/](./docs/) ディレクトリを参照してください：

- [アーキテクチャ設計](./docs/architecture.md)
- [バッチ処理仕様](./docs/batch-specifications.md)
- [AWS 構成ガイド](./docs/aws-configuration.md)
- [デプロイメント手順](./docs/deployment.md)

## 📝 ライセンス

MIT License
