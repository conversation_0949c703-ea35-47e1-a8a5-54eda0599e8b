{"$schema": "https://biomejs.dev/schemas/2.1.2/schema.json", "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noNonNullAssertion": "off", "useExportType": "error", "useImportType": "error", "useConst": "error"}, "suspicious": {"noExplicitAny": "warn"}, "correctness": {"noUnusedVariables": "error"}}}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100, "lineEnding": "lf"}, "javascript": {"formatter": {"quoteStyle": "single", "jsxQuoteStyle": "double", "trailingCommas": "all", "semicolons": "always", "arrowParentheses": "asNeeded", "bracketSpacing": true, "bracketSameLine": false, "quoteProperties": "asNeeded"}}, "json": {"formatter": {"enabled": true}}, "files": {"ignoreUnknown": false}}