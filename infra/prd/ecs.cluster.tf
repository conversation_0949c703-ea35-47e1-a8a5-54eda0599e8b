resource "aws_ecs_cluster" "prd-equtum-web" {
  name = "equtum-web"

  configuration {
    execute_command_configuration {
      kms_key_id = null
      logging    = "DEFAULT"
    }
  }

  setting {
    name  = "containerInsights"
    value = "enabled"
  }
}

resource "aws_security_group" "ecs_tasks" {
  name        = "equtum-ecs-tasks"
  description = "Security group for ECS tasks"
  vpc_id      = aws_vpc.prd_equtum_vpc.id

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "equtum-ecs-tasks"
  }
}
