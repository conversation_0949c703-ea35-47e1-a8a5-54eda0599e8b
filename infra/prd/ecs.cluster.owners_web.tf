resource "aws_ecs_service" "owners-web" {
  cluster                 = aws_ecs_cluster.prd-equtum-web.id
  name                    = "owners-web"
  desired_count           = 1
  enable_ecs_managed_tags = true
  scheduling_strategy     = "REPLICA"
  task_definition         = aws_ecs_task_definition.equtum_owners_web.arn

  capacity_provider_strategy {
    base              = 0
    capacity_provider = "FARGATE"
    weight            = 1
  }

  deployment_circuit_breaker {
    enable   = true
    rollback = true
  }

  deployment_controller {
    type = "ECS"
  }

  load_balancer {
    container_name   = "web"
    container_port   = 3000
    target_group_arn = aws_lb_target_group.ecs-prd-equtum-owners-web.arn
  }

  network_configuration {
    assign_public_ip = true
    security_groups = [
      aws_security_group.prd_equtum_owners_web_sg.id,
    ]
    subnets = [
      aws_subnet.prd_equtum_vpc_subnet_1.id,
      aws_subnet.prd_equtum_vpc_subnet_2.id,
      aws_subnet.prd_equtum_vpc_subnet_3.id,
    ]
  }
}

resource "aws_lb_target_group" "ecs-prd-equtum-owners-web" {
  name                              = "ecs-prd-eq-owners-web"
  port                              = 80
  protocol                          = "HTTP"
  ip_address_type                   = "ipv4"
  load_balancing_cross_zone_enabled = "use_load_balancer_configuration"
  target_type                       = "ip"
  vpc_id                            = aws_vpc.prd_equtum_vpc.id

  health_check {
    enabled             = true
    healthy_threshold   = 5
    unhealthy_threshold = 2
    path                = "/login"
  }

  stickiness {
    enabled = false
    type    = "lb_cookie"
  }
}

resource "aws_security_group" "prd_equtum_owners_web_sg" {
  name        = "prd-equtum-owners-web-sg"
  description = "prd Equtum Owners Web Security Group"
  vpc_id      = aws_vpc.prd_equtum_vpc.id

  ingress {
    from_port = 3000
    to_port   = 3000
    protocol  = "tcp"
    security_groups = [
      aws_security_group.prd_equtum_owners_web_alb_sg.id,
    ]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}
