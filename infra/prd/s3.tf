resource "aws_s3_bucket" "equtum-prd" {
  bucket = "equtum-prd"
}

resource "aws_s3_bucket_public_access_block" "equtum-prd" {
  bucket = aws_s3_bucket.equtum-prd.id

  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false
}

resource "aws_s3_bucket_policy" "equtum-prd" {
  bucket     = aws_s3_bucket.equtum-prd.id
  depends_on = [aws_s3_bucket_public_access_block.equtum-prd]

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "PublicReadGetObject"
        Effect    = "Allow"
        Principal = "*"
        Action    = "s3:GetObject"
        Resource  = "${aws_s3_bucket.equtum-prd.arn}/trainers/photos/*"
      },
    ]
  })
}

resource "aws_s3_bucket_cors_configuration" "equtum-prd" {
  bucket = aws_s3_bucket.equtum-prd.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "PUT", "POST"]
    allowed_origins = ["http://localhost:*", "https://equtum.com", "https://owners.equtum.com"]
    expose_headers  = []
    max_age_seconds = 3000
  }
}

resource "aws_s3_bucket" "equtum-assets-prd" {
  bucket = "equtum-assets-prd"
}

resource "aws_s3_bucket_public_access_block" "equtum-assets-prd" {
  bucket = aws_s3_bucket.equtum-assets-prd.id

  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false
}

resource "aws_s3_bucket_policy" "equtum-assets-prd" {
  bucket = aws_s3_bucket.equtum-assets-prd.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "PublicReadGetObject"
        Effect    = "Allow"
        Principal = "*"
        Action    = "s3:GetObject"
        Resource  = "${aws_s3_bucket.equtum-assets-prd.arn}/*"
      },
    ]
  })
}

resource "aws_s3_bucket" "equtum-prd-tf" {
  bucket = "equtum-prd-tf"
}
