resource "aws_ecs_task_definition" "equtum_owners_web" {
  cpu                = "1024"
  execution_role_arn = "arn:aws:iam::211125479381:role/ecsTaskExecutionRole"
  family             = "equtum-owners-web"
  memory             = "3072"
  network_mode       = "awsvpc"
  requires_compatibilities = [
    "FARGATE",
  ]
  track_latest = false

  runtime_platform {
    cpu_architecture        = "X86_64"
    operating_system_family = "LINUX"
  }

  container_definitions = jsonencode(
    [
      {
        name      = "web"
        image     = "${aws_ecr_repository.equtum-owners-web-prd.repository_url}:latest"
        cpu       = 0
        essential = true
        command = [
          "npm",
          "run",
          "start:prd",
        ],
        logConfiguration = {
          logDriver = "awslogs"
          options = {
            awslogs-create-group  = "true"
            awslogs-group         = "/ecs/equtum-web"
            awslogs-region        = "ap-northeast-1"
            awslogs-stream-prefix = "ecs"
          }
        }
        portMappings = [
          {
            appProtocol   = "http"
            containerPort = 3000
            hostPort      = 3000
            name          = "3000"
            protocol      = "tcp"
          },
        ]
      },
    ]
  )
}
