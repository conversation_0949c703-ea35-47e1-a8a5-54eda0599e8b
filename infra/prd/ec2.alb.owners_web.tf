resource "aws_lb" "prd_equtum_owners_web_alb" {
  security_groups = [
    aws_security_group.prd_equtum_owners_web_alb_sg.id,
  ]
  subnets = [
    aws_subnet.prd_equtum_vpc_subnet_1.id,
    aws_subnet.prd_equtum_vpc_subnet_2.id,
    aws_subnet.prd_equtum_vpc_subnet_3.id,
  ]
  ip_address_type = "dualstack"
}

resource "aws_lb_listener" "prd_equtum_owners_web_alb_443" {
  load_balancer_arn = aws_lb.prd_equtum_owners_web_alb.arn
  port              = 443
  protocol          = "HTTPS"
  certificate_arn   = aws_acm_certificate.wildcard_equtum_com.arn

  default_action {
    order = 1
    type  = "forward"

    forward {
      stickiness {
        duration = 3600
        enabled  = false
      }
      target_group {
        arn    = aws_lb_target_group.ecs-prd-equtum-owners-web.arn
        weight = 1
      }
    }
  }
}

resource "aws_lb_listener" "prd_equtum_owners_web_alb_80" {
  load_balancer_arn = aws_lb.prd_equtum_owners_web_alb.arn
  port              = 80
  protocol          = "HTTP"

  default_action {
    order = 1
    type  = "redirect"
    redirect {
      host        = "#{host}"
      port        = 443
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }
}

resource "aws_security_group" "prd_equtum_owners_web_alb_sg" {
  name        = "prd-equtum-owners-web-alb-sg"
  description = "prd Equtum Owners Web ALB Security Group"
  vpc_id      = aws_vpc.prd_equtum_vpc.id

  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}
