resource "aws_vpc" "prd_equtum_vpc" {
  cidr_block                           = "**********/16"
  ipv6_cidr_block_network_border_group = "ap-northeast-1"
  assign_generated_ipv6_cidr_block     = true
  enable_dns_hostnames                 = true
  enable_dns_support                   = true
  enable_network_address_usage_metrics = false
  instance_tenancy                     = "default"
}

resource "aws_subnet" "prd_equtum_vpc_subnet_1" {
  vpc_id                  = aws_vpc.prd_equtum_vpc.id
  availability_zone       = "ap-northeast-1a"
  cidr_block              = "***********/20"
  ipv6_cidr_block         = "2406:da14:16d9:d300::/64"
  map_public_ip_on_launch = true
}

resource "aws_subnet" "prd_equtum_vpc_subnet_2" {
  vpc_id                  = aws_vpc.prd_equtum_vpc.id
  availability_zone       = "ap-northeast-1c"
  cidr_block              = "**********/20"
  ipv6_cidr_block         = "2406:da14:16d9:d301::/64"
  map_public_ip_on_launch = true
}

resource "aws_subnet" "prd_equtum_vpc_subnet_3" {
  vpc_id                  = aws_vpc.prd_equtum_vpc.id
  availability_zone       = "ap-northeast-1d"
  cidr_block              = "***********/20"
  ipv6_cidr_block         = "2406:da14:16d9:d302::/64"
  map_public_ip_on_launch = true
}
