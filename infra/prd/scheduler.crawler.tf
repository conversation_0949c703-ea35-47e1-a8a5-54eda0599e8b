locals {
  crawler_schedules = {
    daily_race_result = {
      name        = "daily-race-result-crawling"
      description = "Crawl race results daily at 22:00 JST"
      schedule    = "cron(0 13 * * ? *)" # UTC 13:00 = JST 22:00
      command     = ["npm", "run", "crawl", "race-results:today"]
    }
    weekly_update_prize = {
      name        = "weekly-update-prize-crawling"
      description = "Update prize information on Monday and Tuesday at 23:00 JST"
      schedule    = "cron(0 14 ? * MON,TUE *)" # UTC 14:00 = JST 23:00
      command     = ["npm", "run", "crawl", "prize"]
    }
    weekly_upcoming_races = {
      name        = "weekly-upcoming-races-crawling"
      description = "Crawl upcoming races every Monday at 20:00 JST"
      schedule    = "cron(0 11 ? * MON *)" # UTC 11:00 = JST 20:00
      command     = ["npm", "run", "crawl", "upcoming-races"]
    }
    weekly_master_horses = {
      name        = "weekly-master-horses-crawling"
      description = "Crawl master horses every Wednesday at 22:00 JST"
      schedule    = "cron(0 13 ? * WED *)" # UTC 13:00 = JST 22:00
      command     = ["npm", "run", "crawl", "master-horses"]
    }
    weekly_master_horses_en = {
      name        = "weekly-master-horses-en-crawling"
      description = "Crawl English master horses every Wednesday at 23:30 JST"
      schedule    = "cron(30 14 ? * WED *)" # UTC 14:30 = JST 23:30
      command     = ["npm", "run", "crawl", "master-horses:en"]
    }
  }
}

resource "aws_scheduler_schedule" "crawler_schedules" {
  for_each = local.crawler_schedules

  name                = each.value.name
  description         = each.value.description
  schedule_expression = each.value.schedule

  flexible_time_window {
    mode = "OFF"
  }

  target {
    arn      = aws_ecs_cluster.prd-equtum-web.arn
    role_arn = aws_iam_role.ecs_events_role.arn

    ecs_parameters {
      task_definition_arn = aws_ecs_task_definition.equtum_crawler.arn
      launch_type         = "FARGATE"
      platform_version    = "LATEST"
      task_count          = 1

      network_configuration {
        subnets = [
          aws_subnet.prd_equtum_vpc_subnet_1.id,
          aws_subnet.prd_equtum_vpc_subnet_2.id,
          aws_subnet.prd_equtum_vpc_subnet_3.id
        ]
        security_groups  = [aws_security_group.ecs_tasks.id]
        assign_public_ip = true
      }
    }

    input = jsonencode({
      containerOverrides = [
        {
          name    = "crawler"
          command = each.value.command
        }
      ]
    })
  }
}
