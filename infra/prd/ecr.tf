resource "aws_ecr_repository" "equtum-cloud-sql-proxy-prd" {
  name = "equtum-cloud-sql-proxy-prd"
  image_scanning_configuration {
    scan_on_push = true
  }
}

resource "aws_ecr_repository" "equtum-management-api-prd" {
  name = "equtum-management-api-prd"
  image_scanning_configuration {
    scan_on_push = true
  }
}

resource "aws_ecr_repository" "equtum-owners-web-prd" {
  name = "equtum-owners-web-prd"
  image_scanning_configuration {
    scan_on_push = true
  }
}

resource "aws_ecr_repository" "equtum-trainers-web-prd" {
  name = "equtum-trainers-web-prd"
  image_scanning_configuration {
    scan_on_push = true
  }
}

resource "aws_ecr_repository" "equtum-crawler-prd" {
  name = "equtum-crawler-prd"
  image_scanning_configuration {
    scan_on_push = true
  }
}

variable "ecr_lifecycle_policy" {
  default = <<POLICY
  {
    "rules": [
      {
        "rulePriority": 1,
        "description": "Remove images older than 10",
        "selection": {
          "tagStatus": "any",
          "countType": "imageCountMoreThan",
          "countNumber": 10
        },
        "action": {
          "type": "expire"
        }
      }
    ]
  }
  POLICY
}

resource "aws_ecr_lifecycle_policy" "equtum-cloud-sql-proxy-prd" {
  repository = aws_ecr_repository.equtum-cloud-sql-proxy-prd.name
  policy     = var.ecr_lifecycle_policy
}

resource "aws_ecr_lifecycle_policy" "equtum-management-api-prd" {
  repository = aws_ecr_repository.equtum-management-api-prd.name
  policy     = var.ecr_lifecycle_policy
}

resource "aws_ecr_lifecycle_policy" "equtum-owners-web-prd" {
  repository = aws_ecr_repository.equtum-owners-web-prd.name
  policy     = var.ecr_lifecycle_policy
}

resource "aws_ecr_lifecycle_policy" "equtum-trainers-web-prd" {
  repository = aws_ecr_repository.equtum-trainers-web-prd.name
  policy     = var.ecr_lifecycle_policy
}

resource "aws_ecr_lifecycle_policy" "equtum-crawler-prd" {
  repository = aws_ecr_repository.equtum-crawler-prd.name
  policy     = var.ecr_lifecycle_policy
}
