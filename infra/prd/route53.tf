resource "aws_route53_zone" "equtum_com" {
  name          = "equtum.com"
  force_destroy = false
}

resource "aws_route53_record" "equtum_com_NS" {
  zone_id = aws_route53_zone.equtum_com.zone_id
  name    = "equtum.com"
  type    = "NS"
  records = [
    "ns-269.awsdns-33.com.",
    "ns-1681.awsdns-18.co.uk.",
    "ns-568.awsdns-07.net.",
    "ns-1399.awsdns-46.org.",
  ]
  ttl = 172800
}

resource "aws_route53_record" "equtum_com_SOA" {
  zone_id = aws_route53_zone.equtum_com.zone_id
  name    = "equtum.com"
  type    = "SOA"
  records = ["ns-269.awsdns-33.com. awsdns-hostmaster.amazon.com. 1 7200 *********** 86400"]
  ttl     = 900
}


resource "aws_route53_record" "equtum_com_A" {
  zone_id = aws_route53_zone.equtum_com.zone_id
  name    = "equtum.com"
  type    = "A"

  alias {
    name                   = aws_lb.prd_equtum_trainers_web_alb.dns_name
    zone_id                = aws_lb.prd_equtum_trainers_web_alb.zone_id
    evaluate_target_health = true
  }
}

resource "aws_route53_record" "owners_equtum_com_A" {
  zone_id = aws_route53_zone.equtum_com.zone_id
  name    = "owners.equtum.com"
  type    = "A"

  alias {
    name                   = aws_lb.prd_equtum_owners_web_alb.dns_name
    zone_id                = aws_lb.prd_equtum_owners_web_alb.zone_id
    evaluate_target_health = true
  }
}

resource "aws_route53_record" "management_api_equtum_com_A" {
  zone_id = aws_route53_zone.equtum_com.zone_id
  name    = "mapi.equtum.com"
  type    = "A"

  alias {
    name                   = aws_lb.prd_equtum_management_api_alb.dns_name
    zone_id                = aws_lb.prd_equtum_management_api_alb.zone_id
    evaluate_target_health = true
  }
}

resource "aws_route53_record" "user_assets" {
  zone_id = aws_route53_zone.equtum_com.zone_id
  name    = "user-assets.equtum.com"
  type    = "A"

  alias {
    name                   = module.user_assets.cloudfront_distribution_domain_name
    zone_id                = module.user_assets.cloudfront_distribution_hosted_zone_id
    evaluate_target_health = false
  }
}

