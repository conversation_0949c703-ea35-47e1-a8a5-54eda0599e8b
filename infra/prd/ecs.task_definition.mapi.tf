locals {
  common_task_config = {
    requires_compatibilities = ["FARGATE"]
    execution_role_arn       = "arn:aws:iam::211125479381:role/ecsTaskExecutionRole"
    task_role_arn            = aws_iam_role.ecs_task_role.arn
    network_mode             = "awsvpc"
    cpu                      = "1024"
    memory                   = "3072"
  }

  api_container = {
    name    = "api"
    image   = "${aws_ecr_repository.equtum-management-api-prd.repository_url}:latest"
    command = ["npm", "run", "start"]
  }

  common_environment = [
    {
      name  = "FIREBASE_PROJECT_ID"
      value = "equtum-production"
    },
    {
      name  = "NODE_ENV"
      value = "production"
    },
    {
      name  = "PORT"
      value = "3000"
    },
    {
      name  = "S3_BUCKET_NAME"
      value = "equtum-prd"
    },
    {
      name  = "FRONTEND_TRAINERS_URL"
      value = "https://equtum.com"
    },
    {
      name  = "FRONTEND_OWNERS_URL"
      value = "https://owners.equtum.com"
    },
    {
      name  = "USER_ASSETS_URL"
      value = "https://${aws_route53_record.user_assets.fqdn}"
    }
  ]

  common_secrets = [
    {
      name      = "FIREBASE_SERVICE_ACCOUNT_JSON"
      valueFrom = aws_secretsmanager_secret.firebase_service_account_json.arn
    },
    {
      name      = "SENDGRID_API_KEY"
      valueFrom = aws_secretsmanager_secret.sendgrid_api_key.arn
    },
    {
      name      = "DATABASE_URL"
      valueFrom = aws_secretsmanager_secret.database_url.arn
    },
    {
      name      = "SLACK_TOKEN"
      valueFrom = aws_secretsmanager_secret.slack_token.arn
    },
    {
      name      = "ANTHROPIC_API_KEY"
      valueFrom = aws_secretsmanager_secret.anthropic_api_key.arn
    },
    {
      name      = "OPENAI_API_KEY"
      valueFrom = aws_secretsmanager_secret.openai_api_key.arn
    },
    {
      name      = "NOTION_TOKEN"
      valueFrom = aws_secretsmanager_secret.notion_token.arn
    }
  ]

  db_container = {
    name  = "db"
    image = "${aws_ecr_repository.equtum-cloud-sql-proxy-prd.repository_url}:latest"
    command = [
      "equtum-production:asia-northeast1:equtum",
      "-a",
      "0.0.0.0",
    ]
    secrets = [
      {
        name      = "CLOUD_SQL_PROXY_SERVICE_ACCOUNT_JSON"
        valueFrom = aws_secretsmanager_secret.cloud_sql_proxy_service_account_json.arn
      },
    ]
    cpu       = 0
    essential = false
  }
}

resource "aws_ecs_task_definition" "equtum_management_api" {
  family = "equtum-management-api"

  requires_compatibilities = local.common_task_config.requires_compatibilities
  execution_role_arn       = local.common_task_config.execution_role_arn
  task_role_arn            = local.common_task_config.task_role_arn
  network_mode             = local.common_task_config.network_mode
  cpu                      = local.common_task_config.cpu
  memory                   = local.common_task_config.memory
  track_latest             = false

  runtime_platform {
    cpu_architecture        = "X86_64"
    operating_system_family = "LINUX"
  }

  container_definitions = jsonencode(
    [
      merge(local.api_container, {
        environment = local.common_environment
        essential   = true
        logConfiguration = {
          logDriver = "awslogs"
          options = {
            awslogs-create-group  = "true"
            awslogs-group         = "/ecs/equtum-management-api"
            awslogs-region        = "ap-northeast-1"
            awslogs-stream-prefix = "ecs"
          }
        }
        portMappings = [
          {
            appProtocol   = "http"
            containerPort = 3000
            hostPort      = 3000
            name          = "3000"
            protocol      = "tcp"
          },
        ]
        secrets = local.common_secrets
      }),
      merge(local.db_container, {
        logConfiguration = {
          logDriver = "awslogs"
          options = {
            awslogs-create-group  = "true"
            awslogs-group         = "/ecs/equtum-management-api-db"
            awslogs-region        = "ap-northeast-1"
            awslogs-stream-prefix = "ecs"
          }
        }
      })
    ]
  )
}

resource "aws_ecs_task_definition" "equtum_management_api_http2" {
  family = "equtum-management-api-http2"

  requires_compatibilities = local.common_task_config.requires_compatibilities
  execution_role_arn       = local.common_task_config.execution_role_arn
  task_role_arn            = local.common_task_config.task_role_arn
  network_mode             = local.common_task_config.network_mode
  cpu                      = local.common_task_config.cpu
  memory                   = local.common_task_config.memory
  track_latest             = false

  runtime_platform {
    cpu_architecture        = "X86_64"
    operating_system_family = "LINUX"
  }

  container_definitions = jsonencode(
    [
      merge(local.api_container, {
        environment = concat(local.common_environment, [
          {
            name  = "HTTP2_ENABLED"
            value = "true"
          }
        ])
        essential = true
        logConfiguration = {
          logDriver = "awslogs"
          options = {
            awslogs-create-group  = "true"
            awslogs-group         = "/ecs/equtum-management-api"
            awslogs-region        = "ap-northeast-1"
            awslogs-stream-prefix = "ecs"
          }
        }
        portMappings = [
          {
            appProtocol   = "http2"
            containerPort = 3000
            hostPort      = 3000
            name          = "3000"
            protocol      = "tcp"
          },
        ]
        secrets = local.common_secrets
      }),
      merge(local.db_container, {
        logConfiguration = {
          logDriver = "awslogs"
          options = {
            awslogs-create-group  = "true"
            awslogs-group         = "/ecs/equtum-management-api-db"
            awslogs-region        = "ap-northeast-1"
            awslogs-stream-prefix = "ecs"
          }
        }
      })
    ]
  )
}

resource "aws_iam_role" "ecs_task_role" {
  name = "ecs_task_role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
}

resource "aws_iam_policy" "s3_read_write_policy" {
  name        = "s3_read_write_policy"
  description = "Policy to allow read and write access to a specific S3 bucket"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:ListBucket"
        ]
        Resource = [
          "${aws_s3_bucket.equtum-prd.arn}",
          "${aws_s3_bucket.equtum-prd.arn}/*"
        ]
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "ecs_task_role_attach" {
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = aws_iam_policy.s3_read_write_policy.arn
}
