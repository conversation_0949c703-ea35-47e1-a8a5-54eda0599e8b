'use strict';

const { S3Client, GetObjectCommand, PutObjectCommand } = require("@aws-sdk/client-s3");
const Sharp = require('sharp');

// S3クライアントの初期化
const S3 = new S3Client({ region: "ap-northeast-1" });

const BUCKET = 'equtum-prd';

// ストリームをバッファに変換するヘルパー関数
const streamToBuffer = async (stream) => {
  return new Promise((resolve, reject) => {
    const chunks = [];
    stream.on('data', (chunk) => chunks.push(chunk));
    stream.on('error', reject);
    stream.on('end', () => resolve(Buffer.concat(chunks)));
  });
};

exports.handler = async (event, context, callback) => {
  const response = event.Records[0].cf.response;

  console.log(`Response status code: ${response.status}`);

  if (
    response.status === 403 ||
    response.status === 404 ||
    response.status === "403" ||
    response.status === "404"
  ) {
    const request = event.Records[0].cf.request;

    const requestUri = request.uri;
    const subRequestUri = requestUri.substring(1);
    console.log(`requestUri: ${requestUri} -> subRequestUri: ${subRequestUri}`);

    const decodedUriObj = decodeOriginResponseUri(subRequestUri);
    if (!decodedUriObj) {
      console.log("Failed to decode URI");
      callback(null, response);
      return;
    }

    const {
      prefix,
      originalKey,
      imageName,
      extension,
      width,
      height,
      quality,
      targetExtension,
      fit,
    } = decodedUriObj;
    console.log(
      `w=${width}, h=${height}, originalKey: ${originalKey}, requiredFormat: ${targetExtension}, imageName: ${imageName}`
    );

    try {
      // S3からオブジェクトを取得
      const getObjectParams = {
        Bucket: BUCKET,
        Key: originalKey,
      };
      const getObjectCommand = new GetObjectCommand(getObjectParams);
      const data = await S3.send(getObjectCommand);

      // ストリームをバッファに変換
      const inputBuffer = await streamToBuffer(data.Body);

      // 画像をリサイズ
      let image = Sharp(inputBuffer).resize({ width: width, height: height, fit: fit });

      // 目的のフォーマットに変換
      if (targetExtension === "webp") {
        image = image.webp({ quality: quality });
      } else if (targetExtension === "png") {
        image = image.png();
      } else if (targetExtension === "jpg" || targetExtension === "jpeg") {
        image = image.jpeg({ quality: quality });
      } else {
        // 対応していないフォーマットの場合はリクエストをそのまま返す
        callback(null, request);
        return;
      }

      const outputBuffer = await image.toBuffer();

      // リサイズ後の画像をS3に保存
      const putObjectParams = {
        Body: outputBuffer,
        Bucket: BUCKET,
        ContentType: 'image/' + targetExtension,
        CacheControl: 'max-age=31536000',
        Key: subRequestUri,
        StorageClass: 'STANDARD',
      };
      const putObjectCommand = new PutObjectCommand(putObjectParams);
      await S3.send(putObjectCommand);
      console.log(`Successfully put object ${subRequestUri} to bucket ${BUCKET}`);

      // CloudFrontのレスポンスを更新
      response.status = 200;
      response.body = outputBuffer.toString('base64');
      response.bodyEncoding = 'base64';
      response.headers['content-type'] = [{ key: 'Content-Type', value: 'image/' + targetExtension }];
      callback(null, response);
    } catch (err) {
      console.log("Exception occurred:", err);
      // エラーが発生した場合は元のレスポンスを返す
      callback(null, response);
    }
  } else {
    // ステータスコードが403または404以外の場合はそのままレスポンスを返す
    callback(null, response);
  }
};

// URIをデコードする関数
const decodeOriginResponseUri = (uri) => {
  try {
    // プレフィックスありの場合
    const match = uri.match(/(.*)\/w=(\d+)&h=(\d+)&quality=(\d+)&ext=(.*)&fit=(.*)\/(.*)\.(.*)/);
    if (match) {
      const prefix = match[1];
      const width = parseInt(match[2], 10);
      const height = parseInt(match[3], 10);
      const quality = parseInt(match[4], 10);
      const targetExtension = match[5];
      const fit = match[6];
      const imageName = match[7];
      const extension = match[8];
      console.log(`prefix: ${prefix}, imageName: ${imageName}, extension: ${extension}`);

      return {
        prefix,
        imageName,
        extension,
        width,
        height,
        quality,
        targetExtension,
        fit,
        originalKey: `${prefix}/${imageName}.${extension}`,
      };
    } else {
      throw new Error("No prefix present");
    }
  } catch (err) {
    console.log("No prefix present.. Attempting without prefix");

    // プレフィックスなしの場合
    const match = uri.match(/w=(\d+)&h=(\d+)&quality=(\d+)&ext=(.*)&fit=(.*)\/(.*)\.(.*)/);
    if (match) {
      const width = parseInt(match[1], 10);
      const height = parseInt(match[2], 10);
      const quality = parseInt(match[3], 10);
      const targetExtension = match[4];
      const fit = match[5];
      const imageName = match[6];
      const extension = match[7];

      return {
        prefix: "",
        imageName,
        extension,
        width,
        height,
        quality,
        targetExtension,
        fit,
        originalKey: `${imageName}.${extension}`,
      };
    } else {
      console.log("URI does not match expected format");
      return null;
    }
  }
};
