provider "aws" {
  region = "us-east-1"
  alias = "us-east-1"
}

data "archive_file" "lambda_edge_image_url_customizer" {
  type        = "zip"
  source_dir = "${path.module}/function/lambda_edge_image_url_customizer"
  output_path = "${path.module}/function/output_zip/lambda_edge_image_url_customizer.zip"
}

resource "aws_lambda_function" "lambda_edge_image_url_customizer" {
  provider = aws.us-east-1

  function_name = "lambdaEdgeImageUrlCustomizer"
  role          = aws_iam_role.lambda_edge_role.arn
  handler       = "lambda_edge_image_url_customizer.handler"
  runtime       = "nodejs20.x"

  publish = true

  filename         = data.archive_file.lambda_edge_image_url_customizer.output_path
  source_code_hash = data.archive_file.lambda_edge_image_url_customizer.output_base64sha256
}

# lambda関数をデプロイするためにzip化する
data "archive_file" "lambda_edge_image_resize" {
  type        = "zip"
  source_dir = "${path.module}/function/lambda_edge_image_resize"
  output_path = "${path.module}/function/output_zip/lambda_edge_image_resize.zip"
}

resource "aws_lambda_function" "lambda_edge_image_resize" {
  provider = aws.us-east-1

  function_name = "lambdaEdgeImageResize"
  role          = aws_iam_role.lambda_edge_role.arn
  handler       = "lambda_edge_image_resize.handler"
  runtime       = "nodejs20.x"

  publish = true

  timeout = 10

  filename         = data.archive_file.lambda_edge_image_resize.output_path
  source_code_hash = data.archive_file.lambda_edge_image_resize.output_base64sha256
}

output "lambda_edge_image_url_customizer_qualified_arn" {
  value = aws_lambda_function.lambda_edge_image_url_customizer.qualified_arn
}

output "lambda_edge_image_resize_qualified_arn" {
  value = aws_lambda_function.lambda_edge_image_resize.qualified_arn
}

