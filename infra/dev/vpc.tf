resource "aws_vpc" "dev_equtum_vpc" {
  cidr_block                           = "**********/16"
  ipv6_cidr_block_network_border_group = "ap-northeast-1"
  assign_generated_ipv6_cidr_block     = true
  enable_dns_hostnames                 = true
  enable_dns_support                   = true
  enable_network_address_usage_metrics = false
  instance_tenancy                     = "default"
}

resource "aws_subnet" "dev_equtum_vpc_subnet_1" {
  vpc_id                  = aws_vpc.dev_equtum_vpc.id
  availability_zone       = "ap-northeast-1a"
  cidr_block              = "***********/20"
  ipv6_cidr_block         = "2406:da14:d48:3000::/64"
  map_public_ip_on_launch = true
}

resource "aws_subnet" "dev_equtum_vpc_subnet_2" {
  vpc_id                  = aws_vpc.dev_equtum_vpc.id
  availability_zone       = "ap-northeast-1c"
  cidr_block              = "**********/20"
  ipv6_cidr_block         = "2406:da14:d48:3001::/64"
  map_public_ip_on_launch = true
}

resource "aws_subnet" "dev_equtum_vpc_subnet_3" {
  vpc_id                  = aws_vpc.dev_equtum_vpc.id
  availability_zone       = "ap-northeast-1d"
  cidr_block              = "***********/20"
  ipv6_cidr_block         = "2406:da14:d48:3002::/64"
  map_public_ip_on_launch = true
}

data "aws_internet_gateway" "dev_equtum_igw" {
  filter {
    name   = "attachment.vpc-id"
    values = [aws_vpc.dev_equtum_vpc.id]
  }
}

resource "aws_route_table" "dev_equtum_public_rt" {
  vpc_id = aws_vpc.dev_equtum_vpc.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = data.aws_internet_gateway.dev_equtum_igw.id
  }

  route {
    ipv6_cidr_block = "::/0"
    gateway_id      = data.aws_internet_gateway.dev_equtum_igw.id
  }

  tags = {
    Name = "dev-equtum-public-rt"
  }
}

resource "aws_route_table_association" "dev_equtum_rta_1" {
  subnet_id      = aws_subnet.dev_equtum_vpc_subnet_1.id
  route_table_id = aws_route_table.dev_equtum_public_rt.id
}

resource "aws_route_table_association" "dev_equtum_rta_2" {
  subnet_id      = aws_subnet.dev_equtum_vpc_subnet_2.id
  route_table_id = aws_route_table.dev_equtum_public_rt.id
}

resource "aws_route_table_association" "dev_equtum_rta_3" {
  subnet_id      = aws_subnet.dev_equtum_vpc_subnet_3.id
  route_table_id = aws_route_table.dev_equtum_public_rt.id
}
