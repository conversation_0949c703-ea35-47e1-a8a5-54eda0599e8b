resource "aws_iam_role" "deploy_github_actions" {
  name = "deploy_github_actions"
  assume_role_policy = jsonencode(
    {
      Statement = [
        {
          Action = "sts:AssumeRoleWithWebIdentity"
          Condition = {
            StringLike = {
              "token.actions.githubusercontent.com:sub" = [
                "repo:abelorg/equtum-management-api:*",
                "repo:abelorg/equtum-web:*",
                "repo:abelorg/equtum-terraform-aws:*",
                "repo:abelorg/equtum-crawler:*",
              ]
            }
          }
          Effect = "Allow"
          Principal = {
            Federated = aws_iam_openid_connect_provider.github.arn
          }
        },
      ]
      Version = "2012-10-17"
    }
  )
  managed_policy_arns = [
    "arn:aws:iam::aws:policy/AdministratorAccess",
  ]
}

resource "aws_iam_openid_connect_provider" "github" {
  url             = "https://token.actions.githubusercontent.com"
  client_id_list  = ["sts.amazonaws.com"]
  thumbprint_list = ["1b511abead59c6ce207077c0bf0e0043b1382612"]
}

resource "aws_iam_role_policy" "ecs_task_execution_role_policy" {
  name = "ecs_task_execution_role_policy"
  role = "ecsTaskExecutionRole"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "*"
      }
    ]
  })
}

variable "bedrock_developers" {
  description = "Bedrockを使用する開発者のリスト"
  type        = list(string)
  default     = ["ryosei_ogawa", "tomoki_tanezaki"]
}

resource "aws_iam_user" "bedrock_developers" {
  for_each = toset(var.bedrock_developers)
  name     = "bedrock_${each.value}"
}

resource "aws_iam_user_policy_attachment" "bedrock_developers_policy" {
  for_each   = toset(var.bedrock_developers)
  user       = aws_iam_user.bedrock_developers[each.key].name
  policy_arn = "arn:aws:iam::aws:policy/AmazonBedrockFullAccess"
}
