resource "aws_route53_zone" "dev_equtum_com" {
  name          = "dev.equtum.com"
  force_destroy = false
}

resource "aws_route53_record" "dev_equtum_com_NS" {
  zone_id = aws_route53_zone.dev_equtum_com.zone_id
  name    = "dev.equtum.com"
  type    = "NS"
  records = [
    "ns-82.awsdns-10.com.",
    "ns-1415.awsdns-48.org.",
    "ns-682.awsdns-21.net.",
    "ns-1624.awsdns-11.co.uk.",
  ]
  ttl = 172800
}

resource "aws_route53_record" "dev_equtum_com_A" {
  zone_id = aws_route53_zone.dev_equtum_com.zone_id
  name    = "dev.equtum.com"
  type    = "A"

  alias {
    name                   = aws_lb.dev_equtum_trainers_web_alb.dns_name
    zone_id                = aws_lb.dev_equtum_trainers_web_alb.zone_id
    evaluate_target_health = true
  }
}

resource "aws_route53_record" "owners_dev_equtum_com_A" {
  zone_id = aws_route53_zone.dev_equtum_com.zone_id
  name    = "owners.dev.equtum.com"
  type    = "A"

  alias {
    name                   = aws_lb.dev_equtum_owners_web_alb.dns_name
    zone_id                = aws_lb.dev_equtum_owners_web_alb.zone_id
    evaluate_target_health = true
  }
}

resource "aws_route53_record" "management_api_dev_equtum_com_A" {
  zone_id = aws_route53_zone.dev_equtum_com.zone_id
  name    = "mapi.dev.equtum.com"
  type    = "A"

  alias {
    name                   = aws_lb.dev_equtum_management_api_alb.dns_name
    zone_id                = aws_lb.dev_equtum_management_api_alb.zone_id
    evaluate_target_health = true
  }
}

resource "aws_route53_record" "user_assets" {
  zone_id = aws_route53_zone.dev_equtum_com.zone_id
  name    = "user-assets.dev.equtum.com"
  type    = "A"

  alias {
    name                   = module.user_assets.cloudfront_distribution_domain_name
    zone_id                = module.user_assets.cloudfront_distribution_hosted_zone_id
    evaluate_target_health = false
  }
}

resource "aws_acm_certificate" "equtum_dev" {
  provider                  = aws
  domain_name               = "equtum.dev"
  subject_alternative_names = ["*.equtum.dev"]
  validation_method         = "DNS"

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_acm_certificate" "equtum_dev_cloudfront" {
  provider                  = aws.us-east-1
  domain_name               = "equtum.dev"
  subject_alternative_names = ["*.equtum.dev"]
  validation_method         = "DNS"

  lifecycle {
    create_before_destroy = true
  }
}
