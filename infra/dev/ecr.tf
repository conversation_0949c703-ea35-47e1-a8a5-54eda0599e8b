resource "aws_ecr_repository" "equtum-cloud-sql-proxy-dev" {
  name = "equtum-cloud-sql-proxy-dev"
  image_scanning_configuration {
    scan_on_push = true
  }
}

resource "aws_ecr_repository" "equtum-management-api-dev" {
  name = "equtum-management-api-dev"
  image_scanning_configuration {
    scan_on_push = true
  }
}

resource "aws_ecr_repository" "equtum-owners-web-dev" {
  name = "equtum-owners-web-dev"
  image_scanning_configuration {
    scan_on_push = true
  }
}

resource "aws_ecr_repository" "equtum-trainers-web-dev" {
  name = "equtum-trainers-web-dev"
  image_scanning_configuration {
    scan_on_push = true
  }
}

resource "aws_ecr_repository" "equtum-web-dev" {
  name = "equtum-web-dev"
  image_scanning_configuration {
    scan_on_push = true
  }
}

resource "aws_ecr_repository" "equtum-crawler-dev" {
  name = "equtum-crawler-dev"
  image_scanning_configuration {
    scan_on_push = true
  }
}

variable "ecr_lifecycle_policy" {
  default = <<POLICY
  {
    "rules": [
      {
        "rulePriority": 1,
        "description": "Remove images older than 10",
        "selection": {
          "tagStatus": "any",
          "countType": "imageCountMoreThan",
          "countNumber": 10
        },
        "action": {
          "type": "expire"
        }
      }
    ]
  }
  POLICY
}

resource "aws_ecr_lifecycle_policy" "equtum-cloud-sql-proxy-dev" {
  repository = aws_ecr_repository.equtum-cloud-sql-proxy-dev.name
  policy     = var.ecr_lifecycle_policy
}

resource "aws_ecr_lifecycle_policy" "equtum-management-api-dev" {
  repository = aws_ecr_repository.equtum-management-api-dev.name
  policy     = var.ecr_lifecycle_policy
}

resource "aws_ecr_lifecycle_policy" "equtum-owners-web-dev" {
  repository = aws_ecr_repository.equtum-owners-web-dev.name
  policy     = var.ecr_lifecycle_policy
}

resource "aws_ecr_lifecycle_policy" "equtum-trainers-web-dev" {
  repository = aws_ecr_repository.equtum-trainers-web-dev.name
  policy     = var.ecr_lifecycle_policy
}

resource "aws_ecr_lifecycle_policy" "equtum-web-dev" {
  repository = aws_ecr_repository.equtum-web-dev.name
  policy     = var.ecr_lifecycle_policy
}

resource "aws_ecr_lifecycle_policy" "equtum-crawler-dev" {
  repository = aws_ecr_repository.equtum-crawler-dev.name
  policy     = var.ecr_lifecycle_policy
}
