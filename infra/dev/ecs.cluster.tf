resource "aws_ecs_cluster" "dev-equtum-management-api" {
  name = "dev-equtum-management-api"

  configuration {
    execute_command_configuration {
      kms_key_id = null
      logging    = "DEFAULT"
    }
  }

  setting {
    name  = "containerInsights"
    value = "enabled"
  }

  service_connect_defaults {
    namespace = "arn:aws:servicediscovery:ap-northeast-1:471112647741:namespace/ns-zvrtvattp34jmwbe"
  }
}

resource "aws_security_group" "ecs_tasks" {
  name        = "equtum-ecs-tasks"
  description = "Security group for ECS tasks"
  vpc_id      = aws_vpc.dev_equtum_vpc.id

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "equtum-ecs-tasks"
  }
}
