resource "aws_secretsmanager_secret" "firebase_service_account_json" {
  name                    = "FIREBASE_SERVICE_ACCOUNT_JSON"
  recovery_window_in_days = 0
}

resource "aws_secretsmanager_secret" "sendgrid_api_key" {
  name                    = "SENDGRID_API_KEY"
  recovery_window_in_days = 0
}

resource "aws_secretsmanager_secret" "database_url" {
  name                    = "DATABASE_URL"
  recovery_window_in_days = 0
}

resource "aws_secretsmanager_secret" "slack_token" {
  name                    = "SLACK_TOKEN"
  recovery_window_in_days = 0
}

resource "aws_secretsmanager_secret" "cloud_sql_proxy_service_account_json" {
  name                    = "CLOUD_SQL_PROXY_SERVICE_ACCOUNT_JSON"
  recovery_window_in_days = 0
}

resource "aws_secretsmanager_secret" "anthropic_api_key" {
  name                    = "ANTHROPIC_API_KEY"
  recovery_window_in_days = 0
}

resource "aws_secretsmanager_secret" "openai_api_key" {
  name                    = "OPENAI_API_KEY"
  recovery_window_in_days = 0
}

resource "aws_secretsmanager_secret" "notion_token" {
  name                    = "NOTION_TOKEN"
  recovery_window_in_days = 0
}
