resource "aws_s3_bucket" "equtum-dev" {
  bucket = "equtum-dev"
}

resource "aws_s3_bucket_public_access_block" "equtum-dev" {
  bucket = aws_s3_bucket.equtum-dev.id

  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false
}

resource "aws_s3_bucket_policy" "equtum-dev" {
  bucket     = aws_s3_bucket.equtum-dev.id
  depends_on = [aws_s3_bucket_public_access_block.equtum-dev]

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "PublicReadGetObject"
        Effect    = "Allow"
        Principal = "*"
        Action    = "s3:GetObject"
        Resource  = "${aws_s3_bucket.equtum-dev.arn}/trainers/photos/*"
      },
    ]
  })
}

resource "aws_s3_bucket_cors_configuration" "equtum-dev" {
  bucket = aws_s3_bucket.equtum-dev.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "PUT", "POST"]
    allowed_origins = [
      "http://localhost:*",
      "https://dev.equtum.com",
      "https://owners.dev.equtum.com",
      "https://dbjz2xytw7cmq.cloudfront.net",
      "https://equtum.dev",
      "https://owners.equtum.dev",
    ]
    expose_headers  = []
    max_age_seconds = 3000
  }
}

resource "aws_s3_bucket" "equtum-assets-dev" {
  bucket = "equtum-assets-dev"
}

resource "aws_s3_bucket_public_access_block" "equtum-assets-dev" {
  bucket = aws_s3_bucket.equtum-assets-dev.id

  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false
}

resource "aws_s3_bucket_policy" "equtum-assets-dev" {
  bucket = aws_s3_bucket.equtum-assets-dev.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "PublicReadGetObject"
        Effect    = "Allow"
        Principal = "*"
        Action    = "s3:GetObject"
        Resource  = "${aws_s3_bucket.equtum-assets-dev.arn}/*"
      },
    ]
  })
}

resource "aws_s3_bucket" "equtum-dev-tf" {
  bucket = "equtum-dev-tf"
}
