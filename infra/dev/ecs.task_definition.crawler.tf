locals {
  crawler_task_config = {
    requires_compatibilities = ["FARGATE"]
    execution_role_arn       = "arn:aws:iam::471112647741:role/ecsTaskExecutionRole"
    task_role_arn            = aws_iam_role.ecs_task_role.arn
    network_mode             = "awsvpc"
    cpu                      = "1024"
    memory                   = "4096"
  }

  crawler_container = {
    name    = "crawler"
    image   = "${aws_ecr_repository.equtum-crawler-dev.repository_url}:latest"
    command = ["npm", "run", "start"]
    environment = [
      {
        name  = "PRISMA_TRANSACTION_TIMEOUT"
        value = "10000"
      }
    ]
    secrets = [
      {
        name      = "DATABASE_URL"
        valueFrom = aws_secretsmanager_secret.database_url.arn
      }
    ]
    essential = true
    portMappings = [
      {
        containerPort = 8080
        protocol      = "tcp"
      }
    ]
    logConfiguration = {
      logDriver = "awslogs"
      options = {
        awslogs-create-group  = "true"
        awslogs-group         = "/ecs/equtum-crawler"
        awslogs-region        = "ap-northeast-1"
        awslogs-stream-prefix = "ecs"
      }
    }
  }

  crawler_db_container = {
    name  = "db"
    image = "${aws_ecr_repository.equtum-cloud-sql-proxy-dev.repository_url}:latest"
    command = [
      "equtum-development:asia-northeast1:equtum",
      "-a",
      "0.0.0.0",
    ]
    secrets = [
      {
        name      = "CLOUD_SQL_PROXY_SERVICE_ACCOUNT_JSON"
        valueFrom = aws_secretsmanager_secret.cloud_sql_proxy_service_account_json.arn
      },
    ]
    cpu       = 0
    essential = false
    logConfiguration = {
      logDriver = "awslogs"
      options = {
        awslogs-create-group  = "true"
        awslogs-group         = "/ecs/equtum-crawler-db"
        awslogs-region        = "ap-northeast-1"
        awslogs-stream-prefix = "ecs"
      }
    }
  }
}

resource "aws_ecs_task_definition" "equtum_crawler" {
  family = "equtum-crawler"

  requires_compatibilities = local.crawler_task_config.requires_compatibilities
  execution_role_arn       = local.crawler_task_config.execution_role_arn
  task_role_arn            = local.crawler_task_config.task_role_arn
  network_mode             = local.crawler_task_config.network_mode
  cpu                      = local.crawler_task_config.cpu
  memory                   = local.crawler_task_config.memory

  container_definitions = jsonencode([
    local.crawler_container,
    local.crawler_db_container
  ])
}
