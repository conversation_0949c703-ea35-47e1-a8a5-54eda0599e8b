terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }

  required_version = ">= 1.2.0"

  backend "s3" {
    bucket = "equtum-dev-tf"
    key    = "terraform.tfstate"
    region = "ap-northeast-1"
  }
}

provider "aws" {
  region = "ap-northeast-1"
}

provider "aws" {
  alias  = "us_east_1"
  region = "us-east-1"
}

module "sendgrid_logs" {
  source = "./sendgrid_logs"

  sendgrid_logs_athena_results_bucket_name = "equtum-sendgrid-logs-athena-results-dev"
  s3_bucket_name                           = "equtum-sendgrid-logs-dev"
}

module "user_assets" {
  source = "./user_assets"

  route_53_zone_id = aws_route53_zone.dev_equtum_com.zone_id
}
