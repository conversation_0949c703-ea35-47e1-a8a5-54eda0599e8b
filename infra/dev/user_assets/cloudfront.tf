provider "aws" {
  region = "ap-northeast-1"
  alias = "ap-northeast-1"
}

data "aws_s3_bucket" "equtum-dev" {
  provider = aws.ap-northeast-1
  bucket = "equtum-dev"
}

resource "aws_cloudfront_origin_access_control" "equtum-dev" {
  name                              = "equtum-dev-oac"
  description                       = "OAC for equtum-dev bucket"
  origin_access_control_origin_type = "s3"
  signing_behavior                  = "always"
  signing_protocol                  = "sigv4"
}

resource "aws_cloudfront_distribution" "equtum-dev-user-assets" {
  aliases = [ "user-assets.dev.equtum.com" ]

  depends_on = [aws_lambda_function.lambda_edge_image_resize, aws_lambda_function.lambda_edge_image_url_customizer]

  origin {
    domain_name              = data.aws_s3_bucket.equtum-dev.bucket_regional_domain_name
    origin_id                = data.aws_s3_bucket.equtum-dev.id
    origin_access_control_id = aws_cloudfront_origin_access_control.equtum-dev.id
  }
  enabled         = true
  is_ipv6_enabled = true

  default_cache_behavior {
    target_origin_id       = data.aws_s3_bucket.equtum-dev.id
    viewer_protocol_policy = "allow-all"
    allowed_methods        = ["GET", "HEAD"]
    cached_methods         = ["GET", "HEAD"]

    forwarded_values {
      query_string = false

      cookies {
        forward = "none"
      }
    }

    min_ttl     = 0
    default_ttl = 86400  // 1日
    max_ttl     = 259200 // 3日

    lambda_function_association {
      event_type = "viewer-request"
      lambda_arn = aws_lambda_function.lambda_edge_image_url_customizer.qualified_arn
    }

    lambda_function_association {
      event_type = "origin-response"
      lambda_arn = aws_lambda_function.lambda_edge_image_resize.qualified_arn
    }
  }

  restrictions {
    geo_restriction {
      restriction_type = "whitelist"
      locations        = ["JP"]
    }
  }

  viewer_certificate {
    acm_certificate_arn = aws_acm_certificate.user_assets_cert.arn
    ssl_support_method = "sni-only"
    minimum_protocol_version = "TLSv1.2_2021"
  }
}
