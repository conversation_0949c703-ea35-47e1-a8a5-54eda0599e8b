provider "aws" {
  alias  = "us_east_1"
  region = "us-east-1"
}

resource "aws_acm_certificate" "user_assets_cert" {
  domain_name = "user-assets.dev.equtum.com"
  subject_alternative_names = ["dev.equtum.com"]
  validation_method = "DNS"
  provider = aws.us_east_1

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_acm_certificate_validation" "user_assets_cert" {
  certificate_arn         = aws_acm_certificate.user_assets_cert.arn
  validation_record_fqdns = [for record in aws_route53_record.cdn_cert_validation : record.fqdn]
  provider                = aws.us_east_1
}

resource "aws_route53_record" "cdn_cert_validation" {
  for_each = {
    for dvo in aws_acm_certificate.user_assets_cert.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  }

  allow_overwrite = true
  name    = each.value.name
  type    = each.value.type
  zone_id = "${var.route_53_zone_id}"
  records = [each.value.record]
  ttl     = 300
}
