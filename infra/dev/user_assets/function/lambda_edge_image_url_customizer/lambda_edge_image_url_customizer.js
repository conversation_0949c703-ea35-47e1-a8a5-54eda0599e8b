'use strict';

exports.handler = (event, context, callback) => {
  const request = event.Records[0].cf.request;

  let fwdUri = request.uri;
  console.log(`requestUri: ${fwdUri}`)

  const decodedUriObj = decodeViewerRequestUri(fwdUri)
  const [decodeSuccessData, errorResponse] = decodeQuerystring(request.querystring, decodedUriObj)
  if (errorResponse) {
    callback(null, errorResponse);
    return;
  }
  if (!decodeSuccessData && !errorResponse) {
    callback(null, request);
    return;
  }

  const convertedUri = `${decodedUriObj.prefix}/${decodeSuccessData.querystring}/${decodedUriObj.imageName}.${decodedUriObj.extension}`
  request.uri = convertedUri;
  request.querystring = decodeSuccessData.querystring
  console.log(`converted: ${convertedUri}`)
  callback(null, request);
};

const querystring = require('querystring');

const MAX_WIDTH = 2400;
const MAX_HEIGHT = 2400;
const SKIP_EXTENSION = ["svg"];
const limitations = {
  allowed: {
    quality: [50, 100],
    extension: ["jpg", "jpeg", "png", "JPG", "webp"],
    fit: ["outside", "inside"]
  },
  default: {
    dimension: {
      w: 600,
      h: 600,
    },
    fit: "inside",
    quality: 100,
    ext: "webp",
  },
};


const decodeQuerystring = (requestQuerystring, decodedUriObj) => {
  const params = querystring.parse(requestQuerystring);
  if (!params.w && !params.h && !params.quality && !params.ext && !params.fit) {
    return [null, null];
  }

  const paramFit = params.fit ? params.fit : "inside"
  const paramWidth = (isNaN(parseInt(params.w)) || parseInt(params.w) <= 0) ? limitations.default.dimension.w : Math.min(parseInt(params.w), MAX_WIDTH);
  const paramHeight = (isNaN(parseInt(params.h)) || parseInt(params.h) <= 0) ? limitations.default.dimension.h : Math.min(parseInt(params.h), MAX_HEIGHT);
  const paramQuality = (isNaN(parseInt(params.quality)) || parseInt(params.quality) <= 0 || parseInt(params.quality) > 100) ? limitations.default.quality : Math.min(parseInt(params.quality), limitations.default.quality);
  const paramExtension = params.ext ? params.ext : limitations.default.ext;

  if (SKIP_EXTENSION.includes(decodedUriObj.extension)) {
    return [null, null];
  }
  if (!limitations.allowed.extension.includes(decodedUriObj.extension)) {
    return [null, null];
  }

  if (!limitations.allowed.quality.includes(paramQuality)) {
    const errorResponse = {
      status: "500",
      headers: {
        "content-type": [{key: 'Content-Type', value: 'text/plain'}]
      },
      body: `${paramQuality} is not allowed`
    };
    return [null, errorResponse];
  }

  if (!limitations.allowed.fit.includes(paramFit)) {
    const errorResponse = {
      status: "500",
      headers: {
        "content-type": [{key: 'Content-Type', value: 'text/plain'}]
      },
      body: `${paramFit} is not allowed`
    };
    return [null, errorResponse];
  }

  const updatedQuerystring = `w=${paramWidth}&h=${paramHeight}&quality=${paramQuality}&ext=${paramExtension}&fit=${paramFit}`
  const decodeSuccessData = {
    width: paramWidth,
    height: paramHeight,
    quality: paramQuality,
    targetExtension: paramExtension,
    fit: paramFit,
    querystring: updatedQuerystring
  }
  console.log(`width: ${paramWidth}`)
  console.log(`height: ${paramHeight}`)
  console.log(`extension: ${paramExtension}`)
  console.log(`quality: ${paramQuality}`)
  console.log(`fit: ${paramFit}`)
  console.log(`querystring: ${requestQuerystring} -> ${updatedQuerystring}`)

  // 成功した場合
  return [decodeSuccessData, null]
}

const decodeViewerRequestUri = (uri) => {
  const match = uri.match(/(.*)\/(.*)\.(.*)/);

  const prefix = match[1];
  const imageName = match[2];
  const extension = match[3];
  console.log(`prefix: ${prefix}, imageName: ${imageName}, extension: ${extension}`)

  return {
    prefix,
    imageName,
    extension,
  }
}
