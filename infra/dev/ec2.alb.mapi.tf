resource "aws_lb" "dev_equtum_management_api_alb" {
  security_groups = [
    aws_security_group.dev_equtum_management_api_alb_sg.id,
  ]
  subnets = [
    aws_subnet.dev_equtum_vpc_subnet_1.id,
    aws_subnet.dev_equtum_vpc_subnet_2.id,
    aws_subnet.dev_equtum_vpc_subnet_3.id,
  ]
  ip_address_type = "dualstack"
}

resource "aws_lb_listener" "dev_equtum_management_api_alb_443" {
  load_balancer_arn = aws_lb.dev_equtum_management_api_alb.arn
  port              = 443
  protocol          = "HTTPS"
  certificate_arn   = aws_acm_certificate.wildcard_dev_equtum_com.arn

  default_action {
    order = 1
    type  = "forward"

    forward {
      stickiness {
        duration = 3600
        enabled  = false
      }
      target_group {
        arn    = aws_lb_target_group.ecs-dev-equtum-management-api.arn
        weight = 1
      }
    }
  }
}

resource "aws_lb_listener_certificate" "management_api_equtum_dev" {
  listener_arn    = aws_lb_listener.dev_equtum_management_api_alb_443.arn
  certificate_arn = aws_acm_certificate.equtum_dev.arn
}

resource "aws_lb_listener" "dev_equtum_management_api_alb_80" {
  load_balancer_arn = aws_lb.dev_equtum_management_api_alb.arn
  port              = 80
  protocol          = "HTTP"

  default_action {
    order = 1
    type  = "redirect"
    redirect {
      host        = "#{host}"
      port        = 443
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }
}

resource "aws_lb_listener" "dev_equtum_management_api_alb_8443" {
  load_balancer_arn = aws_lb.dev_equtum_management_api_alb.arn
  port              = 8443
  protocol          = "HTTPS"
  certificate_arn   = aws_acm_certificate.wildcard_dev_equtum_com.arn

  default_action {
    order = 1
    type  = "forward"

    forward {
      stickiness {
        duration = 3600
        enabled  = false
      }
      target_group {
        arn    = aws_lb_target_group.ecs-dev-equtum-management-api-http2.arn
        weight = 1
      }
    }
  }
}

resource "aws_lb_listener_certificate" "management_api_equtum_dev_8443" {
  listener_arn    = aws_lb_listener.dev_equtum_management_api_alb_8443.arn
  certificate_arn = aws_acm_certificate.equtum_dev.arn
}

resource "aws_security_group" "dev_equtum_management_api_alb_sg" {
  name        = "dev-equtum-management-api-alb-sg"
  description = "Dev Equtum Management API ALB Security Group"
  vpc_id      = aws_vpc.dev_equtum_vpc.id

  ingress {
    from_port        = 80
    to_port          = 80
    protocol         = "tcp"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  ingress {
    from_port        = 443
    to_port          = 443
    protocol         = "tcp"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  ingress {
    from_port        = 8443
    to_port          = 8443
    protocol         = "tcp"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }
}
