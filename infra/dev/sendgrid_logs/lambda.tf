data "archive_file" "sendgrid_logs_lambda_zip" {
  type        = "zip"
  source_file = "${path.module}/logging.py"
  output_path = "${path.module}/logging.zip"
}

resource "aws_lambda_function" "sendgrid_logs_lambda" {
  function_name    = "sendgrid_logs_lambda"
  filename         = data.archive_file.sendgrid_logs_lambda_zip.output_path
  handler          = "logging.lambda_handler"
  runtime          = "python3.8"
  role             = aws_iam_role.sendgrid_logs_lambda_role.arn
  source_code_hash = data.archive_file.sendgrid_logs_lambda_zip.output_base64sha256

  environment {
    variables = {
      BUCKET_NAME = aws_s3_bucket.sendgrid_logs_bucket.bucket
    }
  }
}
