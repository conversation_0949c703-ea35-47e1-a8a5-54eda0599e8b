import json
import boto3
import os
from datetime import datetime

s3 = boto3.client("s3")
BUCKET_NAME = os.environ["BUCKET_NAME"]


def lambda_handler(event, context):
    body = event.get("body")
    if not body:
        return {"statusCode": 400, "body": "Bad Request: No body found"}

    try:
        records = json.loads(body)
    except json.JSONDecodeError:
        return {"statusCode": 400, "body": "Bad Request: Invalid JSON"}

    now = datetime.utcnow()
    key_prefix = now.strftime("year=%Y/month=%m/day=%d/")
    file_name = now.strftime("%Y%m%dT%H%M%S%f") + ".json"
    s3_key = key_prefix + file_name

    s3.put_object(
        Bucket=BUCKET_NAME,
        Key=s3_key,
        Body=json.dumps(records),
        ContentType="application/json",
    )

    return {"statusCode": 200, "body": "Success"}
