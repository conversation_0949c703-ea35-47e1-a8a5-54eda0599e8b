resource "aws_glue_catalog_database" "sendgrid_logs_db" {
  name = "sendgrid_logs_db"
}

resource "aws_glue_catalog_table" "sendgrid_logs_table" {
  name          = "sendgrid_logs_table"
  database_name = aws_glue_catalog_database.sendgrid_logs_db.name

  table_type = "EXTERNAL_TABLE"

  storage_descriptor {
    location      = "s3://${aws_s3_bucket.sendgrid_logs_bucket.bucket}/"
    input_format  = "org.apache.hadoop.mapred.TextInputFormat"
    output_format = "org.apache.hadoop.hive.ql.io.IgnoreKeyTextOutputFormat"
    compressed    = false

    columns {
      name = "email"
      type = "string"
    }

    columns {
      name = "event"
      type = "string"
    }

    columns {
      name = "timestamp"
      type = "bigint"
    }
  }

  partition_keys {
    name = "year"
    type = "string"
  }

  partition_keys {
    name = "month"
    type = "string"
  }

  partition_keys {
    name = "day"
    type = "string"
  }
}
