resource "aws_iam_role" "sendgrid_logs_lambda_role" {
  name = "sendgrid_logs_lambda_role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_iam_policy" "sendgrid_logs_lambda_policy" {
  name        = "sendgrid_logs_lambda_policy"
  description = "SendGrid Logs Lambda Function Policy"

  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": [
        "s3:PutObject",
        "s3:GetObject"
      ],
      "Effect": "Allow",
      "Resource": [
        "${aws_s3_bucket.sendgrid_logs_bucket.arn}/*"
      ]
    },
    {
      "Action": [
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ],
      "Effect": "Allow",
      "Resource": "arn:aws:logs:*:*:*"
    }
  ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "sendgrid_logs_lambda_policy_attachment" {
  role       = aws_iam_role.sendgrid_logs_lambda_role.name
  policy_arn = aws_iam_policy.sendgrid_logs_lambda_policy.arn
}
