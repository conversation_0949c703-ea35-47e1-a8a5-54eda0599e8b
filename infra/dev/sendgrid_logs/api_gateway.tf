resource "aws_api_gateway_rest_api" "sendgrid_logs_api" {
  name = "SendGridLogsAPI"
}

resource "aws_api_gateway_resource" "sendgrid_logs_resource" {
  rest_api_id = aws_api_gateway_rest_api.sendgrid_logs_api.id
  parent_id   = aws_api_gateway_rest_api.sendgrid_logs_api.root_resource_id
  path_part   = "sendgrid"
}

resource "aws_api_gateway_method" "sendgrid_logs_post" {
  rest_api_id   = aws_api_gateway_rest_api.sendgrid_logs_api.id
  resource_id   = aws_api_gateway_resource.sendgrid_logs_resource.id
  http_method   = "POST"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "sendgrid_logs_integration" {
  rest_api_id             = aws_api_gateway_rest_api.sendgrid_logs_api.id
  resource_id             = aws_api_gateway_resource.sendgrid_logs_resource.id
  http_method             = aws_api_gateway_method.sendgrid_logs_post.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = aws_lambda_function.sendgrid_logs_lambda.invoke_arn
}

resource "aws_lambda_permission" "sendgrid_logs_api_gateway_permission" {
  function_name = aws_lambda_function.sendgrid_logs_lambda.function_name
  action        = "lambda:InvokeFunction"
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_api_gateway_rest_api.sendgrid_logs_api.execution_arn}/*/*"
}

resource "aws_api_gateway_deployment" "sendgrid_logs_deployment" {
  depends_on = [aws_api_gateway_integration.sendgrid_logs_integration]

  rest_api_id = aws_api_gateway_rest_api.sendgrid_logs_api.id
  stage_name  = "prod"
}

output "api_gateway_endpoint" {
  value = "${aws_api_gateway_deployment.sendgrid_logs_deployment.invoke_url}/sendgrid"
}
