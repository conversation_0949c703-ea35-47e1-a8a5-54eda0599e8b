-include .env
export

# DB information
DB_USER := root
MYSQL_HOST ?= mysql
DB_HOST := $(MYSQL_HOST)
DB_PORT := 3306
MIGRATION_FILE_PATH := database/db/migrations

.PHONY: migrate reset-db docker-up-db docker-up-all

# Start MySQL only (for backward compatibility)
docker-up-db:
	docker compose -f docker/docker-compose.dev.yml up -d mysql

# Start both MySQL and Firebase emulator
docker-up-all:
	docker compose -f docker/docker-compose.dev.yml up -d mysql

migrate:
	migrate -path $(MIGRATION_FILE_PATH) -database "mysql://$(DB_USER)@tcp($(DB_HOST):$(DB_PORT))/equtum" up
	migrate -path $(MIGRATION_FILE_PATH) -database "mysql://$(DB_USER)@tcp($(DB_HOST):$(DB_PORT))/equtum_test" up

reset-db:
	mysql -u $(DB_USER) --host $(DB_HOST) --port $(DB_PORT) -e "DROP DATABASE IF EXISTS equtum; CREATE DATABASE equtum;"
	migrate -path $(MIGRATION_FILE_PATH) -database "mysql://$(DB_USER)@tcp($(DB_HOST):$(DB_PORT))/equtum" up
	mysql -u $(DB_USER) --host $(DB_HOST) --port $(DB_PORT) -e "DROP DATABASE IF EXISTS equtum_test; CREATE DATABASE equtum_test;"
	migrate -path $(MIGRATION_FILE_PATH) -database "mysql://$(DB_USER)@tcp($(DB_HOST):$(DB_PORT))/equtum_test" up
