{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "ES2022", "moduleResolution": "node", "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "outDir": "./dist", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "exactOptionalPropertyTypes": false, "types": ["node"], "paths": {"@/*": ["./src/*"], "@test/*": ["./test/*"]}}, "ts-node": {"require": ["tsconfig-paths/register"]}, "include": ["src/**/*", "test/**/*"], "exclude": ["node_modules", "./test/factories/fabbrica/*"]}