version: '3.8'
services:
  app:
    build:
      context: ..
      dockerfile: docker/batch/Dockerfile.dev
    volumes:
      - ../:/workspaces/equtum-batch
      - /workspaces/equtum-batch/node_modules
    working_dir: /workspaces/equtum-batch
    command: sleep infinity
    environment:
      - NODE_ENV=local
      - DATABASE_URL=mysql://root@mysql:3306/equtum
      - LOG_LEVEL=debug
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - equtum-batch-network

  mysql:
    image: mysql:9.1.0
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    environment:
      - MYSQL_ALLOW_EMPTY_PASSWORD=1
    ports:
      - '3307:3306'
    healthcheck:
      test: ['CMD', 'mysqladmin', 'ping', '-h', 'localhost']
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - equtum-batch-network

volumes:
  mysql_data:

networks:
  equtum-batch-network:
    driver: bridge
