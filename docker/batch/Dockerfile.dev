# Development Dockerfile for TypeScript + Prisma Batch Processing
FROM node:20-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    vim \
    nano \
    wget \
    unzip \
    python3 \
    python3-pip \
    ca-certificates \
    gnupg \
    default-mysql-client \
    && rm -rf /var/lib/apt/lists/*

# Install golang-migrate (v4.18.3, linux-arm64)
RUN wget https://github.com/golang-migrate/migrate/releases/download/v4.18.3/migrate.linux-arm64.deb \
    && dpkg -i migrate.linux-arm64.deb \
    && rm migrate.linux-arm64.deb

# Install AWS CLI
RUN curl "https://awscli.amazonaws.com/awscli-exe-linux-aarch64.zip" -o "awscliv2.zip" \
    && unzip awscliv2.zip \
    && ./aws/install \
    && rm -rf aws awscliv2.zip

# Install global npm packages for development
RUN npm install -g \
    typescript \
    ts-node \
    nodemon \
    prisma \
    @biomejs/biome

# Create app directory
WORKDIR /workspace

# Copy package files for dependency installation
COPY package*.json ./

# Install dependencies
RUN npm install

# Expose common ports
EXPOSE 3000 5432 8080

# Set default command
CMD ["bash"] 